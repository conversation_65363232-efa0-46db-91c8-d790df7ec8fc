<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$plan_id = (int)($_GET['id'] ?? 0);

if (!$plan_id) {
    echo json_encode(['success' => false, 'message' => 'معرف خطة التقسيط مطلوب']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // جلب بيانات خطة التقسيط
    $stmt = $pdo->prepare("
        SELECT ip.*, 
               u.name as user_name, u.phone as user_phone,
               pp.name as plan_name,
               s.name as service_name
        FROM installment_plans ip
        JOIN users u ON ip.user_id = u.id
        JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
        JOIN services s ON pp.service_id = s.id
        WHERE ip.id = ?
    ");
    $stmt->execute([$plan_id]);
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$plan) {
        echo json_encode(['success' => false, 'message' => 'خطة التقسيط غير موجودة']);
        exit();
    }
    
    // جلب المدفوعات مع تحديث الحالة للمتأخرة
    $stmt = $pdo->prepare("
        UPDATE payments 
        SET status = 'overdue' 
        WHERE installment_plan_id = ? 
        AND status = 'pending' 
        AND due_date < CURDATE()
    ");
    $stmt->execute([$plan_id]);
    
    // جلب المدفوعات
    $stmt = $pdo->prepare("
        SELECT * FROM payments 
        WHERE installment_plan_id = ? 
        ORDER BY installment_number ASC
    ");
    $stmt->execute([$plan_id]);
    $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'plan' => $plan,
        'payments' => $payments
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>

