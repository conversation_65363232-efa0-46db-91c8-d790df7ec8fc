<?php
// تعيين رمز الاستجابة 404
http_response_code(404);

$page_title = 'الصفحة غير موجودة - 404';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - FutureWay</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .error-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            animation: gradient 3s ease infinite;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .error-number {
            font-size: 8rem;
            font-weight: 700;
            color: #667eea;
            margin: 0;
            line-height: 1;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .error-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #333;
            margin: 20px 0;
        }

        .error-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .error-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-secondary {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 12px 25px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 50px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .suggestions {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }

        .suggestions h5 {
            color: #333;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .suggestion-links {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
        }

        .suggestion-link {
            background: #f8f9fa;
            color: #667eea;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .suggestion-link:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .search-box {
            margin: 30px 0;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #667eea;
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: #5a6fd8;
            transform: translateY(-50%) scale(1.1);
        }

        @media (max-width: 768px) {
            .error-container {
                padding: 40px 20px;
                margin: 20px;
            }

            .error-number {
                font-size: 6rem;
            }

            .error-title {
                font-size: 2rem;
            }

            .error-subtitle {
                font-size: 1rem;
            }

            .suggestion-links {
                flex-direction: column;
                align-items: center;
            }

            .btn-home, .btn-secondary {
                display: block;
                margin: 10px 0;
                text-align: center;
            }
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 50%;
            animation: float-particles 6s infinite linear;
        }

        @keyframes float-particles {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="particles" id="particles"></div>

    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <h1 class="error-number">404</h1>
        <h2 class="error-title">الصفحة غير موجودة</h2>
        <p class="error-subtitle">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
            <br>
            يمكنك العودة للصفحة الرئيسية أو البحث عما تريد.
        </p>

        <!-- مربع البحث -->
        <div class="search-box">
            <form action="search.php" method="GET">
                <input type="text" name="q" class="search-input" placeholder="ابحث عن الخدمات أو المحتوى...">
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>

        <!-- أزرار التنقل -->
        <div class="mb-4">
            <a href="index.php" class="btn-home">
                <i class="fas fa-home me-2"></i>
                العودة للرئيسية
            </a>
        </div>

        <div class="mb-3">
            <a href="javascript:history.back()" class="btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                الصفحة السابقة
            </a>
            <a href="services.php" class="btn-secondary">
                <i class="fas fa-box me-2"></i>
                تصفح الخدمات
            </a>
        </div>

        <!-- اقتراحات -->
        <div class="suggestions">
            <h5>
                <i class="fas fa-lightbulb me-2"></i>
                صفحات قد تهمك
            </h5>
            <div class="suggestion-links">
                <a href="index.php" class="suggestion-link">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
                <a href="services.php" class="suggestion-link">
                    <i class="fas fa-box me-1"></i>
                    الخدمات
                </a>
                <a href="about.php" class="suggestion-link">
                    <i class="fas fa-info-circle me-1"></i>
                    من نحن
                </a>
                <a href="contact.php" class="suggestion-link">
                    <i class="fas fa-phone me-1"></i>
                    اتصل بنا
                </a>
                <a href="login.php" class="suggestion-link">
                    <i class="fas fa-sign-in-alt me-1"></i>
                    تسجيل الدخول
                </a>
                <a href="register.php" class="suggestion-link">
                    <i class="fas fa-user-plus me-1"></i>
                    إنشاء حساب
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إنشاء جزيئات متحركة في الخلفية
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 20;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // حجم عشوائي
                const size = Math.random() * 10 + 5;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                
                // موضع أفقي عشوائي
                particle.style.left = Math.random() * 100 + '%';
                
                // تأخير عشوائي
                particle.style.animationDelay = Math.random() * 6 + 's';
                
                // مدة عشوائية
                particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
                
                particlesContainer.appendChild(particle);
            }
        }

        // تشغيل الجزيئات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            
            // تسجيل الخطأ في وحدة التحكم للمطورين
            console.log('404 Error: Page not found - ' + window.location.href);
            
            // إرسال إحصائية الخطأ (اختياري)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'page_not_found', {
                    'page_location': window.location.href,
                    'page_title': document.title
                });
            }
        });

        // تحسين تجربة البحث
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.closest('form').submit();
            }
        });

        // تأثير hover للأزرار
        document.querySelectorAll('.btn-home, .btn-secondary, .suggestion-link').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
