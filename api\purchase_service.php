<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'يجب تسجيل الدخول أولاً'
    ]);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['plan_id']) || !isset($input['payment_type'])) {
        throw new Exception('بيانات غير مكتملة');
    }
    
    $plan_id = (int)$input['plan_id'];
    $payment_type = $input['payment_type'];
    $user_id = $_SESSION['user_id'];
    
    if (!in_array($payment_type, ['cash', 'installment'])) {
        throw new Exception('نوع الدفع غير صحيح');
    }
    
    $db = new Database();
    $pdo = $db->getConnection();
    
    // بدء المعاملة
    $pdo->beginTransaction();
    
    // جلب تفاصيل الخطة
    $stmt = $pdo->prepare("
        SELECT pp.*, s.name as service_name 
        FROM pricing_plans pp
        JOIN services s ON pp.service_id = s.id
        WHERE pp.id = ? AND pp.is_active = TRUE AND s.is_active = TRUE
    ");
    $stmt->execute([$plan_id]);
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$plan) {
        throw new Exception('الخطة غير موجودة');
    }
    
    // التحقق من إمكانية التقسيط
    if ($payment_type === 'installment' && !$plan['allow_installment']) {
        throw new Exception('التقسيط غير متاح لهذه الخطة');
    }
    
    $installment_plan_id = null;
    
    // إنشاء خطة التقسيط إذا كان الدفع بالتقسيط
    if ($payment_type === 'installment') {
        // حساب إجمالي المبلغ مع الفائدة
        $base_price = $plan['price'];
        $total_interest = 0;
        if ($plan['interest_type'] === 'fixed') {
            $total_interest = $plan['interest_value'];
        } else {
            $total_interest = ($base_price * $plan['interest_value']) / 100;
        }
        $total_amount = $base_price + $total_interest;

        $down_payment = $plan['down_payment'] ?? 0;
        $remaining_amount = $total_amount - $down_payment;
        $monthly_payment = $plan['installment_amount'] ?? ($remaining_amount / $plan['installments_count']);

        // إدراج خطة التقسيط
        $stmt = $pdo->prepare("
            INSERT INTO installment_plans (
                user_id, pricing_plan_id, total_amount, down_payment,
                remaining_amount, installment_amount, installments_count
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $user_id, $plan_id, $total_amount, $down_payment,
            $remaining_amount, $monthly_payment, $plan['installments_count']
        ]);

        $installment_plan_id = $pdo->lastInsertId();

        // إنشاء دفعات الأقساط
        for ($i = 1; $i <= $plan['installments_count']; $i++) {
            $due_date = date('Y-m-d', strtotime("+$i month"));
            
            $stmt = $pdo->prepare("
                INSERT INTO payments (
                    installment_plan_id, installment_number, amount, due_date
                ) VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$installment_plan_id, $i, $monthly_payment, $due_date]);
        }
    }
    
    // إدراج المشترى
    $stmt = $pdo->prepare("
        INSERT INTO purchases (
            user_id, pricing_plan_id, service_id, total_amount,
            payment_method, status, purchase_date
        ) VALUES (?, ?, ?, ?, ?, 'active', CURDATE())
    ");
    $stmt->execute([
        $user_id, $plan_id, $plan['service_id'], $total_amount, $payment_type
    ]);
    
    // تأكيد المعاملة
    $pdo->commit();
    
    // إرسال رسالة واتساب
    try {
        $stmt = $pdo->prepare("SELECT phone FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user_phone = $stmt->fetchColumn();
        
        $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'wawp_token'");
        $stmt->execute();
        $wawp_token = $stmt->fetchColumn();
        
        if ($wawp_token && $user_phone) {
            if ($payment_type === 'installment') {
                $message = str_replace(
                    '{details}',
                    "الخدمة: {$plan['service_name']}\nالخطة: {$plan['name']}\nالمقدم: " . formatCurrency($plan['down_payment'] ?? 0) . "\nعدد الأقساط: {$plan['installments_count']}",
                    INSTALLMENT_CREATED_MESSAGE
                );
            } else {
                $message = PURCHASE_SUCCESS_MESSAGE;
            }
            
            $wawp_data = [
                'token' => $wawp_token,
                'phone' => $user_phone,
                'message' => $message
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://wawp.net/api/v1/send');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($wawp_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_exec($ch);
            curl_close($ch);
        }
    } catch (Exception $e) {
        // تجاهل أخطاء إرسال الرسائل
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'تم إتمام عملية الشراء بنجاح'
    ]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

