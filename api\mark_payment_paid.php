<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$payment_id = (int)($input['payment_id'] ?? 0);

if (!$payment_id) {
    echo json_encode(['success' => false, 'message' => 'معرف القسط مطلوب']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // التحقق من وجود القسط
    $stmt = $pdo->prepare("SELECT * FROM payments WHERE id = ? AND status IN ('pending', 'overdue')");
    $stmt->execute([$payment_id]);
    $payment = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$payment) {
        echo json_encode(['success' => false, 'message' => 'القسط غير موجود أو مدفوع بالفعل']);
        exit();
    }
    
    // تحديث حالة القسط
    $stmt = $pdo->prepare("
        UPDATE payments
        SET status = 'paid', paid_at = NOW(), updated_at = NOW()
        WHERE id = ?
    ");
    
    if ($stmt->execute([$payment_id])) {
        // التحقق من اكتمال جميع الأقساط لتحديث حالة خطة التقسيط
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total, 
                   COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid
            FROM payments 
            WHERE installment_plan_id = ?
        ");
        $stmt->execute([$payment['installment_plan_id']]);
        $payment_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($payment_stats['total'] == $payment_stats['paid']) {
            // تحديث حالة خطة التقسيط إلى مكتملة
            $stmt = $pdo->prepare("
                UPDATE installment_plans 
                SET status = 'completed', updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$payment['installment_plan_id']]);
        }
        
        echo json_encode(['success' => true, 'message' => 'تم تسجيل الدفع بنجاح']);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في تسجيل الدفع']);
    }
    
} catch (Exception $e) {
    error_log("Error in mark_payment_paid.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام: ' . $e->getMessage()]);
}
?>

