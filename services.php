<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/db_config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/functions.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();

    // التحقق من وجود حقل is_active في جدول services
    $stmt = $pdo->prepare("SHOW COLUMNS FROM services LIKE 'is_active'");
    $stmt->execute();
    $is_active_exists = $stmt->fetch();

    // جلب الخدمات (مع أو بدون شرط is_active حسب وجود الحقل)
    if ($is_active_exists) {
        $stmt = $pdo->prepare("SELECT * FROM services WHERE is_active = TRUE ORDER BY created_at DESC");
    } else {
        $stmt = $pdo->prepare("SELECT * FROM services ORDER BY created_at DESC");
    }
    $stmt->execute();
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب تفاصيل خدمة محددة إذا تم تمرير معرف
    $selected_service = null;
    $pricing_plans = [];
    if (isset($_GET['service']) && is_numeric($_GET['service'])) {
        $service_id = (int)$_GET['service'];

        if ($is_active_exists) {
            $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ? AND is_active = TRUE");
        } else {
            $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ?");
        }
        $stmt->execute([$service_id]);
        $selected_service = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($selected_service) {
            // التحقق من وجود حقل is_active في جدول pricing_plans
            $stmt = $pdo->prepare("SHOW COLUMNS FROM pricing_plans LIKE 'is_active'");
            $stmt->execute();
            $pricing_is_active_exists = $stmt->fetch();

            // جلب خطط الأسعار للخدمة
            if ($pricing_is_active_exists) {
                $stmt = $pdo->prepare("
                    SELECT * FROM pricing_plans
                    WHERE service_id = ? AND is_active = TRUE
                    ORDER BY price ASC
                ");
            } else {
                $stmt = $pdo->prepare("
                    SELECT * FROM pricing_plans
                    WHERE service_id = ?
                    ORDER BY price ASC
                ");
            }
            $stmt->execute([$service_id]);
            $pricing_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }

} catch (Exception $e) {
    $services = [];
    $selected_service = null;
    $pricing_plans = [];
    // إضافة رسالة خطأ للتشخيص
    error_log("خطأ في جلب الخدمات: " . $e->getMessage());
}

$page_title = 'خدماتنا';
include __DIR__ . '/includes/header.php';
?>

<div class="services-page">
    <!-- Page Header -->
    <section class="hero-section" style="padding: 3rem 0;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="hero-content text-center">
                        <h1 class="hero-title" style="font-size: 3rem;">خدماتنا المميزة</h1>
                        <p class="hero-subtitle">اكتشف مجموعة متنوعة من الخدمات عالية الجودة مع أنظمة تقسيط مرنة تناسب احتياجاتك</p>
                        <?php if (!isLoggedIn()): ?>
                        <div class="hero-buttons">
                            <a href="register.php" class="btn btn-light btn-lg">
                                <i class="fas fa-user-plus me-2"></i>
                                ابدأ الآن
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Grid -->
    <section class="services-grid py-5">
        <div class="container">
            <?php if (empty($services)): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="empty-state text-center py-5">
                            <div class="empty-icon">
                                <i class="fas fa-box-open fa-4x text-muted"></i>
                            </div>
                            <h3 class="empty-title">لا توجد خدمات متاحة حالياً</h3>
                            <p class="empty-description">سيتم إضافة الخدمات قريباً. تابعنا للحصول على آخر التحديثات.</p>
                            <a href="index.php" class="btn btn-primary">العودة للرئيسية</a>

                            <?php if (isset($_GET['debug']) && $_GET['debug'] == '1'): ?>
                                <div class="mt-4 p-3 border rounded" style="background-color: #1e1e1e; border-color: #333333; color: #e0e0e0;">
                                    <h6>معلومات التشخيص:</h6>
                                    <p><strong>عدد الخدمات المجلبة:</strong> <?= count($services) ?></p>
                                    <?php
                                    try {
                                        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM services");
                                        $stmt->execute();
                                        $total_services = $stmt->fetch(PDO::FETCH_ASSOC);
                                        echo "<p><strong>إجمالي الخدمات في قاعدة البيانات:</strong> " . $total_services['total'] . "</p>";

                                        $stmt = $pdo->prepare("SHOW COLUMNS FROM services LIKE 'is_active'");
                                        $stmt->execute();
                                        $is_active_exists = $stmt->fetch();
                                        echo "<p><strong>حقل is_active موجود:</strong> " . ($is_active_exists ? 'نعم' : 'لا') . "</p>";

                                        if ($is_active_exists) {
                                            $stmt = $pdo->prepare("SELECT COUNT(*) as active_count FROM services WHERE is_active = TRUE");
                                            $stmt->execute();
                                            $active_services = $stmt->fetch(PDO::FETCH_ASSOC);
                                            echo "<p><strong>عدد الخدمات النشطة:</strong> " . $active_services['active_count'] . "</p>";
                                        }
                                    } catch (Exception $e) {
                                        echo "<p><strong>خطأ في التشخيص:</strong> " . $e->getMessage() . "</p>";
                                    }
                                    ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($services as $service): ?>
                        <?php
                        // جلب نطاق الأسعار للخدمة
                        $stmt = $pdo->prepare("
                            SELECT MIN(price) as min_price, MAX(price) as max_price, COUNT(*) as plans_count
                            FROM pricing_plans 
                            WHERE service_id = ? AND is_active = TRUE
                        ");
                        $stmt->execute([$service['id']]);
                        $price_info = $stmt->fetch(PDO::FETCH_ASSOC);
                        ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="service-card">
                                <div class="service-image">
                                    <?php if ($service['image']): ?>
                                        <img src="assets/uploads/<?= htmlspecialchars($service['image']) ?>" 
                                             alt="<?= htmlspecialchars($service['name']) ?>">
                                    <?php else: ?>
                                        <div class="service-placeholder">
                                            <i class="fas fa-cogs fa-3x"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div class="service-overlay">
                                        <button class="btn btn-light btn-sm view-details" 
                                                data-service-id="<?= $service['id'] ?>">
                                            <i class="fas fa-eye me-2"></i>
                                            عرض التفاصيل
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="service-content">
                                    <h5 class="service-title"><?= htmlspecialchars($service['name']) ?></h5>
                                    <p class="service-description">
                                        <?= htmlspecialchars(substr($service['description'], 0, 120)) ?>
                                        <?= strlen($service['description']) > 120 ? '...' : '' ?>
                                    </p>
                                    
                                    <?php if ($price_info && $price_info['min_price']): ?>
                                        <div class="service-price">
                                            <?php if ($price_info['min_price'] == $price_info['max_price']): ?>
                                                <span class="price"><?= formatCurrency($price_info['min_price']) ?></span>
                                            <?php else: ?>
                                                <span class="price">
                                                    من <?= formatCurrency($price_info['min_price']) ?> 
                                                    إلى <?= formatCurrency($price_info['max_price']) ?>
                                                </span>
                                            <?php endif; ?>
                                            <small class="plans-count"><?= $price_info['plans_count'] ?> خطة متاحة</small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="service-actions">
                                        <button class="btn btn-primary view-details" 
                                                data-service-id="<?= $service['id'] ?>">
                                            <i class="fas fa-shopping-cart me-2"></i>
                                            اختر الخطة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<!-- Service Details Modal -->
<div class="modal fade" id="serviceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="serviceModalTitle">تفاصيل الخدمة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="serviceModalBody">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Purchase Modal -->
<div class="modal fade" id="purchaseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الشراء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="purchaseModalBody">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0 60px;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,100 1000,0"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.service-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    position: relative;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.service-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover .service-overlay {
    opacity: 1;
}

.service-content {
    padding: 1.5rem;
}

.service-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
}

.service-description {
    color: #6c757d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-price {
    margin-bottom: 1.5rem;
    text-align: center;
}

.service-price .price {
    font-size: 1.25rem;
    font-weight: 600;
    color: #667eea;
    display: block;
}

.service-price .plans-count {
    color: #6c757d;
    font-size: 0.9rem;
}

.service-actions {
    text-align: center;
}

.service-actions .btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
}

.empty-state {
    padding: 4rem 2rem;
}

.empty-icon {
    margin-bottom: 2rem;
}

.empty-title {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.empty-description {
    color: #6c757d;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

.pricing-plan {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.pricing-plan:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.pricing-plan.featured {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.pricing-plan.featured::before {
    content: 'الأكثر شعبية';
    position: absolute;
    top: -10px;
    right: 20px;
    background: #667eea;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.plan-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.plan-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.plan-price {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
}

.plan-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    align-items: center;
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features li i {
    color: #28a745;
    margin-left: 10px;
}

.plan-actions {
    text-align: center;
}

.installment-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
}

.installment-info h6 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.installment-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.installment-details span {
    font-size: 0.9rem;
    color: #6c757d;
}

.installment-details strong {
    color: #2c3e50;
}

@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .service-image {
        height: 200px;
    }
    
    .modal-dialog {
        margin: 1rem;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.all.min.js"></script>
<script src="assets/js/payment-system.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add global focus management for all modals
    document.addEventListener('hide.bs.modal', function(event) {
        const modal = event.target;
        const focusedElement = modal.querySelector(':focus');
        if (focusedElement) {
            focusedElement.blur();
        }
    });

    // عرض تفاصيل الخدمة
    document.querySelectorAll('.view-details').forEach(button => {
        button.addEventListener('click', function() {
            const serviceId = this.getAttribute('data-service-id');
            loadServiceDetails(serviceId);
        });
    });
    
    function loadServiceDetails(serviceId) {
        const modal = new bootstrap.Modal(document.getElementById('serviceModal'));
        const modalBody = document.getElementById('serviceModalBody');
        
        // إظهار loader
        modalBody.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
            </div>
        `;
        
        modal.show();
        
        // جلب تفاصيل الخدمة
        fetch(`api/get_service_details_public.php?id=${serviceId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayServiceDetails(data.service, data.pricing_plans);
                } else {
                    modalBody.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            حدث خطأ في تحميل تفاصيل الخدمة
                        </div>
                    `;
                }
            })
            .catch(error => {
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        حدث خطأ في الاتصال بالخادم
                    </div>
                `;
            });
    }
    
    function displayServiceDetails(service, pricingPlans) {
        const modalTitle = document.getElementById('serviceModalTitle');
        const modalBody = document.getElementById('serviceModalBody');

        // حفظ بيانات الخطط للاستخدام في الشراء
        window.currentPlans = pricingPlans;

        modalTitle.textContent = service.name;
        
        let plansHtml = '';
        pricingPlans.forEach((plan, index) => {
            // إضافة اسم الخدمة للخطة
            plan.service_name = service.name;

            const featuresArray = plan.features ? plan.features.split('\n') : [];
            const featuresHtml = featuresArray.map(feature =>
                `<li><i class="fas fa-check"></i> ${feature}</li>`
            ).join('');
            
            // حساب قيم التقسيط للعرض في الكارد
            let cardDownPayment = parseFloat(plan.down_payment) || 0;
            let cardInstallmentAmount = parseFloat(plan.installment_amount) || 0;
            let cardInstallmentsCount = parseInt(plan.installments_count) || 6;

            // إذا كان المقدم صفر، احسبه كـ 50% من السعر
            if (cardDownPayment === 0) {
                cardDownPayment = parseFloat(plan.price) * 0.5;
            }

            // إذا كان مبلغ القسط صفر، احسبه
            if (cardInstallmentAmount === 0) {
                const remainingAmount = parseFloat(plan.price) - cardDownPayment;
                cardInstallmentAmount = remainingAmount / cardInstallmentsCount;
            }

            const installmentInfo = plan.allow_installment ? `
                <div class="installment-info">
                    <h6><i class="fas fa-credit-card me-2"></i>معلومات التقسيط</h6>
                    <div class="installment-details">
                        <span>المقدم:</span>
                        <strong>${formatCurrency(cardDownPayment)}</strong>
                    </div>
                    <div class="installment-details">
                        <span>عدد الأقساط:</span>
                        <strong>${cardInstallmentsCount} قسط</strong>
                    </div>
                    <div class="installment-details">
                        <span>القسط الشهري:</span>
                        <strong>${formatCurrency(cardInstallmentAmount)}</strong>
                    </div>
                </div>
            ` : '';
            
            plansHtml += `
                <div class="pricing-plan ${index === 0 ? 'featured' : ''}">
                    <div class="plan-header">
                        <div class="plan-name">${plan.name}</div>
                        <div class="plan-price">${formatCurrency(plan.price)}</div>
                    </div>
                    <ul class="plan-features">
                        ${featuresHtml}
                    </ul>
                    ${installmentInfo}
                    <div class="plan-actions">
                        <button class="btn btn-primary me-2" onclick="purchaseService(${plan.id}, 'cash')">
                            <i class="fas fa-shopping-cart me-2"></i>شراء نقداً
                        </button>
                        ${(plan.allow_installment && parseInt(plan.installments_count) > 0) ? `
                            <button class="btn btn-outline-primary" onclick="purchaseService(${plan.id}, 'installment')">
                                <i class="fas fa-credit-card me-2"></i>شراء بالتقسيط
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        });
        
        modalBody.innerHTML = `
            <div class="service-details">
                ${service.image ? `
                    <div class="service-image-large mb-3">
                        <img src="assets/uploads/${service.image}" alt="${service.name}" 
                             class="img-fluid rounded" style="width: 100%; max-height: 300px; object-fit: cover;">
                    </div>
                ` : ''}
                <div class="service-description mb-4">
                    <h6>وصف الخدمة</h6>
                    <p>${service.description}</p>
                </div>
                <div class="pricing-plans">
                    <h6 class="mb-3">خطط الأسعار المتاحة</h6>
                    ${plansHtml || '<p class="text-muted">لا توجد خطط أسعار متاحة حالياً</p>'}
                </div>
            </div>
        `;
    }
    
    // دالة الشراء
    window.purchaseService = function(planId, paymentType) {
        <?php if (!isLoggedIn()): ?>
            Swal.fire({
                title: 'تسجيل الدخول مطلوب',
                text: 'يجب تسجيل الدخول أولاً لإتمام عملية الشراء',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'تسجيل الدخول',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'login.php?redirect=' + encodeURIComponent(window.location.href);
                }
            });
            return;
        <?php endif; ?>
        
        // إظهار نافذة تأكيد الشراء
        showPurchaseConfirmation(planId, paymentType);
    };
    
    function showPurchaseConfirmation(planId, paymentType) {
        // جلب تفاصيل الخطة
        fetch(`api/get_plan_details.php?id=${planId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayPurchaseConfirmation(data.plan, paymentType);
                }
            });
    }
    
    function displayPurchaseConfirmation(plan, paymentType) {
        const modal = new bootstrap.Modal(document.getElementById('purchaseModal'));
        const modalBody = document.getElementById('purchaseModalBody');

        // حساب قيم التقسيط إذا لم تكن محددة
        let downPayment = parseFloat(plan.down_payment) || 0;
        let installmentAmount = parseFloat(plan.installment_amount) || 0;
        let installmentsCount = parseInt(plan.installments_count) || 6;

        // إذا كان المقدم صفر، احسبه كـ 50% من السعر
        if (downPayment === 0) {
            downPayment = parseFloat(plan.price) * 0.5;
        }

        // إذا كان مبلغ القسط صفر، احسبه
        if (installmentAmount === 0) {
            const remainingAmount = parseFloat(plan.price) - downPayment;
            installmentAmount = remainingAmount / installmentsCount;
        }

        const installmentDetails = paymentType === 'installment' ? `
            <div class="installment-summary">
                <h6>تفاصيل التقسيط</h6>
                <div class="row">
                    <div class="col-6">
                        <strong>المقدم:</strong><br>
                        ${formatCurrency(downPayment)}
                    </div>
                    <div class="col-6">
                        <strong>عدد الأقساط:</strong><br>
                        ${installmentsCount} قسط
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>القسط الشهري:</strong><br>
                        ${formatCurrency(installmentAmount)}
                    </div>
                    <div class="col-6">
                        <strong>تاريخ أول قسط:</strong><br>
                        ${getNextMonthDate()}
                    </div>
                </div>
            </div>
        ` : '';
        
        modalBody.innerHTML = `
            <div class="purchase-summary">
                <h6>ملخص الشراء</h6>
                <div class="plan-info mb-3">
                    <strong>الخدمة:</strong> ${plan.service_name}<br>
                    <strong>الخطة:</strong> ${plan.name}<br>
                    <strong>السعر:</strong> ${formatCurrency(plan.price)}<br>
                    <strong>نوع الدفع:</strong> ${paymentType === 'cash' ? 'نقداً' : 'تقسيط'}
                </div>
                ${installmentDetails}
                <div class="purchase-actions mt-4">
                    <button class="btn btn-success" onclick="confirmPurchase(${plan.id}, '${paymentType}')">
                        <i class="fas fa-check me-2"></i>تأكيد الشراء
                    </button>
                    <button class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                </div>
            </div>
        `;
        
        modal.show();
    }
    
    window.confirmPurchase = function(planId, paymentType) {
        // إغلاق نافذة تأكيد الشراء
        const purchaseModalElement = document.getElementById('purchaseModal');
        bootstrap.Modal.getInstance(purchaseModalElement).hide();

        // جلب تفاصيل الخطة لحساب المبلغ
        const plan = window.currentPlans.find(p => p.id == planId);
        if (!plan) {
            Swal.fire({
                title: 'خطأ',
                text: 'لم يتم العثور على تفاصيل الخطة',
                icon: 'error'
            });
            return;
        }

        // حساب المبلغ المطلوب دفعه
        let amount = 0;
        let payment_type_for_gateway = '';

        if (paymentType === 'cash') {
            // دفع كامل
            amount = parseFloat(plan.price);
            payment_type_for_gateway = 'service_purchase';
        } else if (paymentType === 'installment') {
            // دفع المقدم فقط
            amount = parseFloat(plan.down_payment) || 0;
            payment_type_for_gateway = 'down_payment';
        }

        // حفظ بيانات الشراء لاستخدامها بعد الدفع
        window.pendingPurchase = {
            plan_id: planId,
            payment_type: paymentType,
            plan: plan
        };

        // فتح نافذة بوابات الدفع
        if (window.PaymentSystem) {
            window.PaymentSystem.openModal(amount, payment_type_for_gateway, null, null, planId);
        } else if (typeof window.openPaymentModal === 'function') {
            window.openPaymentModal(amount, payment_type_for_gateway, null, null, planId);
        } else {
            console.error('Payment system not loaded');
            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ في تحميل نظام الدفع',
                icon: 'error'
            });
        }
        
        // إغلاق النافذة المنبثقة (already closed above, no need to close again)
    };
    
    function formatCurrency(amount) {
        return new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: 'EGP'
        }).format(amount);
    }
    
    function getNextMonthDate() {
        const date = new Date();
        date.setMonth(date.getMonth() + 1);
        return date.toLocaleDateString('ar-EG');
    }
});
</script>

<?php
// إضافة مكون بوابات الدفع
include __DIR__ . '/includes/payment_gateways_modal.php';
include __DIR__ . '/includes/footer.php';
?>

