<?php
// تعيين التايم زون للقاهرة
date_default_timezone_set('Africa/Cairo');

// إعدادات عامة للموقع
define('SITE_NAME', 'FutureWay');
define('SITE_URL', 'http://localhost');
define('SITE_DESCRIPTION', 'خدمات متابعة وأنظمة التقسيط');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('OTP_EXPIRY', 300); // 5 دقائق
define('MAX_LOGIN_ATTEMPTS', 5);

// إعدادات WAWP
define('WAWP_API_URL', 'https://wawp.net/wp-json/awp/v1/');
define('WAWP_TOKEN', ''); // سيتم تعيينه من صفحة الإعدادات

// إعدادات الرسائل
define('OTP_MESSAGE_TEMPLATE', 'رمز التحقق الخاص بك في FutureWay هو: {code}');
define('WELCOME_MESSAGE_TEMPLATE', 'مرحباً بك في FutureWay! تم إنشاء حسابك بنجاح.');
define('INSTALLMENT_CREATED_MESSAGE', 'تم إنشاء خطة التقسيط الخاصة بك بنجاح. التفاصيل: {details}');
define('PURCHASE_SUCCESS_MESSAGE', 'تم شراء الخدمة بنجاح. شكراً لثقتك في FutureWay.');
define('INSTALLMENT_COMPLETED_MESSAGE', 'تهانينا! تم إنهاء خطة التقسيط الخاصة بك بنجاح.');

// إعدادات الملفات
define('UPLOAD_PATH', __DIR__ . '/../assets/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// إعدادات التقسيط
define('DEFAULT_INTEREST_RATE', 0.05); // 5%
define('MIN_DOWN_PAYMENT_PERCENTAGE', 0.20); // 20%
define('MAX_INSTALLMENTS', 24);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// وظائف مساعدة
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['is_admin']) && ($_SESSION['is_admin'] === true || $_SESSION['is_admin'] === 1 || $_SESSION['is_admin'] === '1');
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

function requireAdmin() {
    if (!isAdmin()) {
        header('Location: login.php');
        exit();
    }
}

function cleanPhone($phone) {
    // إزالة جميع الرموز والمسافات وعلامة +
    $phone = preg_replace('/[^0-9]/', '', $phone);

    // إزالة الأصفار البادئة إذا وجدت
    $phone = ltrim($phone, '0');

    // التعامل مع الأرقام المصرية
    if (strlen($phone) === 10 && substr($phone, 0, 1) === '1') {
        // رقم مصري بدون رمز الدولة (مثل: 1050073086)
        $phone = '20' . $phone;
    } elseif (strlen($phone) === 11 && substr($phone, 0, 2) === '01') {
        // رقم مصري بصفر بادئ (مثل: 01050073086)
        $phone = '20' . substr($phone, 1);
    } elseif (strlen($phone) === 13 && substr($phone, 0, 3) === '200') {
        // رقم مصري بصفر إضافي (مثل: 2001050073086)
        $phone = '20' . substr($phone, 3);
    } elseif (strlen($phone) === 12 && substr($phone, 0, 2) === '20') {
        // رقم مصري صحيح (مثل: 201050073086)
        // لا نحتاج لتغيير شيء
    } elseif (strlen($phone) === 11 && substr($phone, 0, 2) !== '20') {
        // رقم مصري بدون رمز الدولة (مثل: 11050073086)
        $phone = '20' . substr($phone, 1);
    }

    // التأكد من أن الرقم يبدأ بـ 20 وطوله 13 رقم
    if (substr($phone, 0, 2) !== '20') {
        $phone = '20' . $phone;
    }

    // قطع الرقم إذا كان أطول من المطلوب
    if (strlen($phone) > 13) {
        $phone = substr($phone, 0, 13);
    }

    return $phone;
}

function formatCurrency($amount) {
    return number_format($amount, 2) . ' جنيه';
}

function generateOTP() {
    return str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
}

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// تحميل إعدادات قاعدة البيانات
require_once __DIR__ . '/database.php';
?>

