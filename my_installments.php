<?php
session_start();
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$page_title = 'أقساطي';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // جلب خطط التقسيط الخاصة بالمستخدم
    $stmt = $pdo->prepare("
        SELECT ip.*, pp.name as plan_name, s.name as service_name,
               (SELECT COUNT(*) FROM payments WHERE installment_plan_id = ip.id AND status = 'paid') as paid_count,
               (SELECT COUNT(*) FROM payments WHERE installment_plan_id = ip.id) as total_count,
               (SELECT COUNT(*) FROM payments WHERE installment_plan_id = ip.id AND status = 'overdue') as overdue_count
        FROM installment_plans ip
        JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
        JOIN services s ON pp.service_id = s.id
        WHERE ip.user_id = ?
        ORDER BY ip.created_at DESC
    ");
    $stmt->execute([$user_id]);
    $installment_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $installment_plans = [];
    error_log("خطأ في جلب أقساط المستخدم: " . $e->getMessage());
}

include __DIR__ . '/includes/header.php';
?>

<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-credit-card me-2 text-primary"></i>
                    أقساطي
                </h1>
                <a href="services.php" class="btn btn-outline-primary">
                    <i class="fas fa-plus me-2"></i>
                    تصفح الخدمات
                </a>
            </div>

            <?php if (empty($installment_plans)): ?>
                <!-- لا توجد أقساط -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-credit-card fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted mb-3">لا توجد خطط تقسيط</h4>
                    <p class="text-muted mb-4">لم تقم بشراء أي خدمة بنظام التقسيط بعد</p>
                    <a href="services.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-cart me-2"></i>
                        تصفح الخدمات
                    </a>
                </div>
            <?php else: ?>
                <!-- عرض خطط التقسيط -->
                <div class="row">
                    <?php foreach ($installment_plans as $plan): ?>
                        <?php
                        // حساب المبلغ المدفوع مع المقدم
                        $paid_amount = $plan['down_payment'] + ($plan['paid_count'] * $plan['installment_amount']);
                        $remaining_amount = $plan['total_amount'] - $paid_amount;
                        $progress_percentage = $plan['total_amount'] > 0 ? ($paid_amount / $plan['total_amount']) * 100 : 0;
                        
                        // تحديد لون الكارت حسب الحالة
                        $card_class = '';
                        $status_badge = '';
                        if ($plan['status'] === 'completed') {
                            $card_class = 'border-success';
                            $status_badge = '<span class="badge bg-success">مكتمل</span>';
                        } elseif ($plan['status'] === 'cancelled') {
                            $card_class = 'border-danger';
                            $status_badge = '<span class="badge bg-danger">ملغي</span>';
                        } elseif ($plan['overdue_count'] > 0) {
                            $card_class = 'border-warning';
                            $status_badge = '<span class="badge bg-warning">يوجد أقساط متأخرة</span>';
                        } else {
                            $card_class = 'border-primary';
                            $status_badge = '<span class="badge bg-primary">نشط</span>';
                        }
                        ?>
                        
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 <?= $card_class ?>">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-box me-2"></i>
                                        <?= htmlspecialchars($plan['service_name']) ?>
                                    </h6>
                                    <?= $status_badge ?>
                                </div>
                                <div class="card-body">
                                    <h6 class="text-muted mb-3"><?= htmlspecialchars($plan['plan_name']) ?></h6>
                                    
                                    <!-- الملخص المالي -->
                                    <div class="row text-center mb-3">
                                        <div class="col-4">
                                            <div class="border-end">
                                                <h6 class="text-primary mb-1"><?= number_format($plan['total_amount'], 2) ?></h6>
                                                <small class="text-muted">إجمالي جنيه</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="border-end">
                                                <h6 class="text-success mb-1"><?= number_format($paid_amount, 2) ?></h6>
                                                <small class="text-muted">مدفوع جنيه</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <h6 class="text-warning mb-1"><?= number_format($remaining_amount, 2) ?></h6>
                                            <small class="text-muted">متبقي جنيه</small>
                                        </div>
                                    </div>
                                    
                                    <!-- شريط التقدم -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <small class="text-muted">التقدم</small>
                                            <small class="text-muted"><?= number_format($paid_amount, 2) ?>/<?= number_format($plan['total_amount'], 2) ?> جنيه</small>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: <?= $progress_percentage ?>%"></div>
                                        </div>
                                        <div class="text-center mt-1">
                                            <small class="text-muted"><?= number_format($progress_percentage, 1) ?>%</small>
                                        </div>
                                    </div>
                                    
                                    <!-- معلومات إضافية -->
                                    <div class="row text-center small text-muted mb-3">
                                        <div class="col-6">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            <?= $plan['installments_count'] ?> قسط
                                        </div>
                                        <div class="col-6">
                                            <i class="fas fa-money-bill-wave me-1"></i>
                                            <?= number_format($plan['installment_amount'], 2) ?> جنيه/قسط
                                        </div>
                                    </div>
                                    
                                    <?php if ($plan['overdue_count'] > 0): ?>
                                        <div class="alert alert-warning py-2 mb-3">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <small>لديك <?= $plan['overdue_count'] ?> قسط متأخر</small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-footer">
                                    <div class="d-grid gap-2">
                                        <?php if ($plan['status'] === 'active' && ($plan['installments_count'] - $plan['paid_installments']) > 0): ?>
                                            <button type="button" class="btn btn-success btn-sm"
                                                    onclick="openPaymentModal(<?= $plan['installment_amount'] ?>, 'installment', <?= $plan['id'] ?>)">
                                                <i class="fas fa-credit-card me-2"></i>
                                                دفع القسط التالي (<?= number_format($plan['installment_amount'], 2) ?> جنيه)
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-primary btn-sm"
                                                onclick="viewInstallmentDetails(<?= $plan['id'] ?>)">
                                            <i class="fas fa-eye me-2"></i>
                                            عرض التفاصيل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- إحصائيات سريعة -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card" style="background-color: #1e1e1e; border: 1px solid #333333; color: #e0e0e0;">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    ملخص عام
                                </h6>
                                <div class="row text-center">
                                    <?php
                                    $total_plans = count($installment_plans);
                                    $active_plans = count(array_filter($installment_plans, fn($p) => $p['status'] === 'active'));
                                    $completed_plans = count(array_filter($installment_plans, fn($p) => $p['status'] === 'completed'));
                                    $total_amount = array_sum(array_column($installment_plans, 'total_amount'));
                                    // حساب إجمالي المدفوع مع المقدم
                                    $total_paid = array_sum(array_map(fn($p) => $p['down_payment'] + ($p['paid_count'] * $p['installment_amount']), $installment_plans));
                                    ?>
                                    <div class="col-md-3 col-6 mb-3">
                                        <h5 class="text-primary mb-1"><?= $total_plans ?></h5>
                                        <small class="text-muted">إجمالي الخطط</small>
                                    </div>
                                    <div class="col-md-3 col-6 mb-3">
                                        <h5 class="text-success mb-1"><?= $active_plans ?></h5>
                                        <small class="text-muted">خطط نشطة</small>
                                    </div>
                                    <div class="col-md-3 col-6 mb-3">
                                        <h5 class="text-info mb-1"><?= number_format($total_amount, 2) ?></h5>
                                        <small class="text-muted">إجمالي المبلغ (جنيه)</small>
                                    </div>
                                    <div class="col-md-3 col-6 mb-3">
                                        <h5 class="text-warning mb-1"><?= number_format($total_paid, 2) ?></h5>
                                        <small class="text-muted">إجمالي المدفوع (جنيه)</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نموذج تفاصيل القسط -->
<div class="modal fade" id="installmentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل خطة التقسيط</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="installment_details_content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// عرض تفاصيل خطة التقسيط
function viewInstallmentDetails(installmentId) {
    const modal = new bootstrap.Modal(document.getElementById('installmentDetailsModal'));
    const content = document.getElementById('installment_details_content');
    
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    fetch(`api/get_installment_details.php?id=${installmentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = generateInstallmentDetailsHTML(data.plan, data.payments);
            } else {
                content.innerHTML = '<div class="alert alert-danger">فشل في تحميل التفاصيل</div>';
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل التفاصيل:', error);
            content.innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل التفاصيل</div>';
        });
}

// إنشاء HTML لتفاصيل القسط
function generateInstallmentDetailsHTML(plan, payments) {
    // حساب المبلغ المدفوع مع المقدم
    const paidInstallments = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + parseFloat(p.amount), 0);
    const downPayment = parseFloat(plan.down_payment) || 0;
    const paidAmount = downPayment + paidInstallments;
    const totalAmount = parseFloat(plan.total_amount);
    const remainingAmount = totalAmount - paidAmount;
    const progressPercentage = (paidAmount / totalAmount) * 100;
    
    let html = `
        <div class="row mb-4">
            <div class="col-md-6">
                <h6>معلومات الخطة</h6>
                <p><strong>الخدمة:</strong> ${plan.service_name}</p>
                <p><strong>الخطة:</strong> ${plan.plan_name}</p>
                <p><strong>تاريخ البدء:</strong> ${plan.created_at}</p>
                <p><strong>الحالة:</strong> ${getStatusBadge(plan.status)}</p>
            </div>
            <div class="col-md-6">
                <h6>ملخص مالي</h6>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center p-3 bg-primary text-white rounded">
                            <h5>${totalAmount.toFixed(2)} جنيه</h5>
                            <small>المبلغ الإجمالي</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-success text-white rounded">
                            <h5>${paidAmount.toFixed(2)} جنيه</h5>
                            <small>المدفوع</small>
                        </div>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" style="width: ${progressPercentage}%">
                            ${progressPercentage.toFixed(1)}%
                        </div>
                    </div>
                    <div class="text-center mt-1">
                        <small>المتبقي: ${remainingAmount.toFixed(2)} جنيه</small>
                    </div>
                </div>
            </div>
        </div>
        
        <h6>جدول الأقساط</h6>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم القسط</th>
                        <th>المبلغ</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>تاريخ الدفع</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    payments.forEach(payment => {
        const statusBadge = getPaymentStatusBadge(payment.status);
        
        html += `
            <tr>
                <td>${payment.installment_number}</td>
                <td>${parseFloat(payment.amount).toFixed(2)} جنيه</td>
                <td>${payment.due_date}</td>
                <td>${payment.paid_date || '-'}</td>
                <td>${statusBadge}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    return html;
}

// إنشاء شارة حالة الخطة
function getStatusBadge(status) {
    switch (status) {
        case 'active':
            return '<span class="badge bg-primary">نشط</span>';
        case 'completed':
            return '<span class="badge bg-success">مكتمل</span>';
        case 'cancelled':
            return '<span class="badge bg-danger">ملغي</span>';
        case 'suspended':
            return '<span class="badge bg-warning">معلق</span>';
        default:
            return '<span class="badge bg-secondary">غير محدد</span>';
    }
}

// إنشاء شارة حالة الدفعة
function getPaymentStatusBadge(status) {
    switch (status) {
        case 'paid':
            return '<span class="badge bg-success">مدفوع</span>';
        case 'overdue':
            return '<span class="badge bg-danger">متأخر</span>';
        case 'pending':
        default:
            return '<span class="badge bg-warning">معلق</span>';
    }
}
</script>

<style>
/* Force dark theme for installments page */
body {
    background-color: #121212 !important;
    color: #e0e0e0 !important;
}

.card {
    background-color: #1e1e1e !important;
    border: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.card-body {
    color: #e0e0e0 !important;
}

.card-title {
    color: #e0e0e0 !important;
}

.text-muted {
    color: #b0b0b0 !important;
}

.table {
    background-color: #1e1e1e !important;
    color: #e0e0e0 !important;
}

.table th {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.table td {
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.table tbody tr:hover {
    background-color: #2a2a2a !important;
}

.progress {
    background-color: #333333 !important;
}

.btn-outline-primary {
    border-color: #6c7ce7 !important;
    color: #6c7ce7 !important;
}

.btn-outline-primary:hover {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
    color: white !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #e0e0e0 !important;
}

p, span, div {
    color: #e0e0e0 !important;
}
</style>

<?php
// إضافة مكون بوابات الدفع
include __DIR__ . '/includes/payment_gateways_modal.php';
include __DIR__ . '/includes/footer.php';
?>
