<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'config/wawp.php';

// إنشاء اتصال قاعدة البيانات
try {
    $database = new Database();
    $pdo = $database->getConnection();
} catch (Exception $e) {
    die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
}

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';
$step = 1; // 1 = إدخال البيانات، 2 = تأكيد OTP

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['name']) && !isset($_POST['otp_code'])) {
        // الخطوة الأولى: إنشاء الحساب وإرسال OTP
        $name = trim($_POST['name']);
        $phone = trim($_POST['phone']);
        $governorate = trim($_POST['governorate']);
        $country = trim($_POST['country']);
        $profession = trim($_POST['profession']);
        $national_id = trim($_POST['national_id']);
        
        // التحقق من صحة البيانات
        if (empty($name) || empty($phone) || empty($governorate) || empty($country) || empty($profession) || empty($national_id)) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            // تنظيف رقم الهاتف المصري
            $clean_phone = preg_replace('/[^0-9]/', '', $phone);
            if (substr($clean_phone, 0, 1) === '0') {
                $clean_phone = '20' . substr($clean_phone, 1);
            } elseif (substr($clean_phone, 0, 2) !== '20') {
                $clean_phone = '20' . $clean_phone;
            }
            
            try {
                // التحقق من عدم وجود رقم الهاتف مسبقاً
                $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ?");
                $stmt->execute([$clean_phone]);
                
                if ($stmt->rowCount() > 0) {
                    $error = 'رقم الهاتف مسجل بالفعل. يرجى تسجيل الدخول.';
                } else {
                    // إنشاء OTP
                    $otp = generateOTP();
                    $otp_expires = date('Y-m-d H:i:s', time() + OTP_EXPIRY);
                    
                    // إدراج المستخدم الجديد
                    $stmt = $pdo->prepare("
                        INSERT INTO users (name, phone, governorate, country, profession, national_id, otp_code, otp_expires_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$name, $clean_phone, $governorate, $country, $profession, $national_id, $otp, $otp_expires]);
                    
                    // جلب WAWP token
                    $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'wawp_token'");
                    $stmt->execute();
                    $wawp_token = $stmt->fetchColumn();
                    
                    if ($wawp_token) {
                        // إرسال OTP عبر واتساب
                        $wawp = new WAWP();
                        $message = "مرحباً " . $name . "، رمز التحقق لتفعيل حسابك في FutureWay هو: " . $otp . "\nأهلاً بك في منصة FutureWay للخدمات المتقدمة";
                        $result = $wawp->sendMessage($clean_phone, $message);
                        
                        if ($result['success']) {
                            $success = 'تم إنشاء حسابك بنجاح! تم إرسال رمز التحقق إلى واتساب الخاص بك';
                            $step = 2;
                            $_SESSION['register_phone'] = $clean_phone;
                            $_SESSION['register_name'] = $name;
                        } else {
                            $error = 'تم إنشاء الحساب ولكن فشل في إرسال رمز التحقق. يرجى المحاولة مرة أخرى.';
                        }
                    } else {
                        $error = 'خدمة الرسائل غير متاحة حالياً';
                    }
                }
            } catch (Exception $e) {
                $error = 'خطأ في النظام. يرجى المحاولة مرة أخرى.';
            }
        }
    } elseif (isset($_POST['otp_code'])) {
        // الخطوة الثانية: تأكيد OTP
        $otp_code = trim($_POST['otp_code']);
        $phone = $_SESSION['register_phone'] ?? '';
        
        if (empty($otp_code) || empty($phone)) {
            $error = 'يرجى إدخال رمز التحقق';
            $step = 2;
        } else {
            try {
                // التحقق من OTP
                $stmt = $pdo->prepare("SELECT * FROM users WHERE phone = ? AND otp_code = ? AND otp_expires_at > NOW()");
                $stmt->execute([$phone, $otp_code]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user) {
                    // تفعيل الحساب
                    $stmt = $pdo->prepare("UPDATE users SET is_verified = TRUE, otp_code = NULL, otp_expires_at = NULL WHERE id = ?");
                    $stmt->execute([$user['id']]);
                    
                    // تسجيل الدخول التلقائي
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['name'];
                    $_SESSION['user_phone'] = $user['phone'];
                    $_SESSION['is_admin'] = (bool)$user['is_admin'];
                    
                    // مسح بيانات الجلسة المؤقتة
                    unset($_SESSION['register_phone']);
                    unset($_SESSION['register_name']);
                    
                    header('Location: dashboard.php');
                    exit;
                } else {
                    $error = 'رمز التحقق غير صحيح أو منتهي الصلاحية';
                    $step = 2;
                }
            } catch (Exception $e) {
                $error = 'خطأ في النظام. يرجى المحاولة مرة أخرى.';
                $step = 2;
            }
        }
    }
}

// التحقق من وجود رقم هاتف في الجلسة للانتقال للخطوة الثانية
// فقط إذا لم يكن هناك طلب إعادة تعيين صريح
if (isset($_SESSION['register_phone']) && empty($_POST) && !isset($_GET['reset'])) {
    $step = 2;
}

// إعادة تعيين الجلسة إذا طُلب ذلك
if (isset($_GET['reset'])) {
    unset($_SESSION['register_phone']);
    unset($_SESSION['register_name']);
    $step = 1;
}

$page_title = 'إنشاء حساب جديد';
$hide_footer = true;

// جلب قائمة المحافظات والدول
try {
    $stmt = $pdo->prepare("SELECT DISTINCT governorate FROM users WHERE governorate IS NOT NULL AND governorate != '' ORDER BY governorate");
    $stmt->execute();
    $governorates = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $stmt = $pdo->prepare("SELECT DISTINCT country FROM users WHERE country IS NOT NULL AND country != '' ORDER BY country");
    $stmt->execute();
    $countries = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $governorates = [];
    $countries = [];
}

// قائمة افتراضية للمحافظات المصرية
$default_governorates = [
    'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة', 'الفيوم',
    'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية', 'الوادي الجديد', 'السويس',
    'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد', 'دمياط', 'الشرقية', 'جنوب سيناء',
    'كفر الشيخ', 'مطروح', 'الأقصر', 'قنا', 'شمال سيناء', 'سوهاج'
];

// قائمة افتراضية للدول العربية مع مصر في المقدمة
$default_countries = [
    'مصر', 'السعودية', 'الإمارات', 'الكويت', 'قطر', 'البحرين', 'عمان', 'الأردن',
    'لبنان', 'سوريا', 'العراق', 'المغرب', 'تونس', 'الجزائر', 'ليبيا',
    'السودان', 'اليمن', 'فلسطين'
];

// دمج القوائم
$governorates = array_unique(array_merge($default_governorates, $governorates));
$countries = array_unique(array_merge($default_countries, $countries));
sort($governorates);
sort($countries);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - FutureWay</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- International Telephone Input -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/css/intlTelInput.css">
</head>
<body>

<div class="register-container">
    <div class="register-card">
        <div class="register-header">
            <div class="logo">
                <i class="fas fa-rocket"></i>
                <h1>FutureWay</h1>
            </div>
            <h2><?= $step === 1 ? 'إنشاء حساب جديد' : 'تأكيد رقم الهاتف' ?></h2>
            <p class="text-muted">
                <?= $step === 1 ? 'املأ البيانات التالية لإنشاء حسابك' : 'أدخل رمز التحقق المرسل إلى واتساب' ?>
            </p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" class="register-form">
            <?php if ($step === 1): ?>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-2"></i>
                                الاسم الكامل
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required 
                                   placeholder="أدخل اسمك الكامل" value="<?= htmlspecialchars($_POST['name'] ?? '') ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-2"></i>
                                رقم الهاتف
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" required 
                                   placeholder="أدخل رقم هاتفك" value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="governorate" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                المحافظة
                            </label>
                            <select class="form-select" id="governorate" name="governorate" required>
                                <option value="">اختر المحافظة</option>
                                <?php foreach ($governorates as $gov): ?>
                                    <option value="<?= htmlspecialchars($gov) ?>" 
                                            <?= (($_POST['governorate'] ?? '') === $gov) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($gov) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="country" class="form-label">
                                <i class="fas fa-globe me-2"></i>
                                الدولة
                            </label>
                            <select class="form-select" id="country" name="country" required>
                                <option value="">اختر الدولة</option>
                                <?php foreach ($countries as $country): ?>
                                    <option value="<?= htmlspecialchars($country) ?>" 
                                            <?= (($_POST['country'] ?? '') === $country) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($country) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="profession" class="form-label">
                                <i class="fas fa-briefcase me-2"></i>
                                المهنة
                            </label>
                            <input type="text" class="form-control" id="profession" name="profession" required
                                   placeholder="أدخل مهنتك" value="<?= htmlspecialchars($_POST['profession'] ?? '') ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="national_id" class="form-label">
                                <i class="fas fa-id-card me-2"></i>
                                الرقم القومي المصري
                            </label>
                            <input type="text" class="form-control" id="national_id" name="national_id" required
                                   placeholder="أدخل الرقم القومي (14 رقم)" maxlength="14" value="<?= htmlspecialchars($_POST['national_id'] ?? '') ?>">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="terms" required>
                        <label class="form-check-label" for="terms">
                            أوافق على <a href="#" class="text-primary">الشروط والأحكام</a> و <a href="#" class="text-primary">سياسة الخصوصية</a>
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary btn-lg w-100">
                    <i class="fas fa-user-plus me-2"></i>
                    إنشاء الحساب
                </button>
            <?php else: ?>
                <div class="form-group">
                    <label for="otp_code" class="form-label">
                        <i class="fas fa-shield-alt me-2"></i>
                        رمز التحقق
                    </label>
                    <input type="text" class="form-control otp-input" id="otp_code" name="otp_code"
                           maxlength="6" required autocomplete="off" placeholder="000000">
                    <div class="form-text">أدخل الرمز المكون من 6 أرقام</div>
                </div>

                <button type="submit" class="btn btn-primary btn-lg w-100">
                    <i class="fas fa-check me-2"></i>
                    تأكيد الحساب
                </button>

                <div class="text-center mt-3">
                    <a href="register.php?reset=1" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </a>
                </div>
            <?php endif; ?>
        </form>

        <div class="divider">
            <span>أو</span>
        </div>

        <div class="text-center">
            <p class="mb-2">لديك حساب بالفعل؟</p>
            <a href="login.php" class="btn btn-outline-primary">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </a>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="auth-footer">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <p class="mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    <strong>FutureWay</strong> - خدمات متابعة وأنظمة التقسيط
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> FutureWay. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>
</footer>

<style>
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-color: #e2e8f0;
    --text-muted: #64748b;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

body {
    font-family: 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;
}

.register-container {
    width: 100%;
    max-width: 700px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.register-card {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    padding: 40px;
    width: 100%;
    animation: fadeInUp 0.6s ease-out;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.register-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-left: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo h1 {
    color: var(--primary-color);
    font-weight: 700;
    margin: 0;
    font-size: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.register-header h2 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.register-header p {
    margin: 0;
    font-size: 0.95rem;
    color: var(--text-muted);
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.form-label i {
    color: var(--primary-color);
}

.form-control, .form-select {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: var(--light-color);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
    background: white;
}

.otp-input {
    text-align: center;
    font-size: 24px;
    letter-spacing: 8px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-check {
    margin: 20px 0;
}

.form-check-input {
    border: 2px solid var(--border-color);
    border-radius: 6px;
    margin-left: 10px;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    color: var(--dark-color);
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 12px;
    padding: 15px 25px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: 12px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-outline-secondary {
    border: 2px solid var(--text-muted);
    color: var(--text-muted);
    border-radius: 12px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--text-muted);
    border-color: var(--text-muted);
    color: white;
}

.divider {
    text-align: center;
    margin: 30px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.divider span {
    background: white;
    padding: 0 20px;
    color: var(--text-muted);
    font-size: 0.9rem;
    position: relative;
}

.alert {
    border-radius: 12px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
    font-weight: 500;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: var(--danger-color);
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
}

.auth-footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 0;
    margin-top: 30px;
    width: 100%;
    color: white;
}

.auth-footer p {
    margin: 0;
    font-size: 0.9rem;
}

/* International Telephone Input Styling */
.iti {
    width: 100%;
}

.iti__country-list {
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.iti__selected-flag {
    border-radius: 12px 0 0 12px;
    background: var(--light-color);
}

/* Select2 Styling */
.select2-container--bootstrap-5 .select2-selection {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 10px 15px;
    min-height: 50px;
    background: var(--light-color);
}

.select2-container--bootstrap-5.select2-container--focus .select2-selection {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
}

.select2-dropdown {
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .register-card {
        padding: 30px 20px;
        border-radius: 15px;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .logo i {
        font-size: 2rem;
    }

    .register-header h2 {
        font-size: 1.3rem;
    }

    .auth-footer {
        text-align: center;
    }

    .auth-footer .col-md-6:last-child {
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .otp-input {
        font-size: 20px;
        letter-spacing: 4px;
    }

    .col-md-6 {
        margin-bottom: 15px;
    }
}
</style>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/intlTelInput.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إعداد International Telephone Input
    const phoneInput = document.querySelector("#phone");
    let iti;

    if (phoneInput) {
        iti = window.intlTelInput(phoneInput, {
            initialCountry: "eg",
            preferredCountries: ["eg", "sa", "ae", "kw", "qa", "bh"],
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js",
            formatOnDisplay: true,
            nationalMode: false,
            autoPlaceholder: "aggressive"
        });

        // تنظيف رقم الهاتف عند الإرسال
        document.querySelector('.register-form').addEventListener('submit', function() {
            phoneInput.value = iti.getNumber().replace('+', '');
        });
    }

    // إعداد Select2
    $('#governorate, #country').select2({
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: function() {
            return $(this).data('placeholder');
        },
        allowClear: true
    });

    // إضافة placeholder للـ Select2
    $('#governorate').attr('data-placeholder', 'اختر المحافظة');
    $('#country').attr('data-placeholder', 'اختر الدولة');

    // التركيز التلقائي على حقل OTP
    const otpInput = document.querySelector("#otp_code");
    if (otpInput) {
        otpInput.focus();

        // السماح بالأرقام فقط
        otpInput.addEventListener('input', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        // إرسال النموذج تلقائياً عند إدخال 6 أرقام
        otpInput.addEventListener('input', function(e) {
            if (this.value.length === 6) {
                setTimeout(() => {
                    document.querySelector('.register-form').submit();
                }, 500);
            }
        });

        // تحسين تجربة المستخدم - تقسيم الأرقام
        otpInput.addEventListener('keydown', function(e) {
            // السماح بمفاتيح التحكم
            if (e.key === 'Backspace' || e.key === 'Delete' || e.key === 'Tab' ||
                e.key === 'Escape' || e.key === 'Enter' || e.key === 'ArrowLeft' ||
                e.key === 'ArrowRight') {
                return;
            }

            // منع الأحرف غير الرقمية
            if (!/[0-9]/.test(e.key)) {
                e.preventDefault();
            }
        });
    }

    // التحقق من صحة الرقم القومي المصري
    const nationalIdInput = document.querySelector("#national_id");
    if (nationalIdInput) {
        nationalIdInput.addEventListener('input', function(e) {
            // السماح بالأرقام فقط
            this.value = this.value.replace(/[^0-9]/g, '');

            // التحقق من طول الرقم
            if (this.value.length > 14) {
                this.value = this.value.substring(0, 14);
            }

            // التحقق من صحة الرقم القومي
            if (this.value.length === 14) {
                if (validateEgyptianNationalId(this.value)) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                this.classList.remove('is-valid', 'is-invalid');
            }
        });
    }

    // دالة التحقق من صحة الرقم القومي المصري
    function validateEgyptianNationalId(id) {
        if (id.length !== 14) return false;

        // التحقق من أن الرقم يبدأ بـ 2 أو 3 (القرن)
        const century = id.substring(0, 1);
        if (century !== '2' && century !== '3') return false;

        // التحقق من صحة التاريخ
        const year = parseInt(id.substring(1, 3));
        const month = parseInt(id.substring(3, 5));
        const day = parseInt(id.substring(5, 7));

        if (month < 1 || month > 12) return false;
        if (day < 1 || day > 31) return false;

        // التحقق من رمز المحافظة
        const governorateCode = parseInt(id.substring(7, 9));
        if (governorateCode < 1 || governorateCode > 35) return false;

        return true;
    }

    // تحسين تجربة المستخدم - إضافة تأثيرات بصرية
    const form = document.querySelector('.register-form');
    if (form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                submitBtn.disabled = true;
            }
        });
    }

    // إخفاء الرسائل تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });

    // تحسين تجربة المستخدم - التحقق من قوة كلمة المرور (إذا كانت مطلوبة لاحقاً)
    // يمكن إضافة المزيد من التحققات هنا

    // إضافة تأثيرات بصرية للحقول
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});
</script>

</body>
</html>
