<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($page_title) ? $page_title . ' - ' : '' ?>FutureWay Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- intl-tel-input -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/css/intlTelInput.css">
    
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    
    <!-- Dark Mode CSS -->
    <link href="../assets/css/dark-mode.css" rel="stylesheet">

    <!-- Admin Dark Mode CSS -->
    <link href="../assets/css/admin-dark.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --text-color: #2c3e50;
            --text-muted: #6c757d;
            --border-color: #e9ecef;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #121212 !important;
            color: #e0e0e0 !important;
        }

        /* Force dark theme for all admin elements */
        * {
            color: #e0e0e0;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #e0e0e0 !important;
        }

        p, span, div, li, td, th {
            color: #e0e0e0 !important;
        }

        .text-muted {
            color: #b0b0b0 !important;
        }
        
        .admin-navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-navbar .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }
        
        .admin-navbar .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .admin-navbar .nav-link:hover {
            color: white !important;
            transform: translateY(-1px);
        }
        
        .admin-navbar .dropdown-menu {
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        
        .admin-sidebar {
            background: #1e1e1e;
            border-right: 1px solid #333333;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.3);
            padding: 0;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            border-bottom: 1px solid var(--border-color);
        }
        
        .sidebar-menu li:last-child {
            border-bottom: none;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #e0e0e0;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .sidebar-menu a:hover {
            background: linear-gradient(135deg, rgba(108, 124, 231, 0.2) 0%, rgba(139, 95, 191, 0.2) 100%);
            color: #6c7ce7;
            text-decoration: none;
        }
        
        .sidebar-menu a.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }
        
        .sidebar-menu a.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: white;
        }
        
        .sidebar-menu i {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .page-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .card {
            border: 1px solid #333333;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }

        .table thead th {
            background: #2a2a2a;
            border: none;
            font-weight: 600;
            color: #e0e0e0;
        }

        .table tbody td {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        .table tbody tr:hover {
            background-color: #2a2a2a;
        }
        
        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }
        
        .badge {
            font-size: 0.8rem;
            padding: 0.5em 0.8em;
        }
        
        .modal-content {
            border: none;
            border-radius: 15px;
        }
        
        .modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .modal-header .btn-close {
            filter: invert(1);
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #333333;
            transition: all 0.3s ease;
            background-color: #2a2a2a;
            color: #e0e0e0;
        }

        .form-control:focus, .form-select:focus {
            border-color: #6c7ce7;
            box-shadow: 0 0 0 0.2rem rgba(108, 124, 231, 0.25);
            background-color: #2a2a2a;
            color: #e0e0e0;
        }

        .form-label {
            color: #e0e0e0;
        }
        
        .alert {
            border: 1px solid #333333;
            border-radius: 10px;
            background-color: #2a2a2a;
            color: #e0e0e0;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            border-color: rgba(40, 167, 69, 0.3);
            color: #86efac;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            border-color: rgba(220, 53, 69, 0.3);
            color: #fca5a5;
        }

        .alert-warning {
            background-color: rgba(255, 193, 7, 0.1);
            border-color: rgba(255, 193, 7, 0.3);
            color: #fde047;
        }

        .alert-info {
            background-color: rgba(23, 162, 184, 0.1);
            border-color: rgba(23, 162, 184, 0.3);
            color: #7dd3fc;
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                position: fixed;
                top: 76px;
                left: -100%;
                width: 280px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .admin-sidebar.show {
                left: 0;
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Admin Navbar -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-rocket me-2"></i>
                FutureWay Admin
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>
                            عرض الموقع
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?= htmlspecialchars($_SESSION['user_name'] ?? 'المدير') ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../dashboard.php">لوحة التحكم الشخصية</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar">
                <ul class="sidebar-menu">
                    <li>
                        <a href="index.php" class="<?= basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : '' ?>">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="<?= basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : '' ?>">
                            <i class="fas fa-users"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                    <li>
                        <a href="services.php" class="<?= basename($_SERVER['PHP_SELF']) == 'services.php' ? 'active' : '' ?>">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="pricing_plans.php" class="<?= basename($_SERVER['PHP_SELF']) == 'pricing_plans.php' ? 'active' : '' ?>">
                            <i class="fas fa-tags"></i>
                            خطط الأسعار
                        </a>
                    </li>
                    <li>
                        <a href="installments.php" class="<?= basename($_SERVER['PHP_SELF']) == 'installments.php' ? 'active' : '' ?>">
                            <i class="fas fa-credit-card"></i>
                            خطط التقسيط
                        </a>
                    </li>
                    <li>
                        <a href="payments.php" class="<?= basename($_SERVER['PHP_SELF']) == 'payments.php' ? 'active' : '' ?>">
                            <i class="fas fa-money-bill-wave"></i>
                            إدارة المدفوعات
                        </a>
                    </li>
                    <li>
                        <a href="payment_gateways.php" class="<?= basename($_SERVER['PHP_SELF']) == 'payment_gateways.php' ? 'active' : '' ?>">
                            <i class="fas fa-money-check-alt"></i>
                            بوابات الدفع
                        </a>
                    </li>
                    <li>
                        <a href="payment_requests.php" class="<?= basename($_SERVER['PHP_SELF']) == 'payment_requests.php' ? 'active' : '' ?>">
                            <i class="fas fa-file-invoice-dollar"></i>
                            طلبات الدفع
                        </a>
                    </li>
                    <li>
                        <a href="wawp_test.php" class="<?= basename($_SERVER['PHP_SELF']) == 'wawp_test.php' ? 'active' : '' ?>">
                            <i class="fab fa-whatsapp"></i>
                            اختبار WAWP
                        </a>
                    </li>
                    <li>
                        <a href="settings.php" class="<?= basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : '' ?>">
                            <i class="fas fa-cog"></i>
                            إعدادات النظام
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">

