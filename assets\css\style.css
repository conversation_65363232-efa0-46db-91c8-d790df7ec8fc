/* Modern FutureWay Styles */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4facfe;
    --warning-color: #ffc107;
    --danger-color: #f5576c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --text-color: #2c3e50;
    --text-muted: #6c757d;
    --border-radius: 15px;
    --border-radius-sm: 8px;
    --border-radius-lg: 25px;
    --box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    --box-shadow-sm: 0 5px 15px rgba(0,0,0,0.08);
    --box-shadow-lg: 0 20px 60px rgba(0,0,0,0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    line-height: 1.6;
    color: #e0e0e0;
    background-color: #121212;
}

/* Force light text colors for all elements */
h1, h2, h3, h4, h5, h6 {
    color: #e0e0e0 !important;
}

p {
    color: #e0e0e0 !important;
}

.text-muted {
    color: #b0b0b0 !important;
}

.lead {
    color: #e0e0e0 !important;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    color: #e0e0e0 !important;
}

/* Override Bootstrap's white backgrounds */
.bg-light {
    background-color: #1e1e1e !important;
}

.bg-white {
    background-color: #1e1e1e !important;
}

.card {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.card-body {
    color: #e0e0e0 !important;
}

.card-title {
    color: #e0e0e0 !important;
}

.card-text {
    color: #b0b0b0 !important;
}

/* Utility Classes */
.gradient-primary {
    background: var(--gradient-primary);
}

.gradient-secondary {
    background: var(--gradient-secondary);
}

.gradient-success {
    background: var(--gradient-success);
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.rounded-modern {
    border-radius: var(--border-radius) !important;
}

.rounded-modern-sm {
    border-radius: var(--border-radius-sm) !important;
}

.rounded-modern-lg {
    border-radius: var(--border-radius-lg) !important;
}

.shadow-modern {
    box-shadow: var(--box-shadow) !important;
}

.shadow-modern-sm {
    box-shadow: var(--box-shadow-sm) !important;
}

.shadow-modern-lg {
    box-shadow: var(--box-shadow-lg) !important;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
    background-position: bottom;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons .btn {
    border-radius: var(--border-radius-lg);
    padding: 1rem 2rem;
    font-weight: 600;
    margin: 0.5rem;
    transition: var(--transition);
}

.hero-buttons .btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

/* Modern Cards */
.modern-card {
    background: #1e1e1e;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    border: 1px solid #333333;
    transition: var(--transition);
    overflow: hidden;
    color: #e0e0e0;
}

.modern-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-lg);
}

.modern-card .card-header {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1.5rem;
}

.modern-card .card-body {
    padding: 2rem;
    color: #e0e0e0;
}

.modern-card .card-footer {
    background: transparent;
    border-top: 1px solid #333333;
    padding: 1.5rem;
}

/* Service Cards */
.service-card {
    background: #1e1e1e;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    border: 1px solid #333333;
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
    color: #e0e0e0;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.service-card .service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.service-card .card-title {
    color: #e0e0e0;
    font-weight: 600;
    margin-bottom: 1rem;
}

.service-card .card-text {
    color: #b0b0b0;
    line-height: 1.6;
}

/* Modern Buttons */
.btn-modern {
    border-radius: var(--border-radius-lg);
    padding: 0.75rem 2rem;
    font-weight: 600;
    border: none;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-primary-modern {
    background: var(--gradient-primary);
    color: white;
}

.btn-secondary-modern {
    background: var(--gradient-secondary);
    color: white;
}

.btn-success-modern {
    background: var(--gradient-success);
    color: white;
}

/* Modern Forms */
.form-modern .form-control {
    border-radius: var(--border-radius-sm);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-modern .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-modern .form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

/* Swiper Carousel Styles */
.services-carousel {
    padding: 2rem 0;
}

.services-carousel .swiper-slide {
    height: auto;
}

.services-carousel .swiper-pagination-bullet {
    background: var(--primary-color);
    opacity: 0.3;
}

.services-carousel .swiper-pagination-bullet-active {
    opacity: 1;
    background: var(--primary-color);
}

.services-carousel .swiper-button-next,
.services-carousel .swiper-button-prev {
    color: var(--primary-color);
    background: white;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    box-shadow: var(--box-shadow-sm);
}

.services-carousel .swiper-button-next:after,
.services-carousel .swiper-button-prev:after {
    font-size: 1.2rem;
}

/* Statistics Section */
.stats-section {
    background: var(--gradient-dark);
    color: white;
    padding: 4rem 0;
}

.stat-item {
    text-align: center;
    padding: 2rem 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-color);
    display: block;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-top: 0.5rem;
}

/* Features Section */
.features-section {
    padding: 5rem 0;
    background: #121212;
}

.feature-item {
    text-align: center;
    padding: 2rem 1rem;
}

.feature-icon {
    width: 100px;
    height: 100px;
    background: var(--gradient-success);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2.5rem;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #e0e0e0;
    margin-bottom: 1rem;
}

.feature-description {
    color: #b0b0b0;
    line-height: 1.6;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

/* Animation Classes */
.animate-in {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-header.scrolled {
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(10px);
}

.navbar-toggler.active .navbar-toggler-icon {
    transform: rotate(45deg);
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .modern-header {
        transition: transform 0.3s ease;
    }

    .hero-section {
        padding: 4rem 0;
    }

    .hero-title {
        font-size: 2.8rem;
    }

    .stats-section {
        padding: 3rem 0;
    }

    .features-section {
        padding: 4rem 0;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .modern-card .card-body {
        padding: 1.5rem;
    }

    .services-carousel .swiper-button-next,
    .services-carousel .swiper-button-prev {
        display: none;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 3rem 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .service-card .service-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .feature-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .modern-card .card-body {
        padding: 1rem;
    }

    .contact-item {
        font-size: 0.9rem;
    }

    .footer-links-inline {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Print Styles */
@media print {
    .modern-header,
    .modern-footer,
    .hero-buttons,
    .swiper-button-next,
    .swiper-button-prev,
    .swiper-pagination {
        display: none !important;
    }

    .hero-section {
        background: white !important;
        color: black !important;
    }

    .modern-card,
    .service-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
