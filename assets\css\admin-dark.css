/* Admin Dark Mode - FutureWay */

/* Force dark theme for all admin pages */
body {
    background-color: #121212 !important;
    color: #e0e0e0 !important;
}

/* Global text colors */
h1, h2, h3, h4, h5, h6 {
    color: #e0e0e0 !important;
}

p, span, div, li, td, th, label {
    color: #e0e0e0 !important;
}

.text-muted {
    color: #b0b0b0 !important;
}

.text-dark {
    color: #e0e0e0 !important;
}

/* Cards */
.card {
    background-color: #1e1e1e !important;
    border: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.card-header {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%) !important;
    border-bottom: 1px solid #333333 !important;
    color: white !important;
}

.card-body {
    background-color: #1e1e1e !important;
    color: #e0e0e0 !important;
}

.card-footer {
    background-color: #2a2a2a !important;
    border-top: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.card-title {
    color: #e0e0e0 !important;
}

.card-text {
    color: #b0b0b0 !important;
}

/* Tables */
.table {
    background-color: #1e1e1e !important;
    color: #e0e0e0 !important;
}

.table thead th {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.table tbody td {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.table tbody tr:hover {
    background-color: #2a2a2a !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #2a2a2a !important;
}

/* Forms */
.form-control, .form-select {
    background-color: #2a2a2a !important;
    border: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.form-control:focus, .form-select:focus {
    background-color: #2a2a2a !important;
    border-color: #6c7ce7 !important;
    color: #e0e0e0 !important;
    box-shadow: 0 0 0 0.2rem rgba(108, 124, 231, 0.25) !important;
}

.form-label {
    color: #e0e0e0 !important;
}

.form-check-label {
    color: #e0e0e0 !important;
}

.form-text {
    color: #b0b0b0 !important;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%) !important;
    border-color: #6c7ce7 !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #7a4f8a 100%) !important;
    border-color: #5a6fd8 !important;
}

.btn-outline-primary {
    border-color: #6c7ce7 !important;
    color: #6c7ce7 !important;
}

.btn-outline-primary:hover {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
    color: white !important;
}

.btn-outline-secondary {
    border-color: #6c757d !important;
    color: #6c757d !important;
}

.btn-outline-secondary:hover {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
}

.btn-outline-success {
    border-color: #28a745 !important;
    color: #28a745 !important;
}

.btn-outline-success:hover {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

.btn-outline-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

.btn-outline-warning:hover {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #000 !important;
}

.btn-outline-danger {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
}

.btn-outline-danger:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.btn-outline-info {
    border-color: #17a2b8 !important;
    color: #17a2b8 !important;
}

.btn-outline-info:hover {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white !important;
}

/* Modals */
.modal-content {
    background-color: #1e1e1e !important;
    border: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.modal-header {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%) !important;
    border-bottom: 1px solid #333333 !important;
    color: white !important;
}

.modal-body {
    background-color: #1e1e1e !important;
    color: #e0e0e0 !important;
}

.modal-footer {
    background-color: #1e1e1e !important;
    border-top: 1px solid #333333 !important;
}

.modal-title {
    color: white !important;
}

/* Alerts */
.alert {
    background-color: #2a2a2a !important;
    border: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-color: rgba(40, 167, 69, 0.3) !important;
    color: #86efac !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
    color: #fca5a5 !important;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-color: rgba(255, 193, 7, 0.3) !important;
    color: #fde047 !important;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
    border-color: rgba(23, 162, 184, 0.3) !important;
    color: #7dd3fc !important;
}

/* Dropdowns */
.dropdown-menu {
    background-color: #2a2a2a !important;
    border: 1px solid #333333 !important;
}

.dropdown-item {
    color: #e0e0e0 !important;
}

.dropdown-item:hover {
    background-color: #6c7ce7 !important;
    color: white !important;
}

.dropdown-divider {
    border-color: #333333 !important;
}

/* Pagination */
.page-link {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.page-link:hover {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
    color: white !important;
}

.page-item.active .page-link {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
}

.page-item.disabled .page-link {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #6c757d !important;
}

/* Breadcrumbs */
.breadcrumb {
    background-color: #2a2a2a !important;
}

.breadcrumb-item a {
    color: #6c7ce7 !important;
}

.breadcrumb-item.active {
    color: #b0b0b0 !important;
}

/* List groups */
.list-group-item {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.list-group-item:hover {
    background-color: #2a2a2a !important;
}

.list-group-item.active {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
}

/* Progress bars */
.progress {
    background-color: #333333 !important;
}

.progress-bar {
    background-color: #6c7ce7 !important;
}

.progress-bar-success {
    background-color: #28a745 !important;
}

.progress-bar-warning {
    background-color: #ffc107 !important;
}

.progress-bar-danger {
    background-color: #dc3545 !important;
}

/* Badges */
.badge {
    background-color: #6c7ce7 !important;
    color: white !important;
}

.badge-success {
    background-color: #28a745 !important;
}

.badge-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.badge-danger {
    background-color: #dc3545 !important;
}

.badge-info {
    background-color: #17a2b8 !important;
}

.badge-secondary {
    background-color: #6c757d !important;
}

/* Links */
a {
    color: #6c7ce7 !important;
}

a:hover {
    color: #8b5fbf !important;
}

/* Borders */
.border {
    border-color: #333333 !important;
}

.border-top {
    border-top-color: #333333 !important;
}

.border-bottom {
    border-bottom-color: #333333 !important;
}

.border-left {
    border-left-color: #333333 !important;
}

.border-right {
    border-right-color: #333333 !important;
}

/* Background utilities */
.bg-light {
    background-color: #1e1e1e !important;
}

.bg-white {
    background-color: #1e1e1e !important;
}

.bg-secondary {
    background-color: #2a2a2a !important;
}

.bg-dark {
    background-color: #121212 !important;
}
