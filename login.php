<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'config/wawp.php';

// إنشاء اتصال قاعدة البيانات
try {
    $database = new Database();
    $pdo = $database->getConnection();
} catch (Exception $e) {
    die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
}

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';
$step = 1; // 1 = إدخال رقم الهاتف، 2 = إدخال OTP

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['phone']) && !isset($_POST['otp_code'])) {
        // الخطوة الأولى: إرسال OTP
        $phone = trim($_POST['phone']);
        
        if (empty($phone)) {
            $error = 'يرجى إدخال رقم الهاتف';
        } else {
            // تنظيف رقم الهاتف المصري
            $clean_phone = preg_replace('/[^0-9]/', '', $phone);
            if (substr($clean_phone, 0, 1) === '0') {
                $clean_phone = '20' . substr($clean_phone, 1);
            } elseif (substr($clean_phone, 0, 2) !== '20') {
                $clean_phone = '20' . $clean_phone;
            }
            
            try {
                // التحقق من وجود المستخدم
                $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ? AND is_verified = TRUE");
                $stmt->execute([$clean_phone]);
                
                if ($stmt->rowCount() > 0) {
                    // إنشاء OTP جديد
                    $otp = generateOTP();
                    $otp_expires = date('Y-m-d H:i:s', time() + OTP_EXPIRY);
                    
                    // تحديث OTP في قاعدة البيانات
                    $stmt = $pdo->prepare("UPDATE users SET otp_code = ?, otp_expires_at = ? WHERE phone = ?");
                    $stmt->execute([$otp, $otp_expires, $clean_phone]);
                    
                    // جلب WAWP token
                    $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'wawp_token'");
                    $stmt->execute();
                    $wawp_token = $stmt->fetchColumn();
                    
                    if ($wawp_token) {
                        // إرسال OTP عبر واتساب
                        $wawp = new WAWP();
                        $message = "رمز تسجيل الدخول في FutureWay هو: " . $otp . "\nمرحباً بك في منصة FutureWay للخدمات المتقدمة";
                        $result = $wawp->sendMessage($clean_phone, $message);
                        
                        if ($result['success']) {
                            $success = 'تم إرسال رمز التحقق إلى واتساب الخاص بك';
                            $step = 2;
                            $_SESSION['login_phone'] = $clean_phone;
                        } else {
                            $error = 'فشل في إرسال رمز التحقق. يرجى المحاولة مرة أخرى.';
                        }
                    } else {
                        $error = 'خدمة الرسائل غير متاحة حالياً';
                    }
                } else {
                    $error = 'رقم الهاتف غير مسجل أو غير مفعل. يرجى إنشاء حساب جديد.';
                }
            } catch (Exception $e) {
                $error = 'خطأ في النظام. يرجى المحاولة مرة أخرى.';
            }
        }
    } elseif (isset($_POST['otp_code'])) {
        // الخطوة الثانية: التحقق من OTP
        $otp_code = trim($_POST['otp_code']);
        $phone = $_SESSION['login_phone'] ?? '';
        
        if (empty($otp_code) || empty($phone)) {
            $error = 'يرجى إدخال رمز التحقق';
            $step = 2;
        } else {
            try {
                // التحقق من OTP
                $stmt = $pdo->prepare("SELECT * FROM users WHERE phone = ? AND otp_code = ? AND otp_expires_at > NOW() AND is_verified = TRUE");
                $stmt->execute([$phone, $otp_code]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user) {
                    // تسجيل الدخول
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['name'];
                    $_SESSION['user_phone'] = $user['phone'];
                    $_SESSION['is_admin'] = (bool)$user['is_admin'];
                    
                    // مسح OTP
                    $stmt = $pdo->prepare("UPDATE users SET otp_code = NULL, otp_expires_at = NULL WHERE id = ?");
                    $stmt->execute([$user['id']]);
                    
                    // مسح بيانات الجلسة المؤقتة
                    unset($_SESSION['login_phone']);
                    
                    header('Location: dashboard.php');
                    exit;
                } else {
                    $error = 'رمز التحقق غير صحيح أو منتهي الصلاحية';
                    $step = 2;
                }
            } catch (Exception $e) {
                $error = 'خطأ في النظام. يرجى المحاولة مرة أخرى.';
                $step = 2;
            }
        }
    }
}

// التحقق من وجود رقم هاتف في الجلسة للانتقال للخطوة الثانية
// فقط إذا لم يكن هناك طلب إعادة تعيين صريح
if (isset($_SESSION['login_phone']) && empty($_POST) && !isset($_GET['reset'])) {
    $step = 2;
}

// إعادة تعيين الجلسة إذا طُلب ذلك
if (isset($_GET['reset'])) {
    unset($_SESSION['login_phone']);
    $step = 1;
}

$page_title = 'تسجيل الدخول';
$hide_footer = true;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - FutureWay</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- International Telephone Input -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/css/intlTelInput.css">
</head>
<body>

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-rocket"></i>
                <h1>FutureWay</h1>
            </div>
            <h2><?= $step === 1 ? 'تسجيل الدخول' : 'تأكيد رقم الهاتف' ?></h2>
            <p class="text-muted">
                <?= $step === 1 ? 'أدخل رقم هاتفك لتسجيل الدخول' : 'أدخل رمز التحقق المرسل إلى واتساب' ?>
            </p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" class="login-form">
            <?php if ($step === 1): ?>
                <div class="form-group">
                    <label for="phone" class="form-label">
                        <i class="fas fa-phone me-2"></i>
                        رقم الهاتف
                    </label>
                    <input type="tel" class="form-control" id="phone" name="phone" required 
                           placeholder="أدخل رقم هاتفك">
                    <div class="form-text">سيتم إرسال رمز التحقق عبر واتساب</div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-lg w-100">
                    <i class="fas fa-paper-plane me-2"></i>
                    إرسال رمز التحقق
                </button>
            <?php else: ?>
                <div class="form-group">
                    <label for="otp_code" class="form-label">
                        <i class="fas fa-shield-alt me-2"></i>
                        رمز التحقق
                    </label>
                    <input type="text" class="form-control otp-input" id="otp_code" name="otp_code" 
                           maxlength="6" required autocomplete="off" placeholder="000000">
                    <div class="form-text">أدخل الرمز المكون من 6 أرقام</div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-lg w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
                
                <div class="text-center mt-3">
                    <a href="login.php?reset=1" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </a>
                </div>
            <?php endif; ?>
        </form>
        
        <div class="divider">
            <span>أو</span>
        </div>
        
        <div class="text-center">
            <p class="mb-2">ليس لديك حساب؟</p>
            <a href="register.php" class="btn btn-outline-primary">
                <i class="fas fa-user-plus me-2"></i>
                إنشاء حساب جديد
            </a>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="auth-footer">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <p class="mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    <strong>FutureWay</strong> - خدمات متابعة وأنظمة التقسيط
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> FutureWay. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>
</footer>

<style>
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-color: #e2e8f0;
    --text-muted: #64748b;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

body {
    font-family: 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;
}

.login-container {
    width: 100%;
    max-width: 450px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    padding: 40px;
    width: 100%;
    animation: fadeInUp 0.6s ease-out;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-left: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo h1 {
    color: var(--primary-color);
    font-weight: 700;
    margin: 0;
    font-size: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-header h2 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.login-header p {
    margin: 0;
    font-size: 0.95rem;
    color: var(--text-muted);
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.form-label i {
    color: var(--primary-color);
}

.form-control {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: var(--light-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
    background: white;
}

.otp-input {
    text-align: center;
    font-size: 24px;
    letter-spacing: 8px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 5px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 12px;
    padding: 15px 25px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: 12px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-outline-secondary {
    border: 2px solid var(--text-muted);
    color: var(--text-muted);
    border-radius: 12px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--text-muted);
    border-color: var(--text-muted);
    color: white;
}

.divider {
    text-align: center;
    margin: 30px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.divider span {
    background: white;
    padding: 0 20px;
    color: var(--text-muted);
    font-size: 0.9rem;
    position: relative;
}

.alert {
    border-radius: 12px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
    font-weight: 500;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: var(--danger-color);
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
}

.auth-footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 0;
    margin-top: 30px;
    width: 100%;
    color: white;
}

.auth-footer p {
    margin: 0;
    font-size: 0.9rem;
}

/* International Telephone Input Styling */
.iti {
    width: 100%;
}

.iti__country-list {
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.iti__selected-flag {
    border-radius: 12px 0 0 12px;
    background: var(--light-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .login-card {
        padding: 30px 20px;
        border-radius: 15px;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .logo i {
        font-size: 2rem;
    }

    .login-header h2 {
        font-size: 1.3rem;
    }

    .auth-footer {
        text-align: center;
    }

    .auth-footer .col-md-6:last-child {
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .otp-input {
        font-size: 20px;
        letter-spacing: 4px;
    }
}
</style>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/intlTelInput.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إعداد International Telephone Input
    const phoneInput = document.querySelector("#phone");
    let iti;

    if (phoneInput) {
        iti = window.intlTelInput(phoneInput, {
            initialCountry: "eg",
            preferredCountries: ["eg", "sa", "ae", "kw", "qa", "bh"],
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js",
            formatOnDisplay: true,
            nationalMode: false,
            autoPlaceholder: "aggressive"
        });

        // تنظيف رقم الهاتف عند الإرسال
        document.querySelector('.login-form').addEventListener('submit', function() {
            phoneInput.value = iti.getNumber().replace('+', '');
        });
    }

    // التركيز التلقائي على حقل OTP
    const otpInput = document.querySelector("#otp_code");
    if (otpInput) {
        otpInput.focus();

        // السماح بالأرقام فقط
        otpInput.addEventListener('input', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        // إرسال النموذج تلقائياً عند إدخال 6 أرقام
        otpInput.addEventListener('input', function(e) {
            if (this.value.length === 6) {
                setTimeout(() => {
                    document.querySelector('.login-form').submit();
                }, 500);
            }
        });

        // تحسين تجربة المستخدم - تقسيم الأرقام
        otpInput.addEventListener('keydown', function(e) {
            // السماح بمفاتيح التحكم
            if (e.key === 'Backspace' || e.key === 'Delete' || e.key === 'Tab' ||
                e.key === 'Escape' || e.key === 'Enter' || e.key === 'ArrowLeft' ||
                e.key === 'ArrowRight') {
                return;
            }

            // منع الأحرف غير الرقمية
            if (!/[0-9]/.test(e.key)) {
                e.preventDefault();
            }
        });
    }

    // تحسين تجربة المستخدم - إضافة تأثيرات بصرية
    const form = document.querySelector('.login-form');
    if (form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                submitBtn.disabled = true;
            }
        });
    }

    // إخفاء الرسائل تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
});
</script>

</body>
</html>
