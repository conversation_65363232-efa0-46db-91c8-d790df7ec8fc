<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requireAdmin();

$message = '';
$message_type = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create') {
        $name = sanitizeInput($_POST['name'] ?? '');
        $phone = cleanPhone($_POST['phone'] ?? '');
        $governorate = sanitizeInput($_POST['governorate'] ?? '');
        $country = sanitizeInput($_POST['country'] ?? '');
        $profession = sanitizeInput($_POST['profession'] ?? '');
        $national_id = sanitizeInput($_POST['national_id'] ?? '');
        
        if (empty($name) || empty($phone) || empty($governorate) || empty($country) || empty($profession)) {
            $message = 'جميع الحقول مطلوبة باستثناء الرقم القومي';
            $message_type = 'danger';
        } else {
            // التحقق من عدم وجود المستخدم
            if (getUserByPhone($phone)) {
                $message = 'رقم الهاتف مسجل بالفعل';
                $message_type = 'danger';
            } else {
                $user_id = createUser($name, $phone, $governorate, $country, $profession, $national_id);
                if ($user_id) {
                    $message = 'تم إنشاء المستخدم بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء إنشاء المستخدم';
                    $message_type = 'danger';
                }
            }
        }
    } elseif ($action === 'update') {
        $id = (int)($_POST['id'] ?? 0);
        $name = sanitizeInput($_POST['name'] ?? '');
        $phone = cleanPhone($_POST['phone'] ?? '');
        $governorate = sanitizeInput($_POST['governorate'] ?? '');
        $country = sanitizeInput($_POST['country'] ?? '');
        $profession = sanitizeInput($_POST['profession'] ?? '');
        $national_id = sanitizeInput($_POST['national_id'] ?? '');
        
        if (empty($name) || empty($phone) || empty($governorate) || empty($country) || empty($profession)) {
            $message = 'جميع الحقول مطلوبة باستثناء الرقم القومي';
            $message_type = 'danger';
        } else {
            $data = [
                'name' => $name,
                'phone' => $phone,
                'governorate' => $governorate,
                'country' => $country,
                'profession' => $profession,
                'national_id' => $national_id
            ];
            
            if (updateUserAdmin($id, $data)) {
                $message = 'تم تحديث بيانات المستخدم بنجاح';
                $message_type = 'success';
            } else {
                $message = 'حدث خطأ أثناء تحديث البيانات';
                $message_type = 'danger';
            }
        }
    } elseif ($action === 'delete') {
        $id = (int)($_POST['id'] ?? 0);
        $result = deleteUserAdmin($id);
        $message = $result['message'];
        $message_type = $result['success'] ? 'success' : 'danger';
    }
}

// جلب جميع المستخدمين
$users = getAllUsersAdmin();

$page_title = 'إدارة المستخدمين';
include __DIR__ . '/../includes/admin_header.php';
?>

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="page-title">إدارة المستخدمين</h1>
        </div>
        <div class="col-auto">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus me-2"></i>
                إضافة مستخدم جديد
            </button>
        </div>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
        <?= $message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>
            قائمة المستخدمين
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped data-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>الاسم</th>
                        <th>رقم الهاتف</th>
                        <th>المحافظة</th>
                        <th>الدولة</th>
                        <th>المهنة</th>
                        <th>خطط التقسيط</th>
                        <th>إجمالي الإنفاق</th>
                        <th>تاريخ التسجيل</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?= $user['id'] ?></td>
                            <td>
                                <strong><?= htmlspecialchars($user['name']) ?></strong>
                                <?php if ($user['national_id']): ?>
                                    <br><small class="text-muted">ر.ق: <?= htmlspecialchars($user['national_id']) ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-secondary"><?= htmlspecialchars($user['phone']) ?></span>
                            </td>
                            <td><?= htmlspecialchars($user['governorate']) ?></td>
                            <td><?= htmlspecialchars($user['country']) ?></td>
                            <td><?= htmlspecialchars($user['profession']) ?></td>
                            <td>
                                <span class="badge bg-info"><?= $user['installment_plans_count'] ?> خطة</span>
                            </td>
                            <td>
                                <strong class="text-success"><?= formatCurrency($user['total_spent']) ?></strong>
                            </td>
                            <td>
                                <small><?= date('Y/m/d', strtotime($user['created_at'])) ?></small>
                            </td>
                            <td>
                                <?php if ($user['is_verified']): ?>
                                    <span class="badge bg-success">مفعل</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">غير مفعل</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="viewUser(<?= $user['id'] ?>)" 
                                            data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                            onclick="editUser(<?= $user['id'] ?>)" 
                                            data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteUserConfirm(<?= $user['id'] ?>)" 
                                            data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="governorate" class="form-label">المحافظة *</label>
                                <select class="form-select select2" id="governorate" name="governorate" required>
                                    <option value="">اختر المحافظة</option>
                                    <option value="القاهرة">القاهرة</option>
                                    <option value="الجيزة">الجيزة</option>
                                    <option value="الإسكندرية">الإسكندرية</option>
                                    <option value="الدقهلية">الدقهلية</option>
                                    <option value="البحيرة">البحيرة</option>
                                    <option value="الفيوم">الفيوم</option>
                                    <option value="الغربية">الغربية</option>
                                    <option value="الإسماعيلية">الإسماعيلية</option>
                                    <option value="المنوفية">المنوفية</option>
                                    <option value="السويس">السويس</option>
                                    <option value="أسوان">أسوان</option>
                                    <option value="أسيوط">أسيوط</option>
                                    <option value="بني سويف">بني سويف</option>
                                    <option value="بورسعيد">بورسعيد</option>
                                    <option value="دمياط">دمياط</option>
                                    <option value="الشرقية">الشرقية</option>
                                    <option value="جنوب سيناء">جنوب سيناء</option>
                                    <option value="كفر الشيخ">كفر الشيخ</option>
                                    <option value="مطروح">مطروح</option>
                                    <option value="المنيا">المنيا</option>
                                    <option value="الوادي الجديد">الوادي الجديد</option>
                                    <option value="شمال سيناء">شمال سيناء</option>
                                    <option value="قنا">قنا</option>
                                    <option value="القليوبية">القليوبية</option>
                                    <option value="الأقصر">الأقصر</option>
                                    <option value="البحر الأحمر">البحر الأحمر</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="country" class="form-label">الدولة *</label>
                                <select class="form-select select2" id="country" name="country" required>
                                    <option value="">اختر الدولة</option>
                                    <option value="مصر" selected>مصر</option>
                                    <option value="السعودية">السعودية</option>
                                    <option value="الإمارات">الإمارات</option>
                                    <option value="الكويت">الكويت</option>
                                    <option value="قطر">قطر</option>
                                    <option value="البحرين">البحرين</option>
                                    <option value="عمان">عمان</option>
                                    <option value="الأردن">الأردن</option>
                                    <option value="لبنان">لبنان</option>
                                    <option value="سوريا">سوريا</option>
                                    <option value="العراق">العراق</option>
                                    <option value="فلسطين">فلسطين</option>
                                    <option value="ليبيا">ليبيا</option>
                                    <option value="تونس">تونس</option>
                                    <option value="الجزائر">الجزائر</option>
                                    <option value="المغرب">المغرب</option>
                                    <option value="السودان">السودان</option>
                                    <option value="اليمن">اليمن</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="profession" class="form-label">المهنة *</label>
                                <input type="text" class="form-control" id="profession" name="profession" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="national_id" class="form-label">الرقم القومي (اختياري)</label>
                                <input type="text" class="form-control" id="national_id" name="national_id">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل بيانات المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editUserForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_user_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_phone" class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="edit_phone" name="phone" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_governorate" class="form-label">المحافظة *</label>
                                <select class="form-select" id="edit_governorate" name="governorate" required>
                                    <option value="">اختر المحافظة</option>
                                    <option value="القاهرة">القاهرة</option>
                                    <option value="الجيزة">الجيزة</option>
                                    <option value="الإسكندرية">الإسكندرية</option>
                                    <option value="الدقهلية">الدقهلية</option>
                                    <option value="البحيرة">البحيرة</option>
                                    <option value="الفيوم">الفيوم</option>
                                    <option value="الغربية">الغربية</option>
                                    <option value="الإسماعيلية">الإسماعيلية</option>
                                    <option value="المنوفية">المنوفية</option>
                                    <option value="السويس">السويس</option>
                                    <option value="أسوان">أسوان</option>
                                    <option value="أسيوط">أسيوط</option>
                                    <option value="بني سويف">بني سويف</option>
                                    <option value="بورسعيد">بورسعيد</option>
                                    <option value="دمياط">دمياط</option>
                                    <option value="الشرقية">الشرقية</option>
                                    <option value="جنوب سيناء">جنوب سيناء</option>
                                    <option value="كفر الشيخ">كفر الشيخ</option>
                                    <option value="مطروح">مطروح</option>
                                    <option value="المنيا">المنيا</option>
                                    <option value="الوادي الجديد">الوادي الجديد</option>
                                    <option value="شمال سيناء">شمال سيناء</option>
                                    <option value="قنا">قنا</option>
                                    <option value="القليوبية">القليوبية</option>
                                    <option value="الأقصر">الأقصر</option>
                                    <option value="البحر الأحمر">البحر الأحمر</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_country" class="form-label">الدولة *</label>
                                <select class="form-select" id="edit_country" name="country" required>
                                    <option value="">اختر الدولة</option>
                                    <option value="مصر">مصر</option>
                                    <option value="السعودية">السعودية</option>
                                    <option value="الإمارات">الإمارات</option>
                                    <option value="الكويت">الكويت</option>
                                    <option value="قطر">قطر</option>
                                    <option value="البحرين">البحرين</option>
                                    <option value="عمان">عمان</option>
                                    <option value="الأردن">الأردن</option>
                                    <option value="لبنان">لبنان</option>
                                    <option value="سوريا">سوريا</option>
                                    <option value="العراق">العراق</option>
                                    <option value="فلسطين">فلسطين</option>
                                    <option value="ليبيا">ليبيا</option>
                                    <option value="تونس">تونس</option>
                                    <option value="الجزائر">الجزائر</option>
                                    <option value="المغرب">المغرب</option>
                                    <option value="السودان">السودان</option>
                                    <option value="اليمن">اليمن</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_profession" class="form-label">المهنة *</label>
                                <input type="text" class="form-control" id="edit_profession" name="profession" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_national_id" class="form-label">الرقم القومي (اختياري)</label>
                                <input type="text" class="form-control" id="edit_national_id" name="national_id">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View User Modal -->
<div class="modal fade" id="viewUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Delete User Form -->
<form method="POST" id="deleteUserForm" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="id" id="delete_user_id">
</form>

<script>
// إعداد intl-tel-input
document.addEventListener('DOMContentLoaded', function() {
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        window.intlTelInput(input, {
            initialCountry: "eg",
            preferredCountries: ["eg", "sa", "ae"],
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js"
        });
    });
});

// عرض تفاصيل المستخدم
function viewUser(userId) {
    fetch(`../api/get_user_details.php?id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUserDetails(data.user, data.installment_plans);
            } else {
                showError('فشل في تحميل بيانات المستخدم');
            }
        })
        .catch(error => {
            showError('حدث خطأ في الاتصال بالخادم');
        });
}

function displayUserDetails(user, installmentPlans) {
    const content = document.getElementById('userDetailsContent');
    
    let installmentPlansHtml = '';
    if (installmentPlans.length > 0) {
        installmentPlans.forEach(plan => {
            installmentPlansHtml += `
                <div class="card mb-2">
                    <div class="card-body">
                        <h6>${plan.service_name} - ${plan.plan_name}</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small>المبلغ الإجمالي: ${formatCurrency(plan.total_amount)}</small>
                            </div>
                            <div class="col-md-6">
                                <small>الحالة: <span class="badge bg-${plan.status === 'active' ? 'success' : 'secondary'}">${plan.status === 'active' ? 'نشط' : 'مكتمل'}</span></small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        installmentPlansHtml = '<p class="text-muted">لا توجد خطط تقسيط</p>';
    }
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>المعلومات الشخصية</h6>
                <table class="table table-borderless">
                    <tr><td><strong>الاسم:</strong></td><td>${user.name}</td></tr>
                    <tr><td><strong>رقم الهاتف:</strong></td><td>${user.phone}</td></tr>
                    <tr><td><strong>المحافظة:</strong></td><td>${user.governorate}</td></tr>
                    <tr><td><strong>الدولة:</strong></td><td>${user.country}</td></tr>
                    <tr><td><strong>المهنة:</strong></td><td>${user.profession}</td></tr>
                    ${user.national_id ? `<tr><td><strong>الرقم القومي:</strong></td><td>${user.national_id}</td></tr>` : ''}
                    <tr><td><strong>تاريخ التسجيل:</strong></td><td>${formatDate(user.created_at)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>خطط التقسيط</h6>
                ${installmentPlansHtml}
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('viewUserModal'));
    modal.show();
}

// تعديل المستخدم
function editUser(userId) {
    fetch(`../api/get_user_details.php?id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const user = data.user;
                document.getElementById('edit_user_id').value = user.id;
                document.getElementById('edit_name').value = user.name;
                document.getElementById('edit_phone').value = user.phone;
                document.getElementById('edit_governorate').value = user.governorate;
                document.getElementById('edit_country').value = user.country;
                document.getElementById('edit_profession').value = user.profession;
                document.getElementById('edit_national_id').value = user.national_id || '';
                
                const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
                modal.show();
            } else {
                showError('فشل في تحميل بيانات المستخدم');
            }
        })
        .catch(error => {
            showError('حدث خطأ في الاتصال بالخادم');
        });
}

// حذف المستخدم
function deleteUserConfirm(userId) {
    confirmDelete('هل أنت متأكد من حذف هذا المستخدم؟ سيتم حذف جميع البيانات المرتبطة به.')
        .then((result) => {
            if (result.isConfirmed) {
                document.getElementById('delete_user_id').value = userId;
                document.getElementById('deleteUserForm').submit();
            }
        });
}
</script>

<style>
/* Force dark theme for admin users page */
body {
    background-color: #121212 !important;
    color: #e0e0e0 !important;
}

.card {
    background-color: #1e1e1e !important;
    border: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.modal-content {
    background-color: #1e1e1e !important;
    border: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.modal-header {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%) !important;
    border-bottom: 1px solid #333333 !important;
}

.modal-body {
    background-color: #1e1e1e !important;
    color: #e0e0e0 !important;
}

.modal-footer {
    background-color: #1e1e1e !important;
    border-top: 1px solid #333333 !important;
}

.form-control, .form-select {
    background-color: #2a2a2a !important;
    border: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.form-control:focus, .form-select:focus {
    background-color: #2a2a2a !important;
    border-color: #6c7ce7 !important;
    color: #e0e0e0 !important;
    box-shadow: 0 0 0 0.2rem rgba(108, 124, 231, 0.25) !important;
}

.form-label {
    color: #e0e0e0 !important;
}

.table {
    background-color: #1e1e1e !important;
    color: #e0e0e0 !important;
}

.table th {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.table td {
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.table tbody tr:hover {
    background-color: #2a2a2a !important;
}

.btn-outline-primary {
    border-color: #6c7ce7 !important;
    color: #6c7ce7 !important;
}

.btn-outline-primary:hover {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
    color: white !important;
}

.btn-outline-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

.btn-outline-warning:hover {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #000 !important;
}

.btn-outline-danger {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
}

.btn-outline-danger:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #e0e0e0 !important;
}

p, span, div {
    color: #e0e0e0 !important;
}

.text-muted {
    color: #b0b0b0 !important;
}

.alert {
    background-color: #2a2a2a !important;
    border: 1px solid #333333 !important;
    color: #e0e0e0 !important;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-color: rgba(40, 167, 69, 0.3) !important;
    color: #86efac !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
    color: #fca5a5 !important;
}
</style>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>

