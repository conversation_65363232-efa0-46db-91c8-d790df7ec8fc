// WAWP Test Page JavaScript Functions

// عرض رسائل التنبيه
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid .row .col-12');
    const pageHeader = container.querySelector('.page-header');
    container.insertBefore(alertDiv, pageHeader.nextSibling);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// إرسال طلب AJAX
async function sendAjaxRequest(action, data = {}) {
    const formData = new FormData();
    formData.append('action', action);
    
    for (const [key, value] of Object.entries(data)) {
        formData.append(key, value);
    }
    
    try {
        const response = await fetch('../api/wawp_test.php', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('AJAX Error:', error);
        throw error;
    }
}

// اختبار الاتصال
async function testConnection() {
    const button = document.querySelector('button[onclick="testConnection()"]');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';
    
    try {
        const result = await sendAjaxRequest('test_connection');
        
        if (result.success) {
            showAlert('تم الاتصال بنجاح!', 'success');
        } else {
            showAlert(result.message || 'فشل في الاتصال', 'danger');
        }
        
        displayResult(result);
    } catch (error) {
        showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
    } finally {
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// فحص حالة Instance
async function checkInstanceStatus() {
    const button = document.querySelector('button[onclick="checkInstanceStatus()"]');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...';
    
    try {
        const result = await sendAjaxRequest('check_instance_status');
        
        if (result.success) {
            showAlert('تم فحص الحالة بنجاح', 'success');
        } else {
            showAlert(result.message || 'فشل في فحص الحالة', 'danger');
        }
        
        displayResult(result);
    } catch (error) {
        showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
    } finally {
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// الحصول على QR Code
async function getQRCode() {
    const button = document.querySelector('button[onclick="getQRCode()"]');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
    
    try {
        const result = await sendAjaxRequest('get_qr_code');
        
        if (result.success) {
            showAlert('تم الحصول على QR Code بنجاح', 'success');
        } else {
            showAlert(result.message || 'فشل في الحصول على QR Code', 'danger');
        }
        
        displayResult(result);
    } catch (error) {
        showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
    } finally {
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// إرسال رسالة تجريبية
async function sendTestMessage() {
    const phone = document.getElementById('test_phone').value;
    const message = document.getElementById('test_message').value;
    
    if (!phone || !message) {
        showAlert('يرجى إدخال رقم الهاتف والرسالة', 'warning');
        return;
    }
    
    const button = document.querySelector('button[onclick="sendTestMessage()"]');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    
    try {
        const result = await sendAjaxRequest('send_message', { phone, message });
        
        if (result.success) {
            showAlert('تم إرسال الرسالة بنجاح!', 'success');
            document.getElementById('test_phone').value = '';
            document.getElementById('test_message').value = 'مرحباً! هذه رسالة تجريبية من نظام FutureWay.';
        } else {
            showAlert(result.message || 'فشل في إرسال الرسالة', 'danger');
        }
        
        displayResult(result);
    } catch (error) {
        showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
    } finally {
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// إرسال OTP تجريبي
async function sendTestOTP() {
    const phone = document.getElementById('otp_phone').value;
    const code = document.getElementById('otp_code').value;
    
    if (!phone || !code) {
        showAlert('يرجى إدخال رقم الهاتف ورمز التحقق', 'warning');
        return;
    }
    
    const button = document.querySelector('button[onclick="sendTestOTP()"]');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    
    try {
        const result = await sendAjaxRequest('send_otp', { phone, code });
        
        if (result.success) {
            showAlert('تم إرسال رمز التحقق بنجاح!', 'success');
            // توليد رمز جديد
            document.getElementById('otp_code').value = Math.floor(100000 + Math.random() * 900000);
        } else {
            showAlert(result.message || 'فشل في إرسال رمز التحقق', 'danger');
        }
        
        displayResult(result);
    } catch (error) {
        showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
    } finally {
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// عرض نتائج الاختبار
function displayResult(result) {
    let resultCard = document.getElementById('result-card');
    
    if (!resultCard) {
        resultCard = document.createElement('div');
        resultCard.id = 'result-card';
        resultCard.className = 'col-md-6';
        
        const row = document.querySelector('.row:last-child');
        row.appendChild(resultCard);
    }
    
    const alertClass = result.success ? 'alert-success' : 'alert-danger';
    const statusText = result.success ? 'نجح' : 'فشل';
    
    let content = `
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>
                    نتيجة الاختبار
                </h5>
            </div>
            <div class="card-body">
                <div class="alert ${alertClass}">
                    <strong>الحالة:</strong> ${statusText}
                </div>
    `;
    
    if (result.message) {
        content += `<p><strong>الرسالة:</strong> ${result.message}</p>`;
    }
    
    if (result.qr_code) {
        content += `
            <div class="text-center">
                <p><strong>QR Code:</strong></p>
                <img src="${result.qr_code}" alt="QR Code" class="img-fluid" style="max-width: 200px;">
            </div>
        `;
    }
    
    if (result.data) {
        content += `
            <details class="mt-3">
                <summary><strong>تفاصيل إضافية</strong></summary>
                <pre class="mt-2 p-2 bg-light rounded">${JSON.stringify(result.data, null, 2)}</pre>
            </details>
        `;
    }
    
    content += `
            </div>
        </div>
    `;
    
    resultCard.innerHTML = content;
}

// تنظيف رقم الهاتف
function cleanPhoneNumber(input) {
    let value = input.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام

    // إضافة رمز مصر إذا لم يكن موجوداً
    if (value.length > 0 && !value.startsWith('20')) {
        if (value.startsWith('0')) {
            value = '20' + value.substring(1);
        } else if (value.length === 10) {
            value = '20' + value;
        }
    }

    input.value = value;
}

// إعداد الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تنظيف تلقائي لأرقام الهاتف
    const phoneInputs = document.querySelectorAll('input[type="text"][placeholder*="20"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', () => cleanPhoneNumber(input));
        input.addEventListener('blur', () => cleanPhoneNumber(input));
    });
    
    // إضافة تأكيد قبل إرسال الرسائل
    const sendButtons = document.querySelectorAll('button[type="submit"]');
    sendButtons.forEach(button => {
        const form = button.closest('form');
        if (form && form.querySelector('input[name="action"]')) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const action = form.querySelector('input[name="action"]').value;
                
                if (action === 'send_test_message') {
                    sendTestMessage();
                } else if (action === 'send_otp_test') {
                    sendTestOTP();
                }
            });
        }
    });
});
