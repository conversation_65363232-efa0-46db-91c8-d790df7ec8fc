<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

$service_id = (int)($_GET['id'] ?? 0);

if (!$service_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الخدمة مطلوب']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // التحقق من وجود حقل is_active في جدول services
    $stmt = $pdo->prepare("SHOW COLUMNS FROM services LIKE 'is_active'");
    $stmt->execute();
    $is_active_exists = $stmt->fetch();
    
    // جلب بيانات الخدمة
    if ($is_active_exists) {
        $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ? AND is_active = TRUE");
    } else {
        $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ?");
    }
    $stmt->execute([$service_id]);
    $service = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$service) {
        echo json_encode(['success' => false, 'message' => 'الخدمة غير موجودة']);
        exit();
    }
    
    // التحقق من وجود حقل is_active في جدول pricing_plans
    $stmt = $pdo->prepare("SHOW COLUMNS FROM pricing_plans LIKE 'is_active'");
    $stmt->execute();
    $pricing_is_active_exists = $stmt->fetch();
    
    // جلب خطط الأسعار
    if ($pricing_is_active_exists) {
        $stmt = $pdo->prepare("
            SELECT pp.*, COUNT(ip.id) as purchases_count
            FROM pricing_plans pp
            LEFT JOIN installment_plans ip ON pp.id = ip.pricing_plan_id
            WHERE pp.service_id = ? AND pp.is_active = TRUE
            GROUP BY pp.id
            ORDER BY pp.price ASC
        ");
    } else {
        $stmt = $pdo->prepare("
            SELECT pp.*, COUNT(ip.id) as purchases_count
            FROM pricing_plans pp
            LEFT JOIN installment_plans ip ON pp.id = ip.pricing_plan_id
            WHERE pp.service_id = ?
            GROUP BY pp.id
            ORDER BY pp.price ASC
        ");
    }
    $stmt->execute([$service_id]);
    $pricing_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'service' => $service,
        'pricing_plans' => $pricing_plans
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام'
    ]);
    error_log("خطأ في get_service_details_public.php: " . $e->getMessage());
}
?>
