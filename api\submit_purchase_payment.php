<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $user = getCurrentUser();
    $user_id = $user['id'];
    
    // استلام البيانات
    $gateway_id = (int)($_POST['gateway_id'] ?? 0);
    $amount = (float)($_POST['amount'] ?? 0);
    $payment_type = $_POST['payment_type'] ?? '';
    $plan_id = (int)($_POST['plan_id'] ?? 0);
    $purchase_payment_type = $_POST['purchase_payment_type'] ?? '';
    
    // التحقق من صحة البيانات
    if (!$gateway_id || !$amount || !$payment_type || !$plan_id || !$purchase_payment_type) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit();
    }
    
    if (!in_array($payment_type, ['down_payment', 'service_purchase'])) {
        echo json_encode(['success' => false, 'message' => 'نوع دفع غير صحيح']);
        exit();
    }
    
    if (!in_array($purchase_payment_type, ['cash', 'installment'])) {
        echo json_encode(['success' => false, 'message' => 'نوع شراء غير صحيح']);
        exit();
    }
    
    // التحقق من وجود بوابة الدفع
    $stmt = $pdo->prepare("SELECT * FROM payment_gateways WHERE id = ? AND is_active = TRUE");
    $stmt->execute([$gateway_id]);
    $gateway = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$gateway) {
        echo json_encode(['success' => false, 'message' => 'بوابة دفع غير صحيحة']);
        exit();
    }
    
    // التحقق من وجود الخطة
    $stmt = $pdo->prepare("
        SELECT pp.*, s.name as service_name, s.id as service_id
        FROM pricing_plans pp
        JOIN services s ON pp.service_id = s.id
        WHERE pp.id = ? AND pp.is_active = TRUE AND s.is_active = TRUE
    ");
    $stmt->execute([$plan_id]);
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$plan) {
        echo json_encode(['success' => false, 'message' => 'الخطة غير موجودة']);
        exit();
    }
    
    // معالجة رفع الصور (نفس الكود من submit_payment_proof.php)
    $uploaded_files = [];
    $upload_dir = '../uploads/payment_proofs/';
    
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if (isset($_FILES['payment_proofs']) && is_array($_FILES['payment_proofs']['name'])) {
        $file_count = count($_FILES['payment_proofs']['name']);
        
        if ($file_count > 3) {
            echo json_encode(['success' => false, 'message' => 'يمكن رفع 3 صور كحد أقصى']);
            exit();
        }
        
        $total_size = 0;
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff'];
        $max_file_size = 10 * 1024 * 1024; // 10 MB
        $max_total_size = 30 * 1024 * 1024; // 30 MB
        
        for ($i = 0; $i < $file_count; $i++) {
            if ($_FILES['payment_proofs']['error'][$i] === UPLOAD_ERR_OK) {
                $file_size = $_FILES['payment_proofs']['size'][$i];
                $total_size += $file_size;
                
                if ($file_size > $max_file_size) {
                    echo json_encode(['success' => false, 'message' => 'حجم الصورة يجب أن يكون أقل من 10 ميجابايت']);
                    exit();
                }
            }
        }
        
        if ($total_size > $max_total_size) {
            echo json_encode(['success' => false, 'message' => 'الحجم الإجمالي للصور يجب أن يكون أقل من 30 ميجابايت']);
            exit();
        }
        
        for ($i = 0; $i < $file_count; $i++) {
            if ($_FILES['payment_proofs']['error'][$i] === UPLOAD_ERR_OK) {
                $file_extension = strtolower(pathinfo($_FILES['payment_proofs']['name'][$i], PATHINFO_EXTENSION));
                
                if (!in_array($file_extension, $allowed_extensions)) {
                    echo json_encode(['success' => false, 'message' => 'نوع ملف غير مدعوم. يُسمح بالصور فقط']);
                    exit();
                }
                
                $filename = uniqid() . '_' . $i . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;
                
                if (move_uploaded_file($_FILES['payment_proofs']['tmp_name'][$i], $upload_path)) {
                    $uploaded_files[] = 'uploads/payment_proofs/' . $filename;
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في رفع إحدى الصور']);
                    exit();
                }
            }
        }
    }
    
    if (empty($uploaded_files)) {
        echo json_encode(['success' => false, 'message' => 'يجب رفع إثبات دفع واحد على الأقل']);
        exit();
    }
    
    // بدء المعاملة
    $pdo->beginTransaction();
    
    try {
        // إنشاء خطة التقسيط إذا كان الدفع بالتقسيط
        $installment_plan_id = null;
        if ($purchase_payment_type === 'installment') {
            // حساب قيم التقسيط مع الفائدة
            $base_price = $plan['price'];
            $total_interest = 0;
            if ($plan['interest_type'] === 'fixed') {
                $total_interest = $plan['interest_value'];
            } else {
                $total_interest = ($base_price * $plan['interest_value']) / 100;
            }
            $total_amount = $base_price + $total_interest;
            
            $down_payment = $plan['down_payment'];
            $remaining_amount = $total_amount - $down_payment;
            $installment_amount = $plan['installment_amount'];
            $installments_count = $plan['installments_count'];
            
            // إنشاء خطة التقسيط
            $stmt = $pdo->prepare("
                INSERT INTO installment_plans (
                    user_id, pricing_plan_id, total_amount, down_payment,
                    remaining_amount, installment_amount, installments_count,
                    purchase_date, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, CURDATE(), 'pending', NOW())
            ");
            
            $stmt->execute([
                $user_id, $plan_id, $total_amount, $down_payment,
                $remaining_amount, $installment_amount, $installments_count
            ]);
            
            $installment_plan_id = $pdo->lastInsertId();
            
            // إنشاء جدولة الأقساط
            for ($i = 1; $i <= $installments_count; $i++) {
                $due_date = date('Y-m-d', strtotime("+$i month"));
                
                $stmt = $pdo->prepare("
                    INSERT INTO payments (
                        installment_plan_id, installment_number, amount, due_date, status
                    ) VALUES (?, ?, ?, ?, 'pending')
                ");
                $stmt->execute([
                    $installment_plan_id, $i, $installment_amount, $due_date
                ]);
            }
        }
        
        // حفظ طلب الدفع
        $stmt = $pdo->prepare("
            INSERT INTO payment_requests 
            (user_id, installment_plan_id, payment_id, gateway_id, amount, payment_type, payment_proofs, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
        ");
        
        $payment_proofs_json = json_encode($uploaded_files);
        
        $stmt->execute([
            $user_id, $installment_plan_id, null, $gateway_id, $amount, $payment_type, $payment_proofs_json
        ]);
        
        // إنشاء سجل الشراء (في حالة الانتظار)
        $stmt = $pdo->prepare("
            INSERT INTO purchases (
                user_id, pricing_plan_id, service_id, total_amount, 
                payment_method, status, purchase_date
            ) VALUES (?, ?, ?, ?, ?, 'active', CURDATE())
        ");
        
        $total_purchase_amount = ($purchase_payment_type === 'installment') ? 
            ($base_price + $total_interest) : $plan['price'];
        
        $stmt->execute([
            $user_id, $plan_id, $plan['service_id'], $total_purchase_amount, $purchase_payment_type
        ]);
        
        $pdo->commit();
        
        $message = ($purchase_payment_type === 'installment') ? 
            'تم إرسال طلب دفع المقدم بنجاح. سيتم مراجعته من قبل الإدارة وتفعيل خطة التقسيط بعد الموافقة.' :
            'تم إرسال طلب الدفع بنجاح. سيتم مراجعته من قبل الإدارة وتفعيل الخدمة بعد الموافقة.';
        
        echo json_encode([
            'success' => true, 
            'message' => $message
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        
        // حذف الملفات المرفوعة في حالة فشل المعاملة
        foreach ($uploaded_files as $file) {
            if (file_exists('../' . $file)) {
                unlink('../' . $file);
            }
        }
        
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Error in submit_purchase_payment.php: " . $e->getMessage());
    
    // حذف الملفات المرفوعة في حالة حدوث خطأ
    if (isset($uploaded_files)) {
        foreach ($uploaded_files as $file) {
            if (file_exists('../' . $file)) {
                unlink('../' . $file);
            }
        }
    }
    
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام: ' . $e->getMessage()]);
}
?>
