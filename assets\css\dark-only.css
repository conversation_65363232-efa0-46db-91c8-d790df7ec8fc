/* FutureWay - Dark Mode Only Styles */

/* Force dark theme on all elements */
* {
    color-scheme: dark;
}

/* Global text colors */
body, html {
    background-color: #121212 !important;
    color: #e0e0e0 !important;
}

/* Headers */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    color: #e0e0e0 !important;
}

/* Paragraphs and text */
p, span, div, li, td, th {
    color: #e0e0e0 !important;
}

/* Bootstrap text utilities override */
.text-dark {
    color: #e0e0e0 !important;
}

.text-muted {
    color: #b0b0b0 !important;
}

.text-secondary {
    color: #b0b0b0 !important;
}

.text-light {
    color: #e0e0e0 !important;
}

.text-white {
    color: #ffffff !important;
}

/* Bootstrap background utilities override */
.bg-white {
    background-color: #1e1e1e !important;
}

.bg-light {
    background-color: #1e1e1e !important;
}

.bg-secondary {
    background-color: #2a2a2a !important;
}

.bg-dark {
    background-color: #121212 !important;
}

/* Cards and containers */
.card {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.card-header {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.card-body {
    background-color: #1e1e1e !important;
    color: #e0e0e0 !important;
}

.card-footer {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.card-title {
    color: #e0e0e0 !important;
}

.card-text {
    color: #b0b0b0 !important;
}

/* Containers */
.container, .container-fluid {
    color: #e0e0e0 !important;
}

/* Sections */
section {
    background-color: #121212 !important;
    color: #e0e0e0 !important;
}

/* Navigation */
.navbar {
    background-color: #1a1a1a !important;
}

.navbar-brand {
    color: #ffffff !important;
}

.nav-link {
    color: #e0e0e0 !important;
}

.nav-link:hover {
    color: #ffffff !important;
}

/* Buttons */
.btn {
    border-color: #333333 !important;
}

.btn-light {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.btn-light:hover {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
    color: #ffffff !important;
}

.btn-outline-light {
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.btn-outline-light:hover {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
    color: #ffffff !important;
}

/* Forms */
.form-control {
    background-color: #2a2a2a !important;
    border-color: #404040 !important;
    color: #e0e0e0 !important;
}

.form-control:focus {
    background-color: #2a2a2a !important;
    border-color: #6c7ce7 !important;
    color: #e0e0e0 !important;
    box-shadow: 0 0 0 0.2rem rgba(108, 124, 231, 0.25) !important;
}

.form-label {
    color: #e0e0e0 !important;
}

/* Tables */
.table {
    background-color: #1e1e1e !important;
    color: #e0e0e0 !important;
}

.table th {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.table td {
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #2a2a2a !important;
}

/* Alerts */
.alert {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
    border-color: rgba(23, 162, 184, 0.3) !important;
    color: #7dd3fc !important;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-color: rgba(40, 167, 69, 0.3) !important;
    color: #86efac !important;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-color: rgba(255, 193, 7, 0.3) !important;
    color: #fde047 !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
    color: #fca5a5 !important;
}

/* Modals */
.modal-content {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.modal-header {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.modal-body {
    background-color: #1e1e1e !important;
    color: #e0e0e0 !important;
}

.modal-footer {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
}

/* Dropdowns */
.dropdown-menu {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
}

.dropdown-item {
    color: #e0e0e0 !important;
}

.dropdown-item:hover {
    background-color: #6c7ce7 !important;
    color: #ffffff !important;
}

/* Pagination */
.page-link {
    background-color: #2a2a2a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.page-link:hover {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
    color: #ffffff !important;
}

.page-item.active .page-link {
    background-color: #6c7ce7 !important;
    border-color: #6c7ce7 !important;
}

/* Breadcrumbs */
.breadcrumb {
    background-color: #2a2a2a !important;
}

.breadcrumb-item a {
    color: #6c7ce7 !important;
}

.breadcrumb-item.active {
    color: #b0b0b0 !important;
}

/* List groups */
.list-group-item {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.list-group-item:hover {
    background-color: #2a2a2a !important;
}

/* Progress bars */
.progress {
    background-color: #2a2a2a !important;
}

.progress-bar {
    background-color: #6c7ce7 !important;
}

/* Badges */
.badge {
    background-color: #6c7ce7 !important;
    color: #ffffff !important;
}

/* Links */
a {
    color: #6c7ce7 !important;
}

a:hover {
    color: #8b5fbf !important;
}

/* Small text */
small, .small {
    color: #b0b0b0 !important;
}

/* Lead text */
.lead {
    color: #e0e0e0 !important;
}

/* Display headings */
.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    color: #e0e0e0 !important;
}

/* Borders */
.border {
    border-color: #333333 !important;
}

.border-top {
    border-top-color: #333333 !important;
}

.border-bottom {
    border-bottom-color: #333333 !important;
}

.border-left {
    border-left-color: #333333 !important;
}

.border-right {
    border-right-color: #333333 !important;
}
