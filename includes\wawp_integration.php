<?php
require_once __DIR__ . '/../config/wawp.php';
require_once __DIR__ . '/../config/database.php';

class WAWPIntegration {
    private $wawp;
    private $db;
    
    public function __construct() {
        $this->wawp = new WAWP();
        $this->db = new Database();
    }
    
    /**
     * إرسال رمز OTP للمستخدم
     */
    public function sendOTPCode($phone, $code, $type = 'login') {
        try {
            $message = $this->getOTPMessage($code, $type);
            $result = $this->wawp->sendMessage($phone, $message);
            
            // تسجيل محاولة الإرسال
            $this->logMessage($phone, $message, $result['success'] ? 'sent' : 'failed');
            
            return $result;
        } catch (Exception $e) {
            error_log("WAWP OTP Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'فشل في إرسال رمز التحقق'];
        }
    }
    
    /**
     * إرسال رسالة ترحيب للمستخدم الجديد
     */
    public function sendWelcomeMessage($phone, $name) {
        try {
            $message = "مرحباً {$name}! 🎉\n\nأهلاً بك في FutureWay، منصتك المتخصصة في خدمات التقسيط المرن.\n\nيمكنك الآن:\n✅ تصفح خدماتنا المتنوعة\n✅ اختيار خطط التقسيط المناسبة\n✅ متابعة مدفوعاتك بسهولة\n\nنحن هنا لمساعدتك في تحقيق أهدافك! 💪";
            
            $result = $this->wawp->sendMessage($phone, $message);
            $this->logMessage($phone, $message, $result['success'] ? 'sent' : 'failed');
            
            return $result;
        } catch (Exception $e) {
            error_log("WAWP Welcome Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'فشل في إرسال رسالة الترحيب'];
        }
    }
    
    /**
     * إرسال تأكيد إنشاء خطة تقسيط
     */
    public function sendInstallmentCreated($phone, $planDetails) {
        try {
            $message = "تم إنشاء خطة التقسيط بنجاح! ✅\n\n";
            $message .= "📋 تفاصيل الخطة:\n";
            $message .= "🔹 الخدمة: {$planDetails['service_name']}\n";
            $message .= "🔹 خطة السعر: {$planDetails['plan_name']}\n";
            $message .= "🔹 المبلغ الإجمالي: " . formatCurrency($planDetails['total_amount']) . "\n";
            $message .= "🔹 المقدم: " . formatCurrency($planDetails['down_payment']) . "\n";
            $message .= "🔹 عدد الأقساط: {$planDetails['installments_count']}\n";
            $message .= "🔹 قيمة القسط: " . formatCurrency($planDetails['installment_amount']) . "\n\n";
            $message .= "📅 موعد أول قسط: {$planDetails['first_payment_date']}\n\n";
            $message .= "يمكنك متابعة مدفوعاتك من خلال لوحة التحكم في الموقع.";
            
            $result = $this->wawp->sendMessage($phone, $message);
            $this->logMessage($phone, $message, $result['success'] ? 'sent' : 'failed');
            
            return $result;
        } catch (Exception $e) {
            error_log("WAWP Installment Created Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'فشل في إرسال تأكيد خطة التقسيط'];
        }
    }
    
    /**
     * إرسال تذكير بموعد استحقاق قسط
     */
    public function sendPaymentReminder($phone, $paymentDetails) {
        try {
            $message = "تذكير: موعد استحقاق قسط ⏰\n\n";
            $message .= "📋 تفاصيل القسط:\n";
            $message .= "🔹 الخدمة: {$paymentDetails['service_name']}\n";
            $message .= "🔹 رقم القسط: {$paymentDetails['installment_number']}\n";
            $message .= "🔹 المبلغ: " . formatCurrency($paymentDetails['amount']) . "\n";
            $message .= "🔹 تاريخ الاستحقاق: {$paymentDetails['due_date']}\n\n";
            
            if ($paymentDetails['days_until_due'] > 0) {
                $message .= "⏳ متبقي {$paymentDetails['days_until_due']} يوم على موعد الاستحقاق\n\n";
            } else {
                $message .= "⚠️ القسط متأخر بـ " . abs($paymentDetails['days_until_due']) . " يوم\n\n";
            }
            
            $message .= "يرجى سداد القسط في أقرب وقت ممكن.\n";
            $message .= "للاستفسار، تواصل معنا عبر الموقع.";
            
            $result = $this->wawp->sendMessage($phone, $message);
            $this->logMessage($phone, $message, $result['success'] ? 'sent' : 'failed');
            
            return $result;
        } catch (Exception $e) {
            error_log("WAWP Payment Reminder Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'فشل في إرسال تذكير الدفع'];
        }
    }
    
    /**
     * إرسال تأكيد استلام دفعة
     */
    public function sendPaymentConfirmation($phone, $paymentDetails) {
        try {
            $message = "تم استلام دفعتك بنجاح! ✅\n\n";
            $message .= "📋 تفاصيل الدفعة:\n";
            $message .= "🔹 الخدمة: {$paymentDetails['service_name']}\n";
            $message .= "🔹 رقم القسط: {$paymentDetails['installment_number']}\n";
            $message .= "🔹 المبلغ: " . formatCurrency($paymentDetails['amount']) . "\n";
            $message .= "🔹 تاريخ الدفع: {$paymentDetails['paid_date']}\n\n";
            
            if ($paymentDetails['remaining_payments'] > 0) {
                $message .= "📊 متبقي {$paymentDetails['remaining_payments']} قسط\n";
                $message .= "📅 موعد القسط التالي: {$paymentDetails['next_payment_date']}\n\n";
            } else {
                $message .= "🎉 تهانينا! تم سداد جميع الأقساط بنجاح\n\n";
            }
            
            $message .= "شكراً لثقتك في FutureWay! 💙";
            
            $result = $this->wawp->sendMessage($phone, $message);
            $this->logMessage($phone, $message, $result['success'] ? 'sent' : 'failed');
            
            return $result;
        } catch (Exception $e) {
            error_log("WAWP Payment Confirmation Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'فشل في إرسال تأكيد الدفع'];
        }
    }
    
    /**
     * إرسال تأكيد اكتمال خطة التقسيط
     */
    public function sendInstallmentCompleted($phone, $planDetails) {
        try {
            $message = "🎉 تهانينا! تم إكمال خطة التقسيط بنجاح!\n\n";
            $message .= "📋 تفاصيل الخطة المكتملة:\n";
            $message .= "🔹 الخدمة: {$planDetails['service_name']}\n";
            $message .= "🔹 خطة السعر: {$planDetails['plan_name']}\n";
            $message .= "🔹 المبلغ الإجمالي: " . formatCurrency($planDetails['total_amount']) . "\n";
            $message .= "🔹 عدد الأقساط: {$planDetails['installments_count']}\n";
            $message .= "🔹 تاريخ البداية: {$planDetails['start_date']}\n";
            $message .= "🔹 تاريخ الإكمال: {$planDetails['completion_date']}\n\n";
            $message .= "شكراً لالتزامك وثقتك في FutureWay! 💙\n";
            $message .= "نتطلع لخدمتك مرة أخرى في المستقبل.";
            
            $result = $this->wawp->sendMessage($phone, $message);
            $this->logMessage($phone, $message, $result['success'] ? 'sent' : 'failed');
            
            return $result;
        } catch (Exception $e) {
            error_log("WAWP Installment Completed Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'فشل في إرسال تأكيد إكمال التقسيط'];
        }
    }
    
    /**
     * إرسال تذكيرات جماعية للمدفوعات المتأخرة
     */
    public function sendBulkOverdueReminders() {
        try {
            $pdo = $this->db->getConnection();
            
            // جلب المدفوعات المتأخرة
            $stmt = $pdo->prepare("
                SELECT p.*, u.name as user_name, u.phone as user_phone,
                       s.name as service_name, pp.name as plan_name,
                       DATEDIFF(CURDATE(), p.due_date) as days_overdue
                FROM payments p
                JOIN installment_plans ip ON p.installment_plan_id = ip.id
                JOIN users u ON ip.user_id = u.id
                JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
                JOIN services s ON pp.service_id = s.id
                WHERE p.status = 'overdue'
                AND DATEDIFF(CURDATE(), p.due_date) IN (1, 3, 7, 14, 30)
            ");
            $stmt->execute();
            $overduePayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $sent_count = 0;
            foreach ($overduePayments as $payment) {
                $paymentDetails = [
                    'service_name' => $payment['service_name'],
                    'installment_number' => $payment['installment_number'],
                    'amount' => $payment['amount'],
                    'due_date' => date('Y/m/d', strtotime($payment['due_date'])),
                    'days_until_due' => -$payment['days_overdue']
                ];
                
                $result = $this->sendPaymentReminder($payment['user_phone'], $paymentDetails);
                if ($result['success']) {
                    $sent_count++;
                }
                
                // تأخير قصير بين الرسائل
                usleep(500000); // 0.5 ثانية
            }
            
            return ['success' => true, 'count' => $sent_count];
        } catch (Exception $e) {
            error_log("WAWP Bulk Reminders Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'فشل في إرسال التذكيرات الجماعية'];
        }
    }
    
    /**
     * اختبار الاتصال مع WAWP
     */
    public function testConnection() {
        return $this->wawp->testConnection();
    }
    
    /**
     * تسجيل الرسائل المرسلة
     */
    private function logMessage($phone, $message, $status) {
        try {
            $pdo = $this->db->getConnection();
            $stmt = $pdo->prepare("
                INSERT INTO message_logs (phone, message, status, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$phone, $message, $status]);
        } catch (Exception $e) {
            error_log("Message Log Error: " . $e->getMessage());
        }
    }
    
    /**
     * إنشاء رسالة OTP
     */
    private function getOTPMessage($code, $type) {
        $messages = [
            'login' => "رمز تسجيل الدخول في FutureWay: {$code}\n\nهذا الرمز صالح لمدة 10 دقائق فقط.\nلا تشارك هذا الرمز مع أي شخص.",
            'register' => "رمز التحقق لإنشاء حساب في FutureWay: {$code}\n\nهذا الرمز صالح لمدة 10 دقائق فقط.\nلا تشارك هذا الرمز مع أي شخص.",
            'reset' => "رمز إعادة تعيين كلمة المرور في FutureWay: {$code}\n\nهذا الرمز صالح لمدة 10 دقائق فقط.\nلا تشارك هذا الرمز مع أي شخص."
        ];
        
        return $messages[$type] ?? $messages['login'];
    }
}

// دوال مساعدة للاستخدام السريع
function sendWAWPOTP($phone, $code, $type = 'login') {
    $wawp = new WAWPIntegration();
    return $wawp->sendOTPCode($phone, $code, $type);
}

function sendWAWPWelcome($phone, $name) {
    $wawp = new WAWPIntegration();
    return $wawp->sendWelcomeMessage($phone, $name);
}

function sendWAWPInstallmentCreated($phone, $planDetails) {
    $wawp = new WAWPIntegration();
    return $wawp->sendInstallmentCreated($phone, $planDetails);
}

function sendWAWPPaymentReminder($phone, $paymentDetails) {
    $wawp = new WAWPIntegration();
    return $wawp->sendPaymentReminder($phone, $paymentDetails);
}

function sendWAWPPaymentConfirmation($phone, $paymentDetails) {
    $wawp = new WAWPIntegration();
    return $wawp->sendPaymentConfirmation($phone, $paymentDetails);
}

function sendWAWPInstallmentCompleted($phone, $planDetails) {
    $wawp = new WAWPIntegration();
    return $wawp->sendInstallmentCompleted($phone, $planDetails);
}
?>

