# FutureWay - إعدادات Apache

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# صفحات الأخطاء المخصصة
ErrorDocument 404 /futureway_project/404.php
ErrorDocument 403 /futureway_project/404.php
ErrorDocument 500 /futureway_project/404.php

# إعادة توجيه الصفحات المفقودة الشائعة
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^admin/?$ admin/index.php [L]

# حماية الملفات الحساسة
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".env">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول للمجلدات الحساسة
RedirectMatch 404 /\.git
RedirectMatch 404 /\.htaccess
RedirectMatch 404 /config/
RedirectMatch 404 /includes/

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تحسين التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# منع عرض قائمة الملفات
Options -Indexes

# تحسين الأداء
<IfModule mod_rewrite.c>
    # إزالة www (اختياري)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ http://%1/$1 [R=301,L]
    
    # فرض HTTPS (اختياري - قم بإلغاء التعليق عند استخدام SSL)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# إعدادات PHP (إذا كان مسموحاً)
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_flag display_errors Off
    php_flag log_errors On
</IfModule>
