<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/admin_auth.php';

$message = '';
$message_type = '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'delete') {
        $plan_id = (int)($_POST['id'] ?? 0);

        if ($plan_id > 0) {
            try {
                $db = new Database();
                $pdo = $db->getConnection();

                // التحقق من وجود خطط تقسيط مرتبطة
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM installment_plans WHERE pricing_plan_id = ?");
                $stmt->execute([$plan_id]);
                $installment_count = $stmt->fetchColumn();

                if ($installment_count > 0) {
                    $message = 'لا يمكن حذف خطة السعر لأنها مرتبطة بخطط تقسيط موجودة';
                    $message_type = 'danger';
                } else {
                    $stmt = $pdo->prepare("DELETE FROM pricing_plans WHERE id = ?");
                    if ($stmt->execute([$plan_id])) {
                        $message = 'تم حذف خطة السعر بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'فشل في حذف خطة السعر';
                        $message_type = 'danger';
                    }
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ: ' . $e->getMessage();
                $message_type = 'danger';
            }
        }
    }

    elseif ($action === 'update') {
        $plan_id = (int)($_POST['plan_id'] ?? 0);
        $service_id = (int)($_POST['service_id'] ?? 0);
        $name = sanitizeInput($_POST['name'] ?? '');
        $features = sanitizeInput($_POST['description'] ?? '');
        $price = (float)($_POST['price'] ?? 0);
        $installment_enabled = isset($_POST['installment_enabled']) ? 1 : 0;
        $installments_count = (int)($_POST['installments_count'] ?? 0);
        $down_payment_percentage = (float)($_POST['down_payment_percentage'] ?? 0);
        $interest_type = $_POST['interest_type'] ?? 'percentage';
        $interest_value = (float)($_POST['interest_value'] ?? 0);
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        if (empty($name) || empty($features) || $price <= 0 || $service_id <= 0 || $plan_id <= 0) {
            $message = 'جميع الحقول مطلوبة والسعر يجب أن يكون أكبر من صفر';
            $message_type = 'danger';
        } else {
            try {
                $db = new Database();
                $pdo = $db->getConnection();

                // حساب الفائدة والمقدم ومبلغ القسط
                $total_interest = 0;
                $down_payment = 0;
                $installment_amount = 0;

                if ($installment_enabled && $installments_count > 0) {
                    if ($interest_type === 'fixed') {
                        $total_interest = $interest_value;
                    } else {
                        $total_interest = ($price * $interest_value) / 100;
                    }

                    $total_price_with_interest = $price + $total_interest;
                    $down_payment = ($total_price_with_interest * $down_payment_percentage) / 100;
                    $remaining_amount = $total_price_with_interest - $down_payment;
                    $installment_amount = $remaining_amount / $installments_count;
                }

                $sql = "UPDATE pricing_plans SET
                        service_id = ?, name = ?, features = ?, price = ?, allow_installment = ?,
                        installments_count = ?, installment_amount = ?, interest_type = ?,
                        interest_value = ?, down_payment = ?, is_active = ?
                        WHERE id = ?";
                $stmt = $pdo->prepare($sql);

                if ($stmt->execute([$service_id, $name, $features, $price, $installment_enabled,
                                  $installments_count, $installment_amount, $interest_type,
                                  $interest_value, $down_payment, $is_active, $plan_id])) {
                    $message = 'تم تحديث خطة السعر بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تحديث خطة السعر';
                    $message_type = 'danger';
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ: ' . $e->getMessage();
                $message_type = 'danger';
            }
        }
    }

    elseif ($action === 'create') {
        $service_id = (int)($_POST['service_id'] ?? 0);
        $name = sanitizeInput($_POST['name'] ?? '');
        $features = sanitizeInput($_POST['description'] ?? ''); // استخدام features بدلاً من description
        $price = (float)($_POST['price'] ?? 0);
        $installment_enabled = isset($_POST['installment_enabled']) ? 1 : 0;
        $installments_count = (int)($_POST['installments_count'] ?? 0);
        $down_payment_percentage = (float)($_POST['down_payment_percentage'] ?? 0);
        $interest_type = $_POST['interest_type'] ?? 'percentage';
        $interest_value = (float)($_POST['interest_value'] ?? 0);

        if (empty($name) || empty($features) || $price <= 0 || $service_id <= 0) {
            $message = 'جميع الحقول مطلوبة والسعر يجب أن يكون أكبر من صفر';
            $message_type = 'danger';
        } else {
            try {
                $db = new Database();
                $pdo = $db->getConnection();

                // حساب الفائدة والمقدم ومبلغ القسط
                $total_interest = 0;
                $down_payment = 0;
                $installment_amount = 0;

                if ($installment_enabled && $installments_count > 0) {
                    // الخطوة 1: حساب الفائدة وإضافتها للسعر الأصلي
                    if ($interest_type === 'fixed') {
                        $total_interest = $interest_value;
                    } else {
                        $total_interest = ($price * $interest_value) / 100;
                    }

                    // الخطوة 2: السعر الكلي مع الفائدة
                    $total_price_with_interest = $price + $total_interest;

                    // الخطوة 3: حساب المقدم من السعر الكلي مع الفائدة
                    $down_payment = ($total_price_with_interest * $down_payment_percentage) / 100;

                    // الخطوة 4: المبلغ المتبقي للتقسيط
                    $remaining_amount = $total_price_with_interest - $down_payment;

                    // الخطوة 5: مبلغ القسط الواحد
                    $installment_amount = $remaining_amount / $installments_count;
                }

                $sql = "INSERT INTO pricing_plans (service_id, name, features, price, allow_installment,
                        installments_count, installment_amount, interest_type, interest_value, down_payment)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $pdo->prepare($sql);

                if ($stmt->execute([$service_id, $name, $features, $price, $installment_enabled,
                                  $installments_count, $installment_amount, $interest_type, $interest_value, $down_payment])) {
                    $message = 'تم إنشاء خطة السعر بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء إنشاء خطة السعر';
                    $message_type = 'danger';
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ: ' . $e->getMessage();
                $message_type = 'danger';
            }
        }
    }
}

// جلب الخدمات
$services = [];
try {
    $db = new Database();
    $pdo = $db->getConnection();

    $stmt = $pdo->prepare("SELECT id, name FROM services WHERE is_active = TRUE ORDER BY name");
    $stmt->execute();
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $services = [];
}

// جلب خطط الأسعار
$pricing_plans = [];
try {
    $stmt = $pdo->prepare("
        SELECT pp.*, s.name as service_name
        FROM pricing_plans pp
        LEFT JOIN services s ON pp.service_id = s.id
        ORDER BY pp.created_at DESC
    ");
    $stmt->execute();
    $pricing_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $pricing_plans = [];
}

$page_title = 'إدارة خطط الأسعار';
include __DIR__ . '/../includes/admin_header.php';
?>

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="page-title">إدارة خطط الأسعار</h1>
        </div>
        <div class="col-auto">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPricingPlanModal">
                <i class="fas fa-plus me-2"></i>إضافة خطة سعر جديدة
            </button>
        </div>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
        <?= $message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-tags me-2"></i>قائمة خطط الأسعار
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped data-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>الخدمة</th>
                        <th>اسم الخطة</th>
                        <th>السعر</th>
                        <th>التقسيط</th>
                        <th>المقدم</th>
                        <th>عدد الأقساط</th>
                        <th>مبلغ القسط</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pricing_plans as $plan): ?>
                        <tr>
                            <td><?= $plan['id'] ?></td>
                            <td><?= htmlspecialchars($plan['service_name']) ?></td>
                            <td><?= htmlspecialchars($plan['name']) ?></td>
                            <td><?= number_format($plan['price'], 2) ?> جنيه</td>
                            <td>
                                <span class="badge bg-<?= $plan['allow_installment'] ? 'success' : 'secondary' ?>">
                                    <?= $plan['allow_installment'] ? 'متاح' : 'غير متاح' ?>
                                </span>
                            </td>
                            <td><?= $plan['allow_installment'] && $plan['down_payment'] > 0 ? number_format($plan['down_payment'], 2) . ' جنيه' : '-' ?></td>
                            <td><?= $plan['allow_installment'] ? $plan['installments_count'] : '-' ?></td>
                            <td><?= $plan['allow_installment'] ? number_format($plan['installment_amount'], 2) . ' جنيه' : '-' ?></td>
                            <td><?= date('Y/m/d', strtotime($plan['created_at'])) ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                            onclick="viewPlan(<?= $plan['id'] ?>)"
                                            data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning"
                                            onclick="editPlan(<?= $plan['id'] ?>)"
                                            data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deletePlan(<?= $plan['id'] ?>)"
                                            data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Pricing Plan Modal -->
<div class="modal fade" id="editPricingPlanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل خطة السعر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editPricingPlanForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="plan_id" id="edit_plan_id">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_service_id" class="form-label">الخدمة</label>
                            <select class="form-select" id="edit_service_id" name="service_id" required>
                                <option value="">اختر الخدمة</option>
                                <?php foreach ($services as $service): ?>
                                    <option value="<?= $service['id'] ?>"><?= htmlspecialchars($service['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="edit_name" class="form-label">اسم الخطة</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">وصف الخطة</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_price" class="form-label">السعر (جنيه)</label>
                            <input type="number" class="form-control" id="edit_price" name="price" min="0" step="0.01" required onchange="calculateEditInstallment()">
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="edit_installment_enabled" name="installment_enabled" onchange="toggleEditInstallmentOptions()">
                                <label class="form-check-label" for="edit_installment_enabled">
                                    تفعيل التقسيط
                                </label>
                            </div>
                        </div>
                    </div>

                    <div id="edit_installment_options" style="display: none;">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_installments_count" class="form-label">عدد الأقساط</label>
                                <input type="number" class="form-control" id="edit_installments_count" name="installments_count" min="1" max="60" onchange="calculateEditInstallment()">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="edit_down_payment_percentage" class="form-label">نسبة المقدم (%)</label>
                                <input type="number" class="form-control" id="edit_down_payment_percentage" name="down_payment_percentage" min="0" max="100" step="0.01" onchange="calculateEditInstallment()">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_interest_type" class="form-label">نوع الفائدة</label>
                                <select class="form-select" id="edit_interest_type" name="interest_type" onchange="calculateEditInstallment()">
                                    <option value="percentage">نسبة مئوية</option>
                                    <option value="fixed">مبلغ ثابت</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="edit_interest_value" class="form-label">قيمة الفائدة</label>
                                <input type="number" class="form-control" id="edit_interest_value" name="interest_value" min="0" step="0.01" onchange="calculateEditInstallment()">
                                <small class="form-text text-muted" id="edit_interest_help">أدخل النسبة المئوية أو المبلغ الثابت</small>
                            </div>
                        </div>

                        <div id="edit_installment_calculation" class="alert alert-info" style="display: none;">
                            <h6>حساب التقسيط:</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>السعر مع الفائدة:</strong>
                                    <div id="edit_total_with_interest">0 جنيه</div>
                                </div>
                                <div class="col-md-3">
                                    <strong>المقدم:</strong>
                                    <div id="edit_down_payment_amount">0 جنيه</div>
                                </div>
                                <div class="col-md-3">
                                    <strong>المبلغ المتبقي:</strong>
                                    <div id="edit_remaining_amount">0 جنيه</div>
                                </div>
                                <div class="col-md-3">
                                    <strong>مبلغ القسط:</strong>
                                    <div id="edit_installment_amount">0 جنيه</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" checked>
                        <label class="form-check-label" for="edit_is_active">
                            خطة نشطة
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Pricing Plan Details Modal -->
<div class="modal fade" id="viewPricingPlanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل خطة السعر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="plan_details_content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Create Pricing Plan Modal -->
<div class="modal fade" id="createPricingPlanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة خطة سعر جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="createPricingPlanForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="service_id" class="form-label">الخدمة *</label>
                                <select class="form-select" id="service_id" name="service_id" required>
                                    <option value="">اختر الخدمة...</option>
                                    <?php foreach ($services as $service): ?>
                                        <option value="<?= $service['id'] ?>">
                                            <?= htmlspecialchars($service['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    يرجى اختيار خدمة
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم الخطة *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الخطة *</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">السعر (جنيه) *</label>
                                <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="installment_enabled" name="installment_enabled" onchange="toggleInstallmentFields()">
                                    <label class="form-check-label" for="installment_enabled">
                                        تفعيل التقسيط
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="installmentFields" style="display: none;">
                        <hr>
                        <h6>إعدادات التقسيط</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="installments_count" class="form-label">عدد الأقساط</label>
                                    <input type="number" class="form-control" id="installments_count" name="installments_count" min="1" max="60" onchange="calculateInstallment()">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="down_payment_percentage" class="form-label">نسبة المقدم (%)</label>
                                    <input type="number" class="form-control" id="down_payment_percentage" name="down_payment_percentage" min="0" max="100" step="0.01" onchange="calculateInstallment()">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="interest_type" class="form-label">نوع الفائدة</label>
                                    <select class="form-select" id="interest_type" name="interest_type" onchange="calculateInstallment()">
                                        <option value="percentage">نسبة مئوية</option>
                                        <option value="fixed">مبلغ ثابت</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="interest_value" class="form-label">قيمة الفائدة</label>
                                    <input type="number" class="form-control" id="interest_value" name="interest_value" min="0" step="0.01" onchange="calculateInstallment()">
                                    <small class="form-text text-muted" id="interest_help">
                                        أدخل النسبة المئوية أو المبلغ الثابت
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info" id="installment_calculation" style="display: none;">
                            <h6>حساب التقسيط:</h6>
                            <div id="calculation_details"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء خطة السعر</button>
                </div>
            </form>
        </div>
    </div>
</div>



<script>


// تبديل حقول التقسيط
function toggleInstallmentFields() {
    const checkbox = document.getElementById('installment_enabled');
    const fields = document.getElementById('installmentFields');
    fields.style.display = checkbox.checked ? 'block' : 'none';

    if (checkbox.checked) {
        calculateInstallment();
    }
}

// حساب مبلغ القسط
function calculateInstallment() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const installmentsCount = parseInt(document.getElementById('installments_count').value) || 0;
    const downPaymentPercentage = parseFloat(document.getElementById('down_payment_percentage').value) || 0;
    const interestType = document.getElementById('interest_type').value;
    const interestValue = parseFloat(document.getElementById('interest_value').value) || 0;

    if (price > 0 && installmentsCount > 0) {
        // الخطوة 1: حساب الفائدة وإضافتها للسعر الأصلي
        let totalInterest = 0;
        if (interestType === 'fixed') {
            totalInterest = interestValue;
        } else {
            totalInterest = (price * interestValue) / 100;
        }

        // الخطوة 2: السعر الكلي مع الفائدة
        const totalPriceWithInterest = price + totalInterest;

        // الخطوة 3: حساب المقدم من السعر الكلي مع الفائدة
        const downPayment = (totalPriceWithInterest * downPaymentPercentage) / 100;

        // الخطوة 4: المبلغ المتبقي للتقسيط
        const remainingAmount = totalPriceWithInterest - downPayment;

        // الخطوة 5: مبلغ القسط الواحد
        const installmentAmount = remainingAmount / installmentsCount;

        const calculationDiv = document.getElementById('calculation_details');
        calculationDiv.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>السعر الأصلي:</strong> ${formatCurrency(price)}<br>
                    <strong>الفائدة:</strong> ${formatCurrency(totalInterest)}<br>
                    <strong>السعر الكلي مع الفائدة:</strong> ${formatCurrency(totalPriceWithInterest)}
                </div>
                <div class="col-md-6">
                    <strong>المقدم:</strong> ${formatCurrency(downPayment)} (${downPaymentPercentage}%)<br>
                    <strong>المبلغ المتبقي للتقسيط:</strong> ${formatCurrency(remainingAmount)}<br>
                    <strong>مبلغ القسط الواحد:</strong> <span class="text-primary">${formatCurrency(installmentAmount)}</span>
                </div>
            </div>
        `;

        document.getElementById('installment_calculation').style.display = 'block';
    } else {
        document.getElementById('installment_calculation').style.display = 'none';
    }
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP',
        minimumFractionDigits: 2
    }).format(amount);
}

// تهيئة الأحداث عند تحميل الصفحة
$(document).ready(function() {
    // إضافة validation للنموذج
    $('#createPricingPlanForm').on('submit', function(e) {
        const serviceId = $('#service_id').val();

        if (!serviceId) {
            e.preventDefault();
            $('#service_id').addClass('is-invalid');
            $('#service_id').focus();
            return false;
        } else {
            $('#service_id').removeClass('is-invalid');
        }
    });

    // إعادة تعيين الحقول عند إغلاق النموذج
    $('#createPricingPlanModal').on('hidden.bs.modal', function() {
        $('#service_id').val('').removeClass('is-invalid');
        $('#installment_calculation').hide();
        document.getElementById('createPricingPlanForm').reset();
    });

    // تحديث نص المساعدة للفائدة
    $('#interest_type').on('change', function() {
        const helpText = document.getElementById('interest_help');
        if (this.value === 'fixed') {
            helpText.textContent = 'أدخل المبلغ الثابت للفائدة بالجنيه';
        } else {
            helpText.textContent = 'أدخل النسبة المئوية للفائدة';
        }
        calculateInstallment();
    });

    // إعادة حساب التقسيط عند تغيير السعر
    $('#price').on('input', calculateInstallment);
});

// عرض تفاصيل خطة السعر
function viewPlan(id) {
    const modal = new bootstrap.Modal(document.getElementById('viewPricingPlanModal'));
    const content = document.getElementById('plan_details_content');

    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    modal.show();

    fetch(`../api/get_pricing_plan_details.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = generatePlanDetailsHTML(data.plan);
            } else {
                content.innerHTML = '<div class="alert alert-danger">فشل في تحميل التفاصيل</div>';
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل التفاصيل:', error);
            content.innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل التفاصيل</div>';
        });
}

// تعديل خطة السعر
function editPlan(id) {
    fetch(`../api/get_pricing_plan_details.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const plan = data.plan;

                // ملء النموذج بالبيانات
                document.getElementById('edit_plan_id').value = plan.id;
                document.getElementById('edit_service_id').value = plan.service_id;
                document.getElementById('edit_name').value = plan.name;
                document.getElementById('edit_description').value = plan.features;
                document.getElementById('edit_price').value = plan.price;
                document.getElementById('edit_installment_enabled').checked = plan.allow_installment == 1;
                document.getElementById('edit_installments_count').value = plan.installments_count;
                document.getElementById('edit_down_payment_percentage').value = ((plan.down_payment / (plan.price + (plan.interest_value || 0))) * 100).toFixed(2);
                document.getElementById('edit_interest_type').value = plan.interest_type;
                document.getElementById('edit_interest_value').value = plan.interest_value;
                document.getElementById('edit_is_active').checked = plan.is_active == 1;

                // إظهار/إخفاء خيارات التقسيط
                toggleEditInstallmentOptions();

                // إظهار النموذج
                const modal = new bootstrap.Modal(document.getElementById('editPricingPlanModal'));
                modal.show();
            } else {
                alert('فشل في تحميل بيانات خطة السعر');
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            alert('حدث خطأ في تحميل البيانات');
        });
}

// حذف خطة السعر
function deletePlan(id) {
    if (confirm('هل أنت متأكد من حذف خطة السعر؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء!')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// إنشاء HTML لتفاصيل الخطة
function generatePlanDetailsHTML(plan) {
    const installmentInfo = plan.allow_installment == 1 ? `
        <div class="row mt-3">
            <div class="col-md-6">
                <h6>تفاصيل التقسيط:</h6>
                <p><strong>عدد الأقساط:</strong> ${plan.installments_count}</p>
                <p><strong>مبلغ القسط:</strong> ${parseFloat(plan.installment_amount).toFixed(2)} جنيه</p>
                <p><strong>المقدم:</strong> ${parseFloat(plan.down_payment).toFixed(2)} جنيه</p>
            </div>
            <div class="col-md-6">
                <h6>تفاصيل الفائدة:</h6>
                <p><strong>نوع الفائدة:</strong> ${plan.interest_type === 'fixed' ? 'مبلغ ثابت' : 'نسبة مئوية'}</p>
                <p><strong>قيمة الفائدة:</strong> ${parseFloat(plan.interest_value).toFixed(2)} ${plan.interest_type === 'fixed' ? 'جنيه' : '%'}</p>
            </div>
        </div>
    ` : '<p class="text-muted mt-3">التقسيط غير متاح لهذه الخطة</p>';

    return `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات أساسية:</h6>
                <p><strong>الخدمة:</strong> ${plan.service_name}</p>
                <p><strong>اسم الخطة:</strong> ${plan.name}</p>
                <p><strong>السعر:</strong> ${parseFloat(plan.price).toFixed(2)} جنيه</p>
                <p><strong>الحالة:</strong>
                    <span class="badge bg-${plan.is_active == 1 ? 'success' : 'secondary'}">
                        ${plan.is_active == 1 ? 'نشط' : 'غير نشط'}
                    </span>
                </p>
            </div>
            <div class="col-md-6">
                <h6>الوصف:</h6>
                <p>${plan.features}</p>
                <p><strong>تاريخ الإنشاء:</strong> ${plan.created_at}</p>
                <p><strong>التقسيط:</strong>
                    <span class="badge bg-${plan.allow_installment == 1 ? 'success' : 'secondary'}">
                        ${plan.allow_installment == 1 ? 'متاح' : 'غير متاح'}
                    </span>
                </p>
            </div>
        </div>
        ${installmentInfo}
    `;
}

// تبديل خيارات التقسيط في نموذج التعديل
function toggleEditInstallmentOptions() {
    const checkbox = document.getElementById('edit_installment_enabled');
    const options = document.getElementById('edit_installment_options');

    if (checkbox.checked) {
        options.style.display = 'block';
        calculateEditInstallment();
    } else {
        options.style.display = 'none';
    }
}

// حساب التقسيط في نموذج التعديل
function calculateEditInstallment() {
    const price = parseFloat(document.getElementById('edit_price').value) || 0;
    const installmentsCount = parseInt(document.getElementById('edit_installments_count').value) || 0;
    const downPaymentPercentage = parseFloat(document.getElementById('edit_down_payment_percentage').value) || 0;
    const interestType = document.getElementById('edit_interest_type').value;
    const interestValue = parseFloat(document.getElementById('edit_interest_value').value) || 0;

    if (price > 0 && installmentsCount > 0) {
        let totalInterest = 0;
        if (interestType === 'fixed') {
            totalInterest = interestValue;
        } else {
            totalInterest = (price * interestValue) / 100;
        }

        const totalWithInterest = price + totalInterest;
        const downPayment = (totalWithInterest * downPaymentPercentage) / 100;
        const remainingAmount = totalWithInterest - downPayment;
        const installmentAmount = remainingAmount / installmentsCount;

        document.getElementById('edit_total_with_interest').textContent = totalWithInterest.toFixed(2) + ' جنيه';
        document.getElementById('edit_down_payment_amount').textContent = downPayment.toFixed(2) + ' جنيه';
        document.getElementById('edit_remaining_amount').textContent = remainingAmount.toFixed(2) + ' جنيه';
        document.getElementById('edit_installment_amount').textContent = installmentAmount.toFixed(2) + ' جنيه';

        document.getElementById('edit_installment_calculation').style.display = 'block';
    } else {
        document.getElementById('edit_installment_calculation').style.display = 'none';
    }
}
</script>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>