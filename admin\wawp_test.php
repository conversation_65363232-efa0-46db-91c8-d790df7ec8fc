<?php
// بدء الجلسة
session_start();

// تضمين الملفات المطلوبة
try {
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../config/db_config.php';
    require_once __DIR__ . '/../config/database.php';
    require_once __DIR__ . '/../includes/functions.php';
    require_once __DIR__ . '/../config/wawp.php';
    require_once __DIR__ . '/../includes/wawp_integration.php';
} catch (Exception $e) {
    die("خطأ في تحميل الملفات: " . $e->getMessage());
}

// التحقق من تسجيل الدخول والصلاحيات (مؤقتاً معطل للاختبار)
// requireLogin();
// requireAdmin();

$success = '';
$error = '';
$test_result = null;

// معالجة طلبات الاختبار
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'test_connection':
                $wawp = new WAWP();
                $test_result = $wawp->testConnection();
                break;

            case 'send_test_message':
                $phone = $_POST['test_phone'] ?? '';
                $message = $_POST['test_message'] ?? '';

                if (empty($phone) || empty($message)) {
                    $error = 'يرجى إدخال رقم الهاتف والرسالة';
                    break;
                }

                $wawp = new WAWP();
                $test_result = $wawp->sendMessage($phone, $message);
                break;

            case 'send_otp_test':
                $phone = $_POST['otp_phone'] ?? '';
                $code = $_POST['otp_code'] ?? '';

                if (empty($phone) || empty($code)) {
                    $error = 'يرجى إدخال رقم الهاتف ورمز التحقق';
                    break;
                }

                $wawp_integration = new WAWPIntegration();
                $test_result = $wawp_integration->sendOTPCode($phone, $code);
                break;
        }
        
        if ($test_result && $test_result['success']) {
            $success = 'تم تنفيذ العملية بنجاح';
        } elseif ($test_result) {
            $error = $test_result['message'] ?? 'حدث خطأ غير معروف';
        }
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN ('wawp_token', 'wawp_instance_id')");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
} catch (Exception $e) {
    $error = 'خطأ في قراءة الإعدادات: ' . $e->getMessage();
    $settings = [];
}

$page_title = 'اختبار WAWP';
include __DIR__ . '/../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fab fa-whatsapp me-2"></i>
                    اختبار WAWP (WhatsApp API)
                </h1>
                <p class="page-description">اختبر إعدادات وإرسال الرسائل عبر WAWP</p>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- معلومات الإعدادات الحالية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات الحالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>WAWP Token:</strong>
                                <?= !empty($settings['wawp_token']) ? '✅ محدد (' . substr($settings['wawp_token'], 0, 10) . '...)' : '<span class="text-danger">❌ غير محدد</span>' ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Instance ID:</strong>
                                <?= !empty($settings['wawp_instance_id']) ? '✅ محدد (' . htmlspecialchars($settings['wawp_instance_id']) . ')' : '<span class="text-danger">❌ غير محدد</span>' ?>
                            </p>
                        </div>
                        <div class="col-12">
                            <p class="text-muted small">يتم استخدام الـ API الجديد: https://wawp.net/wp-json/awp/v1/send</p>
                        </div>
                    </div>
                    <?php if (empty($settings['wawp_token']) || empty($settings['wawp_instance_id'])): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            يرجى تكوين WAWP Token و Instance ID من <a href="settings.php" class="alert-link">صفحة الإعدادات</a> أولاً.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- اختبار الاتصال -->
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-wifi me-2"></i>
                                اختبار الاتصال
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="action" value="test_connection">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-wifi me-2"></i>
                                    اختبار الاتصال مع WAWP
                                </button>
                            </form>
                            <p class="text-muted mt-2">اختبر الاتصال مع خدمة WAWP قبل إرسال الرسائل</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبار إرسال الرسائل -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-paper-plane me-2"></i>
                                إرسال رسالة تجريبية
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="send_test_message">
                                <div class="mb-3">
                                    <label for="test_phone" class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" id="test_phone" name="test_phone"
                                           placeholder="مثال: 201012345678" required>
                                    <div class="form-text">أدخل الرقم مع رمز مصر (20) بدون علامة +</div>
                                </div>
                                <div class="mb-3">
                                    <label for="test_message" class="form-label">الرسالة</label>
                                    <textarea class="form-control" id="test_message" name="test_message"
                                              rows="4" placeholder="أدخل نص الرسالة التجريبية" required>مرحباً! هذه رسالة تجريبية من نظام FutureWay.</textarea>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال الرسالة
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-key me-2"></i>
                                إرسال رمز تحقق (OTP)
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="send_otp_test">
                                <div class="mb-3">
                                    <label for="otp_phone" class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" id="otp_phone" name="otp_phone"
                                           placeholder="مثال: 201012345678" required>
                                    <div class="form-text">أدخل الرقم مع رمز مصر (20) بدون علامة +</div>
                                </div>
                                <div class="mb-3">
                                    <label for="otp_code" class="form-label">رمز التحقق</label>
                                    <input type="text" class="form-control" id="otp_code" name="otp_code"
                                           placeholder="مثال: 123456" value="<?= rand(100000, 999999) ?>" required>
                                    <div class="form-text">سيتم إرسال رمز التحقق مع رسالة تلقائية</div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-key me-2"></i>
                                    إرسال OTP
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نتائج الاختبار -->
            <?php if ($test_result): ?>
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>
                                نتيجة الاختبار
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert <?= $test_result['success'] ? 'alert-success' : 'alert-danger' ?>">
                                <strong>الحالة:</strong> <?= $test_result['success'] ? 'نجح ✅' : 'فشل ❌' ?>
                            </div>

                            <?php if (isset($test_result['message'])): ?>
                                <p><strong>الرسالة:</strong> <?= htmlspecialchars($test_result['message']) ?></p>
                            <?php endif; ?>

                            <?php if (isset($test_result['api_response'])): ?>
                                <div class="mt-3">
                                    <h6><strong>📡 رد الـ API:</strong></h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>HTTP Status:</strong>
                                                <span class="badge <?= $test_result['api_response']['http_code'] == 200 ? 'bg-success' : 'bg-danger' ?>">
                                                    <?= $test_result['api_response']['http_code'] ?>
                                                </span>
                                            </p>
                                        </div>
                                    </div>

                                    <?php if (!empty($test_result['api_response']['parsed_response'])): ?>
                                        <div class="alert alert-info">
                                            <strong>📋 استجابة الـ API:</strong>
                                            <pre class="mt-2 mb-0"><?= htmlspecialchars(json_encode($test_result['api_response']['parsed_response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></pre>
                                        </div>
                                    <?php endif; ?>

                                    <details class="mt-2">
                                        <summary><strong>📤 البيانات المرسلة</strong></summary>
                                        <pre class="mt-2 p-2 bg-light rounded"><?= htmlspecialchars(json_encode($test_result['api_response']['sent_data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></pre>
                                    </details>

                                    <details class="mt-2">
                                        <summary><strong>📥 الاستجابة الخام</strong></summary>
                                        <pre class="mt-2 p-2 bg-light rounded"><?= htmlspecialchars($test_result['api_response']['raw_response']) ?></pre>
                                    </details>
                                </div>
                            <?php endif; ?>

                            <?php if (isset($test_result['data']) && !isset($test_result['api_response'])): ?>
                                <details class="mt-3">
                                    <summary><strong>تفاصيل إضافية</strong></summary>
                                    <pre class="mt-2 p-2 bg-light rounded"><?= htmlspecialchars(json_encode($test_result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></pre>
                                </details>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- اختبارات إضافية -->
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-flask me-2"></i>
                                اختبارات إضافية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <button type="button" class="btn btn-outline-primary w-100" onclick="sendWelcomeTest()">
                                        <i class="fas fa-hand-wave me-2"></i>
                                        اختبار رسالة الترحيب
                                    </button>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <button type="button" class="btn btn-outline-warning w-100" onclick="sendPaymentReminderTest()">
                                        <i class="fas fa-bell me-2"></i>
                                        اختبار تذكير الدفع
                                    </button>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <button type="button" class="btn btn-outline-info w-100" onclick="createInstanceTest()">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        إنشاء Instance جديد
                                    </button>
                                </div>
                            </div>

                            <!-- نموذج للاختبارات الإضافية -->
                            <div id="additional-test-form" class="mt-4" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="additional_phone" class="form-label">رقم الهاتف</label>
                                            <input type="text" class="form-control" id="additional_phone"
                                                   placeholder="مثال: 966501234567">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="additional_name" class="form-label">الاسم (للترحيب)</label>
                                            <input type="text" class="form-control" id="additional_name"
                                                   placeholder="اسم المستخدم" value="أحمد محمد">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_amount" class="form-label">مبلغ الدفع (للتذكير)</label>
                                            <input type="number" class="form-control" id="payment_amount"
                                                   placeholder="500" value="500">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_due_date" class="form-label">تاريخ الاستحقاق</label>
                                            <input type="date" class="form-control" id="payment_due_date"
                                                   value="<?= date('Y-m-d', strtotime('+7 days')) ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- سجل الرسائل -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history me-2"></i>
                                سجل الرسائل الأخيرة
                            </h5>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="loadMessageLog()">
                                <i class="fas fa-refresh me-2"></i>
                                تحديث
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="message-log">
                                <div class="text-center text-muted">
                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                    <p>اضغط على "تحديث" لعرض سجل الرسائل</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>
