<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'إدارة بوابات الدفع';

try {
    $db = new Database();
    $pdo = $db->getConnection();

    // معالجة الإجراءات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';

        if ($action === 'create') {
            $name = sanitizeInput($_POST['name'] ?? '');
            $payment_method = sanitizeInput($_POST['payment_method'] ?? '');
            $description = sanitizeInput($_POST['description'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            // رفع الصورة
            $image_path = null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/payment_gateways/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

                if (in_array($file_extension, $allowed_extensions)) {
                    $filename = uniqid() . '.' . $file_extension;
                    $upload_path = $upload_dir . $filename;

                    if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                        $image_path = 'uploads/payment_gateways/' . $filename;
                    }
                }
            }

            $stmt = $pdo->prepare("
                INSERT INTO payment_gateways (name, image, payment_method, description, is_active)
                VALUES (?, ?, ?, ?, ?)
            ");

            if ($stmt->execute([$name, $image_path, $payment_method, $description, $is_active])) {
                $success_message = 'تم إضافة بوابة الدفع بنجاح';
            } else {
                $error_message = 'حدث خطأ أثناء إضافة بوابة الدفع';
            }
        }

        elseif ($action === 'update') {
            $id = (int)($_POST['id'] ?? 0);
            $name = sanitizeInput($_POST['name'] ?? '');
            $payment_method = sanitizeInput($_POST['payment_method'] ?? '');
            $description = sanitizeInput($_POST['description'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            // جلب البيانات الحالية
            $stmt = $pdo->prepare("SELECT image FROM payment_gateways WHERE id = ?");
            $stmt->execute([$id]);
            $current_gateway = $stmt->fetch(PDO::FETCH_ASSOC);
            $image_path = $current_gateway['image'];

            // رفع صورة جديدة إذا تم اختيارها
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/payment_gateways/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

                if (in_array($file_extension, $allowed_extensions)) {
                    $filename = uniqid() . '.' . $file_extension;
                    $upload_path = $upload_dir . $filename;

                    if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                        // حذف الصورة القديمة
                        if ($image_path && file_exists('../' . $image_path)) {
                            unlink('../' . $image_path);
                        }
                        $image_path = 'uploads/payment_gateways/' . $filename;
                    }
                }
            }

            $stmt = $pdo->prepare("
                UPDATE payment_gateways 
                SET name = ?, image = ?, payment_method = ?, description = ?, is_active = ?
                WHERE id = ?
            ");

            if ($stmt->execute([$name, $image_path, $payment_method, $description, $is_active, $id])) {
                $success_message = 'تم تحديث بوابة الدفع بنجاح';
            } else {
                $error_message = 'حدث خطأ أثناء تحديث بوابة الدفع';
            }
        }

        elseif ($action === 'delete') {
            $id = (int)($_POST['id'] ?? 0);

            // جلب مسار الصورة لحذفها
            $stmt = $pdo->prepare("SELECT image FROM payment_gateways WHERE id = ?");
            $stmt->execute([$id]);
            $gateway = $stmt->fetch(PDO::FETCH_ASSOC);

            $stmt = $pdo->prepare("DELETE FROM payment_gateways WHERE id = ?");
            if ($stmt->execute([$id])) {
                // حذف الصورة
                if ($gateway['image'] && file_exists('../' . $gateway['image'])) {
                    unlink('../' . $gateway['image']);
                }
                $success_message = 'تم حذف بوابة الدفع بنجاح';
            } else {
                $error_message = 'حدث خطأ أثناء حذف بوابة الدفع';
            }
        }
    }

    // جلب جميع بوابات الدفع
    $stmt = $pdo->prepare("SELECT * FROM payment_gateways ORDER BY created_at DESC");
    $stmt->execute();
    $gateways = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    error_log("Error in payment_gateways.php: " . $e->getMessage());
    $error_message = 'حدث خطأ في النظام';
    $gateways = []; // تهيئة المتغير في حالة الخطأ
}

include __DIR__ . '/../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="admin-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>إدارة بوابات الدفع</h2>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGatewayModal">
                        <i class="fas fa-plus"></i> إضافة بوابة دفع
                    </button>
                </div>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?= $success_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?= $error_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم البوابة</th>
                                        <th>طريقة الدفع</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($gateways as $gateway): ?>
                                    <tr>
                                        <td>
                                            <?php if ($gateway['image']): ?>
                                                <img src="../<?= htmlspecialchars($gateway['image']) ?>" 
                                                     alt="<?= htmlspecialchars($gateway['name']) ?>" 
                                                     style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
                                            <?php else: ?>
                                                <div style="width: 50px; height: 50px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= htmlspecialchars($gateway['name']) ?></td>
                                        <td>
                                            <small class="text-muted">
                                                <?= nl2br(htmlspecialchars(substr($gateway['payment_method'], 0, 50))) ?>
                                                <?= strlen($gateway['payment_method']) > 50 ? '...' : '' ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if ($gateway['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= date('Y-m-d H:i', strtotime($gateway['created_at'])) ?></td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="editGateway(<?= htmlspecialchars(json_encode($gateway)) ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteGateway(<?= $gateway['id'] ?>, '<?= htmlspecialchars($gateway['name']) ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة بوابة دفع -->
<div class="modal fade" id="addGatewayModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة بوابة دفع جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم بوابة الدفع</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="image" class="form-label">صورة البوابة</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <small class="form-text text-muted">اختياري - يُفضل صورة بحجم 100x100 بكسل</small>
                    </div>

                    <div class="mb-3">
                        <label for="payment_method" class="form-label">طريقة الدفع</label>
                        <textarea class="form-control" id="payment_method" name="payment_method" rows="3" required
                                  placeholder="مثال: رقم المحفظة: 01234567890&#10;أو رقم الحساب: 1234567890123456"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="2"
                                  placeholder="وصف مختصر عن بوابة الدفع"></textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                نشط
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل بوابة دفع -->
<div class="modal fade" id="editGatewayModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل بوابة الدفع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">اسم بوابة الدفع</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_image" class="form-label">صورة البوابة</label>
                        <input type="file" class="form-control" id="edit_image" name="image" accept="image/*">
                        <small class="form-text text-muted">اختياري - اتركه فارغاً للاحتفاظ بالصورة الحالية</small>
                        <div id="current_image_preview" class="mt-2"></div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_payment_method" class="form-label">طريقة الدفع</label>
                        <textarea class="form-control" id="edit_payment_method" name="payment_method" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="2"></textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">
                                نشط
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editGateway(gateway) {
    document.getElementById('edit_id').value = gateway.id;
    document.getElementById('edit_name').value = gateway.name;
    document.getElementById('edit_payment_method').value = gateway.payment_method;
    document.getElementById('edit_description').value = gateway.description || '';
    document.getElementById('edit_is_active').checked = gateway.is_active == 1;
    
    // عرض الصورة الحالية
    const imagePreview = document.getElementById('current_image_preview');
    if (gateway.image) {
        imagePreview.innerHTML = `
            <div class="current-image">
                <label class="form-label">الصورة الحالية:</label><br>
                <img src="../${gateway.image}" alt="${gateway.name}" style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px;">
            </div>
        `;
    } else {
        imagePreview.innerHTML = '<small class="text-muted">لا توجد صورة حالية</small>';
    }
    
    new bootstrap.Modal(document.getElementById('editGatewayModal')).show();
}

function deleteGateway(id, name) {
    if (confirm(`هل أنت متأكد من حذف بوابة الدفع "${name}"؟`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>
