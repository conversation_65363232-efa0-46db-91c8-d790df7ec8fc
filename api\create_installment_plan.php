<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit();
}

try {
    $user_id = (int)($_POST['user_id'] ?? 0);
    $pricing_plan_id = (int)($_POST['pricing_plan_id'] ?? 0);
    $down_payment = (float)($_POST['down_payment'] ?? 0);

    // التحقق من صحة البيانات
    if (!$user_id || !$pricing_plan_id || $down_payment < 0) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
        exit();
    }

    $db = new Database();
    $pdo = $db->getConnection();

    // التحقق من وجود المستخدم
    $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'المستخدم غير موجود']);
        exit();
    }

    // الحصول على تفاصيل خطة السعر
    $stmt = $pdo->prepare("SELECT * FROM pricing_plans WHERE id = ? AND allow_installment = 1");
    $stmt->execute([$pricing_plan_id]);
    $pricing_plan = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$pricing_plan) {
        echo json_encode(['success' => false, 'message' => 'خطة السعر غير موجودة أو لا تدعم التقسيط']);
        exit();
    }

    // التحقق من عدم وجود خطة تقسيط نشطة للمستخدم لنفس خطة السعر
    $stmt = $pdo->prepare("SELECT id FROM installment_plans WHERE user_id = ? AND pricing_plan_id = ? AND status = 'active'");
    $stmt->execute([$user_id, $pricing_plan_id]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'يوجد خطة تقسيط نشطة بالفعل لهذا المستخدم لنفس خطة السعر']);
        exit();
    }

    // حساب قيم التقسيط مع الفائدة
    $base_price = $pricing_plan['price'];

    // حساب الفائدة
    $total_interest = 0;
    if ($pricing_plan['interest_type'] === 'fixed') {
        $total_interest = $pricing_plan['interest_value'];
    } else {
        $total_interest = ($base_price * $pricing_plan['interest_value']) / 100;
    }

    // إجمالي المبلغ مع الفائدة
    $total_amount = $base_price + $total_interest;

    // التحقق من أن المقدم لا يتجاوز السعر الإجمالي
    if ($down_payment >= $total_amount) {
        echo json_encode(['success' => false, 'message' => 'المقدم لا يمكن أن يكون أكبر من أو يساوي السعر الإجمالي']);
        exit();
    }

    $remaining_amount = $total_amount - $down_payment;
    $installment_amount = $pricing_plan['installment_amount'];
    $installments_count = $pricing_plan['installments_count'];

    // إنشاء خطة التقسيط
    $stmt = $pdo->prepare("
        INSERT INTO installment_plans (
            user_id, pricing_plan_id, total_amount, down_payment,
            remaining_amount, installment_amount, installments_count, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active')
    ");

    if ($stmt->execute([
        $user_id, $pricing_plan_id, $total_amount, $down_payment,
        $remaining_amount, $installment_amount, $installments_count
    ])) {
        $installment_plan_id = $pdo->lastInsertId();

        // إنشاء جدولة الأقساط
        $current_date = new DateTime();
        for ($i = 1; $i <= $installments_count; $i++) {
            $due_date = clone $current_date;
            $due_date->add(new DateInterval('P' . $i . 'M')); // إضافة شهر لكل قسط

            $stmt = $pdo->prepare("
                INSERT INTO payments (
                    installment_plan_id, installment_number, amount, due_date, status
                ) VALUES (?, ?, ?, ?, 'pending')
            ");
            $stmt->execute([
                $installment_plan_id, $i, $installment_amount, $due_date->format('Y-m-d')
            ]);
        }

        echo json_encode([
            'success' => true,
            'message' => 'تم إنشاء خطة التقسيط بنجاح',
            'installment_plan_id' => $installment_plan_id
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في إنشاء خطة التقسيط']);
    }

} catch (Exception $e) {
    error_log("Error creating installment plan: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
