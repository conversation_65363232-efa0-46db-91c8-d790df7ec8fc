<?php
require_once 'config.php';
require_once 'database.php';

class WAWP {
    private $api_url;
    private $access_token;
    private $instance_id;

    public function __construct($access_token = null, $instance_id = null) {
        $this->api_url = 'https://wawp.net/wp-json/awp/v1/';
        $this->access_token = $access_token;
        $this->instance_id = $instance_id;

        // إذا لم يتم تمرير المعاملات، حاول قراءتها من قاعدة البيانات
        if (!$access_token || !$instance_id) {
            $this->loadSettingsFromDB();
        }
    }

    private function loadSettingsFromDB() {
        try {
            $database = new Database();
            $conn = $database->getConnection();

            $stmt = $conn->prepare("SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN ('wawp_token', 'wawp_instance_id')");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            $this->access_token = $settings['wawp_token'] ?? null;
            $this->instance_id = $settings['wawp_instance_id'] ?? null;
        } catch (Exception $e) {
            error_log("Error loading WAWP settings: " . $e->getMessage());
        }
    }

    // دالة مساعدة لمعالجة استجابات API
    private function processApiResponse($response, $http_code) {
        if ($http_code !== 200) {
            return ['success' => false, 'message' => 'HTTP Error: ' . $http_code, 'response' => $response];
        }

        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['success' => false, 'message' => 'Invalid JSON response', 'response' => $response];
        }

        // معالجة استجابة WAWP API
        if (isset($result['status'])) {
            if ($result['status'] === 'success') {
                return ['success' => true, 'message' => 'العملية تمت بنجاح', 'data' => $result];
            } else {
                return ['success' => false, 'message' => $result['message'] ?? 'فشل في العملية', 'data' => $result];
            }
        }

        // إذا كانت الاستجابة بشكل مختلف
        if (isset($result['success'])) {
            return $result;
        }

        return ['success' => false, 'message' => 'استجابة غير متوقعة من API', 'data' => $result];
    }

    public function sendMessage($phone, $message, $type = 'text') {
        if (empty($this->access_token) || empty($this->instance_id)) {
            return ['success' => false, 'message' => 'WAWP Token و Instance ID مطلوبان'];
        }

        // تنظيف رقم الهاتف - إزالة أي رموز غير رقمية
        $clean_phone = preg_replace('/[^0-9]/', '', $phone);

        // بناء URL مع المعاملات كما هو موضح في API
        $url = 'https://wawp.net/wp-json/awp/v1/send?' . http_build_query([
            'instance_id' => $this->instance_id,
            'access_token' => $this->access_token,
            'chatId' => $clean_phone,
            'message' => $message
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            return ['success' => false, 'message' => 'cURL Error: ' . $curl_error];
        }

        // معالجة الاستجابة
        $result = json_decode($response, true);

        // إرجاع تفاصيل كاملة عن الاستجابة
        $return_data = [
            'http_code' => $http_code,
            'raw_response' => $response,
            'parsed_response' => $result,
            'sent_url' => $url
        ];

        if ($http_code == 200 || $http_code == 201) {
            // تحقق من نجاح العملية بناءً على استجابة WAWP
            // HTTP 200 = OK, HTTP 201 = Created (both are success)
            if ($result && (
                (isset($result['success']) && $result['success']) ||
                (isset($result['status']) && $result['status'] === 'success') ||
                (isset($result['id']) && !empty($result['id'])) || // WAWP returns message ID on success
                (is_string($response) && strpos($response, 'success') !== false)
            )) {
                return ['success' => true, 'message' => 'تم إرسال الرسالة بنجاح', 'api_response' => $return_data];
            } else {
                return ['success' => false, 'message' => $result['message'] ?? 'فشل في إرسال الرسالة', 'api_response' => $return_data];
            }
        } else {
            return ['success' => false, 'message' => "HTTP Error: $http_code", 'api_response' => $return_data];
        }
    }

    public function sendOTP($phone, $code, $type = 'login') {
        $message = str_replace('{code}', $code, 'رمز التحقق الخاص بك في FutureWay هو: {code}');
        return $this->sendMessage($phone, $message);
    }

    public function sendWelcomeMessage($phone, $name) {
        $message = str_replace('{name}', $name, 'مرحباً {name}! تم إنشاء حسابك بنجاح في FutureWay.');
        return $this->sendMessage($phone, $message);
    }

    public function sendInstallmentCreated($phone, $details) {
        $message = str_replace('{details}', $details, 'تم إنشاء خطة التقسيط الخاصة بك بنجاح. التفاصيل: {details}');
        return $this->sendMessage($phone, $message);
    }

    public function sendPurchaseSuccess($phone, $serviceName) {
        $message = str_replace('{service}', $serviceName, 'تم شراء خدمة {service} بنجاح. شكراً لثقتك في FutureWay.');
        return $this->sendMessage($phone, $message);
    }

    public function sendInstallmentCompleted($phone, $planDetails) {
        $message = str_replace('{plan}', $planDetails, 'تهانينا! تم إنهاء خطة التقسيط {plan} بنجاح.');
        return $this->sendMessage($phone, $message);
    }

    // إنشاء Instance جديد
    public function createInstance() {
        if (empty($this->access_token)) {
            return ['success' => false, 'message' => 'Access token not provided'];
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->api_url . 'create_instance?access_token=' . $this->access_token);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false) {
            return ['success' => false, 'message' => 'cURL Error'];
        }

        return $this->processApiResponse($response, $http_code);
    }

    // الحصول على QR Code
    public function getQRCode() {
        if (empty($this->access_token) || empty($this->instance_id)) {
            return ['success' => false, 'message' => 'Access token or instance ID not provided'];
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->api_url . 'get_qrcode?instance_id=' . $this->instance_id . '&access_token=' . $this->access_token);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false) {
            return ['success' => false, 'message' => 'cURL Error'];
        }

        return $this->processApiResponse($response, $http_code);
    }

    // إعادة تشغيل Instance
    public function rebootInstance() {
        if (empty($this->access_token) || empty($this->instance_id)) {
            return ['success' => false, 'message' => 'Access token or instance ID not provided'];
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->api_url . 'reboot?instance_id=' . $this->instance_id . '&access_token=' . $this->access_token);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false) {
            return ['success' => false, 'message' => 'cURL Error'];
        }

        return $this->processApiResponse($response, $http_code);
    }

    // فحص حالة Instance
    public function checkInstanceStatus() {
        if (empty($this->access_token) || empty($this->instance_id)) {
            return ['success' => false, 'message' => 'Access token or instance ID not provided'];
        }

        // محاولة الحصول على QR Code - إذا كان Instance متصل لن يرجع QR
        $qr_result = $this->getQRCode();

        if ($qr_result['success'] && isset($qr_result['data']['qr_code'])) {
            return [
                'success' => true,
                'connected' => false,
                'message' => 'Instance غير متصل - يحتاج لمسح QR Code',
                'qr_code' => $qr_result['data']['qr_code']
            ];
        } elseif ($qr_result['success'] && !isset($qr_result['data']['qr_code'])) {
            return [
                'success' => true,
                'connected' => true,
                'message' => 'Instance متصل ونشط'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'فشل في فحص حالة Instance: ' . ($qr_result['message'] ?? 'خطأ غير معروف')
            ];
        }
    }

    // اختبار الاتصال
    public function testConnection() {
        if (empty($this->access_token) || empty($this->instance_id)) {
            return ['success' => false, 'message' => 'WAWP Token و Instance ID مطلوبان. يرجى إدخالهما في صفحة الإعدادات.'];
        }

        // بناء URL للاختبار مع رقم وهمي
        $test_url = 'https://wawp.net/wp-json/awp/v1/send?' . http_build_query([
            'instance_id' => $this->instance_id,
            'access_token' => $this->access_token,
            'chatId' => '201000000000', // رقم وهمي للاختبار
            'message' => 'test connection'
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            return ['success' => false, 'message' => 'خطأ في الاتصال: ' . $curl_error];
        }

        $result = json_decode($response, true);

        // إرجاع تفاصيل كاملة عن الاستجابة
        $return_data = [
            'http_code' => $http_code,
            'raw_response' => $response,
            'parsed_response' => $result,
            'sent_url' => $test_url
        ];

        if ($http_code == 200 || $http_code == 201) {
            // تحقق من وجود message ID في الاستجابة كدليل على النجاح
            if ($result && isset($result['id']) && !empty($result['id'])) {
                return ['success' => true, 'message' => 'الاتصال مع WAWP يعمل بشكل صحيح ✅ (تم إرسال رسالة اختبار)', 'api_response' => $return_data];
            } else {
                return ['success' => true, 'message' => 'الاتصال مع WAWP يعمل بشكل صحيح ✅', 'api_response' => $return_data];
            }
        } else {
            return ['success' => false, 'message' => "خطأ في الاتصال - HTTP Code: $http_code", 'api_response' => $return_data];
        }
    }
}
?>

