<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/wawp.php';

// وظائف إدارة OTP
function generateAndSaveOTP($phone, $type = 'login') {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $code = generateOTP();
        $expires_at = date('Y-m-d H:i:s', time() + OTP_EXPIRY);
        
        // حذف الرموز القديمة لنفس الهاتف والنوع
        $sql = "DELETE FROM otp_codes WHERE phone = :phone AND type = :type";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':phone', $phone);
        $stmt->bindParam(':type', $type);
        $stmt->execute();
        
        // إدراج الرمز الجديد
        $sql = "INSERT INTO otp_codes (phone, code, type, expires_at) VALUES (:phone, :code, :type, :expires_at)";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':phone', $phone);
        $stmt->bindParam(':code', $code);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':expires_at', $expires_at);
        
        if ($stmt->execute()) {
            return $code;
        }
        return false;
    } catch (Exception $e) {
        error_log("Error generating OTP: " . $e->getMessage());
        return false;
    }
}

function verifyOTP($phone, $code, $type = 'login') {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "SELECT id FROM otp_codes WHERE phone = :phone AND code = :code AND type = :type 
                AND expires_at > NOW() AND is_used = FALSE";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':phone', $phone);
        $stmt->bindParam(':code', $code);
        $stmt->bindParam(':type', $type);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // تحديد الرمز كمستخدم
            $sql = "UPDATE otp_codes SET is_used = TRUE WHERE id = :id";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':id', $row['id']);
            $stmt->execute();
            
            return true;
        }
        return false;
    } catch (Exception $e) {
        error_log("Error verifying OTP: " . $e->getMessage());
        return false;
    }
}

// وظائف إدارة المستخدمين
function createUser($name, $phone, $governorate, $country, $profession, $national_id = null) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "INSERT INTO users (name, phone, governorate, country, profession, national_id, is_verified) 
                VALUES (:name, :phone, :governorate, :country, :profession, :national_id, TRUE)";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':phone', $phone);
        $stmt->bindParam(':governorate', $governorate);
        $stmt->bindParam(':country', $country);
        $stmt->bindParam(':profession', $profession);
        $stmt->bindParam(':national_id', $national_id);
        
        if ($stmt->execute()) {
            return $conn->lastInsertId();
        }
        return false;
    } catch (Exception $e) {
        error_log("Error creating user: " . $e->getMessage());
        return false;
    }
}

function getUserByPhone($phone) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "SELECT * FROM users WHERE phone = :phone";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':phone', $phone);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting user: " . $e->getMessage());
        return false;
    }
}

function isAdminUser($phone) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "SELECT id FROM admin_users WHERE phone = :phone AND is_verified = TRUE";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':phone', $phone);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        error_log("Error checking admin: " . $e->getMessage());
        return false;
    }
}

// وظائف إدارة الخدمات
function getAllServices() {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "SELECT s.*, 
                MIN(p.price) as min_price, 
                MAX(p.price) as max_price,
                COUNT(DISTINCT ip.id) as purchase_count
                FROM services s 
                LEFT JOIN pricing_plans p ON s.id = p.service_id 
                LEFT JOIN installment_plans ip ON p.id = ip.pricing_plan_id
                GROUP BY s.id 
                ORDER BY s.created_at DESC";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting services: " . $e->getMessage());
        return [];
    }
}

function getServiceById($id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "SELECT * FROM services WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting service: " . $e->getMessage());
        return false;
    }
}

function getPricingPlansByService($service_id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "SELECT * FROM pricing_plans WHERE service_id = :service_id ORDER BY price ASC";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting pricing plans: " . $e->getMessage());
        return [];
    }
}

// وظائف إدارة التقسيط
function createInstallmentPlan($user_id, $pricing_plan_id, $down_payment) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // الحصول على تفاصيل خطة السعر
        $sql = "SELECT * FROM pricing_plans WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $pricing_plan_id);
        $stmt->execute();
        $pricing_plan = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pricing_plan) {
            return false;
        }
        
        // حساب إجمالي المبلغ مع الفائدة
        $base_price = $pricing_plan['price'];
        $total_interest = 0;
        if ($pricing_plan['interest_type'] === 'fixed') {
            $total_interest = $pricing_plan['interest_value'];
        } else {
            $total_interest = ($base_price * $pricing_plan['interest_value']) / 100;
        }
        $total_amount = $base_price + $total_interest;
        $remaining_amount = $total_amount - $down_payment;
        $installment_amount = $pricing_plan['installment_amount'];
        $installments_count = $pricing_plan['installments_count'];
        
        // إنشاء خطة التقسيط
        $sql = "INSERT INTO installment_plans (user_id, pricing_plan_id, total_amount, down_payment, 
                remaining_amount, installment_amount, installments_count) 
                VALUES (:user_id, :pricing_plan_id, :total_amount, :down_payment, 
                :remaining_amount, :installment_amount, :installments_count)";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':pricing_plan_id', $pricing_plan_id);
        $stmt->bindParam(':total_amount', $total_amount);
        $stmt->bindParam(':down_payment', $down_payment);
        $stmt->bindParam(':remaining_amount', $remaining_amount);
        $stmt->bindParam(':installment_amount', $installment_amount);
        $stmt->bindParam(':installments_count', $installments_count);
        
        if ($stmt->execute()) {
            $installment_plan_id = $conn->lastInsertId();
            
            // إنشاء دفعات الأقساط
            createPaymentSchedule($installment_plan_id, $installments_count, $installment_amount);
            
            return $installment_plan_id;
        }
        return false;
    } catch (Exception $e) {
        error_log("Error creating installment plan: " . $e->getMessage());
        return false;
    }
}

function createPaymentSchedule($installment_plan_id, $installments_count, $installment_amount) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        for ($i = 1; $i <= $installments_count; $i++) {
            $due_date = date('Y-m-d', strtotime("+$i month"));
            
            $sql = "INSERT INTO payments (installment_plan_id, installment_number, amount, due_date) 
                    VALUES (:installment_plan_id, :installment_number, :amount, :due_date)";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':installment_plan_id', $installment_plan_id);
            $stmt->bindParam(':installment_number', $i);
            $stmt->bindParam(':amount', $installment_amount);
            $stmt->bindParam(':due_date', $due_date);
            $stmt->execute();
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Error creating payment schedule: " . $e->getMessage());
        return false;
    }
}

// وظائف رفع الملفات
function uploadFile($file, $allowed_extensions = null) {
    if (!$allowed_extensions) {
        $allowed_extensions = ALLOWED_EXTENSIONS;
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowed_extensions)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    $filename = uniqid() . '.' . $extension;
    $filepath = UPLOAD_PATH . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename];
    }
    
    return ['success' => false, 'message' => 'فشل في حفظ الملف'];
}

// وظائف الإحصائيات
function getDashboardStats($user_id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // إجمالي خطط التقسيط
        $sql = "SELECT COUNT(*) as total_plans FROM installment_plans WHERE user_id = :user_id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $total_plans = $stmt->fetch(PDO::FETCH_ASSOC)['total_plans'];
        
        // الخطط النشطة
        $sql = "SELECT COUNT(*) as active_plans FROM installment_plans WHERE user_id = :user_id AND status = 'active'";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $active_plans = $stmt->fetch(PDO::FETCH_ASSOC)['active_plans'];
        
        // إجمالي المدفوعات المتأخرة
        $sql = "SELECT COUNT(*) as overdue_payments 
                FROM payments p 
                JOIN installment_plans ip ON p.installment_plan_id = ip.id 
                WHERE ip.user_id = :user_id AND p.status = 'overdue'";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $overdue_payments = $stmt->fetch(PDO::FETCH_ASSOC)['overdue_payments'];
        
        return [
            'total_plans' => $total_plans,
            'active_plans' => $active_plans,
            'overdue_payments' => $overdue_payments
        ];
    } catch (Exception $e) {
        error_log("Error getting dashboard stats: " . $e->getMessage());
        return ['total_plans' => 0, 'active_plans' => 0, 'overdue_payments' => 0];
    }
}
// دوال التحقق من الصلاحيات موجودة في config.php

// دوال تنسيق البيانات
// formatCurrency و sanitizeInput موجودان في config.php

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'منذ لحظات';
    if ($time < 3600) return 'منذ ' . floor($time/60) . ' دقيقة';
    if ($time < 86400) return 'منذ ' . floor($time/3600) . ' ساعة';
    if ($time < 2592000) return 'منذ ' . floor($time/86400) . ' يوم';
    if ($time < 31536000) return 'منذ ' . floor($time/2592000) . ' شهر';
    return 'منذ ' . floor($time/31536000) . ' سنة';
}

// دالة cleanPhone موجودة في config.php

// دالة generateOTP موجودة في config.php

// دوال إدارة المستخدمين للإدارة (محسنة)
function getAllUsersAdmin() {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $sql = "SELECT u.*,
                COUNT(DISTINCT ip.id) as installment_plans_count,
                COALESCE(SUM(CASE WHEN ip.status = 'completed' THEN ip.total_amount ELSE 0 END), 0) as total_spent
                FROM users u
                LEFT JOIN installment_plans ip ON u.id = ip.user_id
                WHERE u.is_admin = FALSE
                GROUP BY u.id
                ORDER BY u.created_at DESC";
        $stmt = $conn->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting all users: " . $e->getMessage());
        return [];
    }
}

function getUserByIdAdmin($id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $sql = "SELECT * FROM users WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting user by ID: " . $e->getMessage());
        return false;
    }
}

function updateUserAdmin($id, $data) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $sql = "UPDATE users SET
                name = :name,
                phone = :phone,
                governorate = :governorate,
                country = :country,
                profession = :profession,
                national_id = :national_id,
                updated_at = NOW()
                WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':phone', $data['phone']);
        $stmt->bindParam(':governorate', $data['governorate']);
        $stmt->bindParam(':country', $data['country']);
        $stmt->bindParam(':profession', $data['profession']);
        $stmt->bindParam(':national_id', $data['national_id']);

        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Error updating user: " . $e->getMessage());
        return false;
    }
}

function deleteUserAdmin($id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        // التحقق من عدم وجود خطط تقسيط نشطة
        $sql = "SELECT COUNT(*) FROM installment_plans WHERE user_id = :id AND status = 'active'";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        if ($stmt->fetchColumn() > 0) {
            return ['success' => false, 'message' => 'لا يمكن حذف المستخدم لوجود خطط تقسيط نشطة'];
        }

        $sql = "DELETE FROM users WHERE id = :id AND is_admin = FALSE";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);

        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم حذف المستخدم بنجاح'];
        }
        return ['success' => false, 'message' => 'فشل في حذف المستخدم'];
    } catch (Exception $e) {
        error_log("Error deleting user: " . $e->getMessage());
        return ['success' => false, 'message' => 'حدث خطأ أثناء الحذف'];
    }
}

// دوال إدارة الخدمات للإدارة
function createServiceAdmin($name, $description, $image = null) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $sql = "INSERT INTO services (name, description, image) VALUES (:name, :description, :image)";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':image', $image);

        if ($stmt->execute()) {
            return $conn->lastInsertId();
        }
        return false;
    } catch (Exception $e) {
        error_log("Error creating service: " . $e->getMessage());
        return false;
    }
}

function updateServiceAdmin($id, $name, $description, $image = null) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        if ($image) {
            $sql = "UPDATE services SET name = :name, description = :description, image = :image, updated_at = NOW() WHERE id = :id";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':image', $image);
        } else {
            $sql = "UPDATE services SET name = :name, description = :description, updated_at = NOW() WHERE id = :id";
            $stmt = $conn->prepare($sql);
        }

        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':description', $description);

        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Error updating service: " . $e->getMessage());
        return false;
    }
}

function deleteServiceAdmin($id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        // التحقق من عدم وجود خطط أسعار مرتبطة
        $sql = "SELECT COUNT(*) FROM pricing_plans WHERE service_id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        if ($stmt->fetchColumn() > 0) {
            return ['success' => false, 'message' => 'لا يمكن حذف الخدمة لوجود خطط أسعار مرتبطة بها'];
        }

        $sql = "DELETE FROM services WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);

        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم حذف الخدمة بنجاح'];
        }
        return ['success' => false, 'message' => 'فشل في حذف الخدمة'];
    } catch (Exception $e) {
        error_log("Error deleting service: " . $e->getMessage());
        return ['success' => false, 'message' => 'حدث خطأ أثناء الحذف'];
    }
}

// دوال إدارة خطط الأسعار
function createPricingPlan($service_id, $name, $price, $features, $down_payment = 0, $interest_rate = 0, $installments_count = 1) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        // حساب مبلغ القسط (هذه الدالة تستخدم للخطط القديمة، يجب تحديثها لاحقاً)
        $remaining_amount = $price - $down_payment;
        $installment_amount = $installments_count > 1 ? round($remaining_amount / $installments_count, 2) : 0;

        $sql = "INSERT INTO pricing_plans (service_id, name, price, features, down_payment, interest_rate, installments_count, installment_amount, allow_installment)
                VALUES (:service_id, :name, :price, :features, :down_payment, :interest_rate, :installments_count, :installment_amount, :allow_installment)";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':price', $price);
        $stmt->bindParam(':features', $features);
        $stmt->bindParam(':down_payment', $down_payment);
        $stmt->bindParam(':interest_rate', $interest_rate);
        $stmt->bindParam(':installments_count', $installments_count);
        $stmt->bindParam(':installment_amount', $installment_amount);
        $allow_installment = $installments_count > 1 ? 1 : 0;
        $stmt->bindParam(':allow_installment', $allow_installment);

        if ($stmt->execute()) {
            return $conn->lastInsertId();
        }
        return false;
    } catch (Exception $e) {
        error_log("Error creating pricing plan: " . $e->getMessage());
        return false;
    }
}

function getPricingPlanById($id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $sql = "SELECT pp.*, s.name as service_name FROM pricing_plans pp
                JOIN services s ON pp.service_id = s.id
                WHERE pp.id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting pricing plan: " . $e->getMessage());
        return false;
    }
}

// دوال إضافية للمستخدمين (الدوال الأساسية موجودة في config.php)

// دالة للحصول على معلومات المستخدم الحالي
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    try {
        $database = new Database();
        $conn = $database->getConnection();

        $sql = "SELECT * FROM users WHERE id = :user_id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting current user: " . $e->getMessage());
        return null;
    }
}

// دالة للحصول على اسم المستخدم الحالي
function getCurrentUserName() {
    $user = getCurrentUser();
    return $user ? $user['name'] : 'مستخدم';
}
?>

