/**
 * Dark Mode Toggle Functionality for FutureWay
 */

class DarkModeManager {
    constructor() {
        this.storageKey = 'futureway-dark-mode';
        this.init();
    }

    init() {
        // تحميل الحالة المحفوظة أو استخدام تفضيل النظام
        const savedTheme = localStorage.getItem(this.storageKey);
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        let initialTheme;
        if (savedTheme) {
            initialTheme = savedTheme;
        } else {
            initialTheme = systemPrefersDark ? 'dark' : 'light';
        }

        this.setTheme(initialTheme);
        this.bindEvents();
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        document.body.setAttribute('data-theme', theme);
        localStorage.setItem(this.storageKey, theme);
        this.updateHeaderToggleButton(theme);

        // إرسال حدث مخصص لإعلام المكونات الأخرى
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    updateHeaderToggleButton(theme) {
        const headerToggle = document.getElementById('darkModeToggle');
        if (headerToggle) {
            const icon = headerToggle.querySelector('i');
            if (icon) {
                if (theme === 'dark') {
                    icon.className = 'fas fa-sun';
                } else {
                    icon.className = 'fas fa-moon';
                }
            }
        }
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }



    bindEvents() {
        // ربط زر التبديل في الهيدر
        const headerToggle = document.getElementById('darkModeToggle');
        if (headerToggle) {
            headerToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // ربط أي أزرار تبديل أخرى
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('dark-mode-toggle') && e.target.id !== 'darkModeToggle') {
                this.toggleTheme();
            }
        });

        // مراقبة تغيير تفضيل النظام
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            // تطبيق تفضيل النظام فقط إذا لم يكن هناك تفضيل محفوظ
            if (!localStorage.getItem(this.storageKey)) {
                this.setTheme(e.matches ? 'dark' : 'light');
            }
        });

        // ربط اختصار لوحة المفاتيح (Ctrl/Cmd + Shift + D)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }

    getCurrentTheme() {
        return document.documentElement.getAttribute('data-theme');
    }

    isDarkMode() {
        return this.getCurrentTheme() === 'dark';
    }

    // دالة لتطبيق الوضع المظلم على المخططات والرسوم البيانية
    applyChartTheme(chartInstance) {
        const isDark = this.isDarkMode();
        const textColor = isDark ? '#ffffff' : '#212529';
        const gridColor = isDark ? '#495057' : '#dee2e6';
        const backgroundColor = isDark ? '#2d2d2d' : '#ffffff';

        if (chartInstance && chartInstance.options) {
            // تحديث ألوان المخطط
            if (chartInstance.options.plugins && chartInstance.options.plugins.legend) {
                chartInstance.options.plugins.legend.labels.color = textColor;
            }
            
            if (chartInstance.options.scales) {
                Object.keys(chartInstance.options.scales).forEach(scaleKey => {
                    const scale = chartInstance.options.scales[scaleKey];
                    if (scale.ticks) scale.ticks.color = textColor;
                    if (scale.grid) scale.grid.color = gridColor;
                });
            }

            chartInstance.update();
        }
    }

    // دالة لتطبيق الوضع المظلم على جداول البيانات
    applyDataTableTheme() {
        const isDark = this.isDarkMode();
        
        if ($.fn.DataTable) {
            $('.data-table').each(function() {
                const table = $(this).DataTable();
                if (table) {
                    // إعادة رسم الجدول لتطبيق الأنماط الجديدة
                    table.draw();
                }
            });
        }
    }

    // دالة لتطبيق الوضع المظلم على Select2
    applySelect2Theme() {
        if ($.fn.select2) {
            // البحث عن العناصر التي تم تهيئتها بـ Select2 فعلياً
            $('select').each(function() {
                const $element = $(this);
                // التحقق من وجود بيانات Select2
                if ($element.hasClass('select2-hidden-accessible')) {
                    try {
                        // الحصول على الإعدادات الحالية
                        const currentOptions = $element.data('select2').options.options;
                        const dropdownParent = currentOptions.dropdownParent || $('body');

                        // إعادة تهيئة Select2 مع الوضع الجديد والإعدادات المحفوظة
                        $element.select2('destroy').select2({
                            theme: 'bootstrap-5',
                            width: '100%',
                            dropdownParent: dropdownParent,
                            placeholder: currentOptions.placeholder || '',
                            allowClear: currentOptions.allowClear || false
                        });
                    } catch (error) {
                        console.warn('Error reinitializing Select2:', error);
                        // إعادة تهيئة بسيطة في حالة الخطأ
                        try {
                            $element.select2('destroy').select2({
                                theme: 'bootstrap-5',
                                width: '100%'
                            });
                        } catch (fallbackError) {
                            console.error('Fallback Select2 initialization failed:', fallbackError);
                        }
                    }
                }
            });
        }
    }

    // دالة شاملة لتطبيق الوضع المظلم على جميع المكونات
    applyThemeToComponents() {
        this.applyDataTableTheme();
        this.applySelect2Theme();
        
        // إرسال حدث للمكونات المخصصة
        window.dispatchEvent(new CustomEvent('darkModeApplied', { 
            detail: { isDark: this.isDarkMode() } 
        }));
    }
}

// تهيئة مدير الوضع المظلم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.darkModeManager = new DarkModeManager();
    
    // تطبيق الوضع المظلم على المكونات بعد التحميل
    setTimeout(() => {
        window.darkModeManager.applyThemeToComponents();
    }, 100);
});

// مراقبة تغيير الوضع المظلم وتطبيقه على المكونات
window.addEventListener('themeChanged', function(e) {
    setTimeout(() => {
        window.darkModeManager.applyThemeToComponents();
    }, 50);
});

// دوال مساعدة للاستخدام العام
window.toggleDarkMode = function() {
    if (window.darkModeManager) {
        window.darkModeManager.toggleTheme();
    }
};

window.setDarkMode = function(enabled) {
    if (window.darkModeManager) {
        window.darkModeManager.setTheme(enabled ? 'dark' : 'light');
    }
};

window.isDarkMode = function() {
    return window.darkModeManager ? window.darkModeManager.isDarkMode() : false;
};

// دالة لتطبيق الوضع المظلم على مخطط معين
window.applyDarkModeToChart = function(chartInstance) {
    if (window.darkModeManager) {
        window.darkModeManager.applyChartTheme(chartInstance);
    }
};

// CSS إضافي للتحسينات
const additionalStyles = `
    <style>
        /* تحسينات إضافية للوضع المظلم */
        .dark-mode-toggle {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .dark-mode-toggle:hover {
            transform: scale(1.1);
        }
        
        .dark-mode-toggle:active {
            transform: scale(0.95);
        }
        
        /* تأثير النبضة للزر */
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
        }
        
        .dark-mode-toggle.pulse {
            animation: pulse 2s infinite;
        }
        
        /* تحسين الانتقال بين الأوضاع */
        [data-theme="dark"] {
            color-scheme: dark;
        }
        
        [data-theme="light"] {
            color-scheme: light;
        }
        
        /* تحسين شكل الزر في الشريط العلوي */
        .navbar .dark-mode-toggle {
            background: transparent;
            border: 2px solid currentColor;
            border-radius: 20px;
            padding: 5px 10px;
            font-size: 0.9rem;
        }
        
        .navbar .dark-mode-toggle:hover {
            background: currentColor;
            color: var(--navbar-bg);
        }
    </style>
`;

// إضافة الأنماط الإضافية
document.head.insertAdjacentHTML('beforeend', additionalStyles);

