<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requireAdmin();

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // إحصائيات سريعة
    $stats = [];

    // عدد المستخدمين
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users");
    $stmt->execute();
    $stats['users_count'] = $stmt->fetchColumn();

    // عدد الخدمات
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM services");
    $stmt->execute();
    $stats['services_count'] = $stmt->fetchColumn();

    // عدد خطط الأسعار
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM pricing_plans");
    $stmt->execute();
    $stats['pricing_plans_count'] = $stmt->fetchColumn();

    // عدد خطط التقسيط
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM installment_plans");
    $stmt->execute();
    $stats['installment_plans_count'] = $stmt->fetchColumn();

    // عدد خطط التقسيط النشطة
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM installment_plans WHERE status = 'active'");
    $stmt->execute();
    $stats['active_installments'] = $stmt->fetchColumn();

    // إجمالي المبالغ في خطط التقسيط
    $stmt = $pdo->prepare("SELECT SUM(total_amount) FROM installment_plans");
    $stmt->execute();
    $stats['total_installment_amount'] = $stmt->fetchColumn() ?: 0;

    // إجمالي المبالغ المدفوعة (المقدم + الأقساط المدفوعة)
    $stmt = $pdo->prepare("
        SELECT
            (SELECT SUM(down_payment) FROM installment_plans) +
            (SELECT SUM(amount) FROM payments WHERE status = 'paid') as total_paid
    ");
    $stmt->execute();
    $stats['total_paid_amount'] = $stmt->fetchColumn() ?: 0;

    // عدد الدفعات المتأخرة
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payments WHERE status = 'overdue'");
    $stmt->execute();
    $stats['overdue_payments'] = $stmt->fetchColumn();

    // المستخدمين الجدد هذا الشهر
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
    $stmt->execute();
    $stats['new_users_month'] = $stmt->fetchColumn();

    // خطط التقسيط الجديدة هذا الشهر
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM installment_plans WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
    $stmt->execute();
    $stats['new_installments_month'] = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $stats = [
        'users_count' => 0,
        'services_count' => 0,
        'active_installments' => 0,
        'total_revenue' => 0,
        'new_users_month' => 0,
        'sales_month' => 0
    ];
}

$page_title = 'لوحة تحكم الإدارة';
include __DIR__ . '/../includes/admin_header.php';
?>

<div class="admin-dashboard">
    <!-- Welcome Section -->
    <div class="welcome-section mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-title">مرحباً بك في لوحة تحكم الإدارة</h1>
                <p class="welcome-subtitle">إدارة شاملة لنظام FutureWay</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="admin-avatar">
                    <i class="fas fa-user-shield fa-4x text-primary"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-section mb-4">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['users_count']) ?></h3>
                        <p class="stat-label">إجمالي المستخدمين</p>
                        <small class="stat-change text-success">
                            <i class="fas fa-arrow-up"></i>
                            <?= $stats['new_users_month'] ?> جديد هذا الشهر
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['services_count']) ?></h3>
                        <p class="stat-label">الخدمات المتاحة</p>
                        <small class="stat-change text-muted">
                            <i class="fas fa-chart-line"></i>
                            خدمات نشطة
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['active_installments']) ?></h3>
                        <p class="stat-label">خطط التقسيط النشطة</p>
                        <small class="stat-change text-info">
                            <i class="fas fa-clock"></i>
                            قيد التنفيذ
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['total_paid_amount'], 2) ?></h3>
                        <p class="stat-label">إجمالي المدفوعات (جنيه)</p>
                        <small class="stat-change text-success">
                            <i class="fas fa-arrow-up"></i>
                            من خطط التقسيط
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- صف ثاني من الإحصائيات -->
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-secondary">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['pricing_plans_count']) ?></h3>
                        <p class="stat-label">خطط الأسعار</p>
                        <small class="stat-change text-muted">
                            <i class="fas fa-tags"></i>
                            خطط متاحة
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['installment_plans_count']) ?></h3>
                        <p class="stat-label">إجمالي خطط التقسيط</p>
                        <small class="stat-change text-info">
                            <i class="fas fa-plus"></i>
                            <?= $stats['new_installments_month'] ?> جديد هذا الشهر
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['overdue_payments']) ?></h3>
                        <p class="stat-label">دفعات متأخرة</p>
                        <small class="stat-change text-danger">
                            <i class="fas fa-clock"></i>
                            تحتاج متابعة
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['total_installment_amount'], 2) ?></h3>
                        <p class="stat-label">إجمالي قيمة الأقساط (جنيه)</p>
                        <small class="stat-change text-success">
                            <i class="fas fa-money-bill"></i>
                            جميع الخطط
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions mb-4">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-bolt me-2"></i>
                إجراءات سريعة
            </h2>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="users.php" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="action-content">
                        <h5>إدارة المستخدمين</h5>
                        <p>عرض وإدارة حسابات المستخدمين</p>
                    </div>
                </a>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="services.php" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="action-content">
                        <h5>إدارة الخدمات</h5>
                        <p>إضافة وتعديل الخدمات المتاحة</p>
                    </div>
                </a>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="pricing_plans.php" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="action-content">
                        <h5>خطط الأسعار</h5>
                        <p>إدارة خطط الأسعار والتقسيط</p>
                    </div>
                </a>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="installments.php" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="action-content">
                        <h5>خطط التقسيط</h5>
                        <p>متابعة وإدارة خطط التقسيط</p>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="recent-activity">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-clock me-2"></i>
                النشاط الأخير
            </h2>
        </div>
        
        <div class="activity-list">
            <?php
            try {
                // جلب آخر الأنشطة
                $stmt = $pdo->prepare("
                    SELECT 'user_registered' as type, name as title, created_at 
                    FROM users 
                    WHERE is_admin = FALSE 
                    ORDER BY created_at DESC 
                    LIMIT 5
                ");
                $stmt->execute();
                $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (empty($activities)): ?>
                    <div class="empty-activity">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد أنشطة حديثة</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($activities as $activity): ?>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">
                                    انضم المستخدم <strong><?= htmlspecialchars($activity['title']) ?></strong> إلى النظام
                                </p>
                                <small class="activity-time">
                                    <?= timeAgo($activity['created_at']) ?>
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif;
            } catch (Exception $e) {
                echo '<div class="empty-activity"><p class="text-muted">خطأ في تحميل الأنشطة</p></div>';
            }
            ?>
        </div>
    </div>
</div>

<style>
.admin-dashboard {
    padding: 20px 0;
    background-color: #121212;
    color: #e0e0e0;
    min-height: 100vh;
}

/* Force dark theme for all admin dashboard elements */
.admin-dashboard * {
    color: #e0e0e0;
}

.admin-dashboard h1, .admin-dashboard h2, .admin-dashboard h3,
.admin-dashboard h4, .admin-dashboard h5, .admin-dashboard h6 {
    color: #e0e0e0 !important;
}

.admin-dashboard p, .admin-dashboard span, .admin-dashboard div {
    color: #e0e0e0 !important;
}

.admin-dashboard .text-muted {
    color: #b0b0b0 !important;
}

.welcome-section {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.admin-avatar {
    text-align: center;
}

.stat-card {
    background: #1e1e1e;
    border: 1px solid #333333;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
    color: #e0e0e0;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.5);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
}

.stat-icon i {
    font-size: 1.5rem;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #6c7ce7;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #b0b0b0;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.stat-change {
    font-size: 0.8rem;
    color: #e0e0e0;
}

.section-header {
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #e0e0e0;
    margin: 0;
}

.action-card {
    display: block;
    background: #1e1e1e;
    border: 1px solid #333333;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    text-decoration: none;
    color: #e0e0e0;
    height: 100%;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.5);
    text-decoration: none;
    color: #e0e0e0;
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.action-icon i {
    font-size: 1.5rem;
    color: white;
}

.action-content h5 {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.action-content p {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

.activity-list {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
}

.activity-icon i {
    font-size: 1rem;
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin-bottom: 0.25rem;
    color: #2c3e50;
}

.activity-time {
    color: #6c757d;
}

.empty-activity {
    text-align: center;
    padding: 3rem 0;
}

@media (max-width: 768px) {
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .action-card {
        margin-bottom: 1rem;
    }
}
</style>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>

