<?php
session_start();
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

$page_title = 'البحث';
$search_query = $_GET['q'] ?? '';
$search_results = [];
$total_results = 0;

if (!empty($search_query)) {
    try {
        $db = new Database();
        $pdo = $db->getConnection();
        
        // البحث في الخدمات
        $stmt = $pdo->prepare("
            SELECT 'service' as type, id, name as title, description, image, 
                   'services.php' as url_base
            FROM services 
            WHERE (name LIKE ? OR description LIKE ?) AND is_active = TRUE
            
            UNION ALL
            
            SELECT 'pricing_plan' as type, pp.id, 
                   CONCAT(s.name, ' - ', pp.name) as title, 
                   pp.features as description, s.image,
                   'services.php' as url_base
            FROM pricing_plans pp
            JOIN services s ON pp.service_id = s.id
            WHERE (pp.name LIKE ? OR pp.features LIKE ? OR s.name LIKE ?) 
                  AND pp.is_active = TRUE AND s.is_active = TRUE
            
            ORDER BY title ASC
            LIMIT 20
        ");
        
        $search_term = '%' . $search_query . '%';
        $stmt->execute([$search_term, $search_term, $search_term, $search_term, $search_term]);
        $search_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $total_results = count($search_results);
        
    } catch (Exception $e) {
        error_log("خطأ في البحث: " . $e->getMessage());
    }
}

include __DIR__ . '/includes/header.php';
?>

<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="text-center mb-5">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-search text-primary me-3"></i>
                    البحث في FutureWay
                </h1>
                <p class="lead text-muted">ابحث عن الخدمات والحلول التي تناسب احتياجاتك</p>
            </div>

            <!-- مربع البحث -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8">
                    <form method="GET" action="search.php">
                        <div class="input-group input-group-lg">
                            <input type="text" 
                                   class="form-control" 
                                   name="q" 
                                   value="<?= htmlspecialchars($search_query) ?>"
                                   placeholder="ابحث عن الخدمات، خطط الأسعار، أو أي محتوى..."
                                   required>
                            <button class="btn btn-primary px-4" type="submit">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <?php if (!empty($search_query)): ?>
                <!-- نتائج البحث -->
                <div class="mb-4">
                    <h3 class="h4 mb-3">
                        نتائج البحث عن: "<span class="text-primary"><?= htmlspecialchars($search_query) ?></span>"
                    </h3>
                    <p class="text-muted">تم العثور على <?= $total_results ?> نتيجة</p>
                </div>

                <?php if ($total_results > 0): ?>
                    <!-- عرض النتائج -->
                    <div class="row">
                        <?php foreach ($search_results as $result): ?>
                            <div class="col-lg-6 col-xl-4 mb-4">
                                <div class="card h-100 shadow-sm">
                                    <?php if (!empty($result['image'])): ?>
                                        <img src="<?= htmlspecialchars($result['image']) ?>" 
                                             class="card-img-top" 
                                             style="height: 200px; object-fit: cover;"
                                             alt="<?= htmlspecialchars($result['title']) ?>">
                                    <?php endif; ?>
                                    
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h5 class="card-title"><?= htmlspecialchars($result['title']) ?></h5>
                                            <span class="badge bg-<?= $result['type'] === 'service' ? 'primary' : 'success' ?>">
                                                <?= $result['type'] === 'service' ? 'خدمة' : 'خطة سعر' ?>
                                            </span>
                                        </div>
                                        
                                        <p class="card-text text-muted">
                                            <?= htmlspecialchars(substr($result['description'], 0, 150)) ?>
                                            <?= strlen($result['description']) > 150 ? '...' : '' ?>
                                        </p>
                                    </div>
                                    
                                    <div class="card-footer bg-transparent">
                                        <?php if ($result['type'] === 'service'): ?>
                                            <a href="service_details.php?id=<?= $result['id'] ?>" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-eye me-2"></i>
                                                عرض التفاصيل
                                            </a>
                                        <?php else: ?>
                                            <a href="services.php#plan-<?= $result['id'] ?>" 
                                               class="btn btn-success btn-sm">
                                                <i class="fas fa-credit-card me-2"></i>
                                                عرض الخطة
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <!-- لا توجد نتائج -->
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-search fa-4x text-muted"></i>
                        </div>
                        <h4 class="text-muted mb-3">لم يتم العثور على نتائج</h4>
                        <p class="text-muted mb-4">
                            لم نتمكن من العثور على أي نتائج تطابق بحثك "<strong><?= htmlspecialchars($search_query) ?></strong>"
                        </p>
                        
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">اقتراحات للبحث:</h6>
                            <ul class="list-unstyled text-muted">
                                <li>• تأكد من كتابة الكلمات بشكل صحيح</li>
                                <li>• جرب استخدام كلمات مختلفة أو أكثر عمومية</li>
                                <li>• تأكد من عدم وجود أخطاء إملائية</li>
                                <li>• جرب البحث بكلمة واحدة بدلاً من جملة كاملة</li>
                            </ul>
                        </div>
                        
                        <a href="services.php" class="btn btn-primary">
                            <i class="fas fa-box me-2"></i>
                            تصفح جميع الخدمات
                        </a>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <!-- صفحة البحث الافتراضية -->
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="text-center mb-5">
                            <h4 class="mb-4">ابحث عن الخدمة المناسبة لك</h4>
                            <p class="text-muted">
                                يمكنك البحث عن الخدمات، خطط الأسعار، أو أي محتوى آخر في موقعنا
                            </p>
                        </div>

                        <!-- اقتراحات البحث -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    اقتراحات البحث الشائعة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="?q=تطوير" class="btn btn-outline-primary btn-sm">تطوير</a>
                                    <a href="?q=تصميم" class="btn btn-outline-primary btn-sm">تصميم</a>
                                    <a href="?q=تسويق" class="btn btn-outline-primary btn-sm">تسويق</a>
                                    <a href="?q=استشارات" class="btn btn-outline-primary btn-sm">استشارات</a>
                                    <a href="?q=تقسيط" class="btn btn-outline-primary btn-sm">تقسيط</a>
                                    <a href="?q=خطة" class="btn btn-outline-primary btn-sm">خطة أساسية</a>
                                </div>
                            </div>
                        </div>

                        <!-- روابط سريعة -->
                        <div class="row mt-5">
                            <div class="col-md-6 mb-3">
                                <div class="card text-center h-100">
                                    <div class="card-body">
                                        <i class="fas fa-box fa-2x text-primary mb-3"></i>
                                        <h6 class="card-title">تصفح الخدمات</h6>
                                        <p class="card-text text-muted">اكتشف جميع الخدمات المتاحة</p>
                                        <a href="services.php" class="btn btn-primary btn-sm">تصفح الآن</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card text-center h-100">
                                    <div class="card-body">
                                        <i class="fas fa-phone fa-2x text-success mb-3"></i>
                                        <h6 class="card-title">اتصل بنا</h6>
                                        <p class="card-text text-muted">تحدث مع فريق الدعم</p>
                                        <a href="contact.php" class="btn btn-success btn-sm">اتصل الآن</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// تحسين تجربة البحث
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="q"]');
    
    // تركيز على مربع البحث
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
    
    // إضافة تأثيرات للكروت
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
