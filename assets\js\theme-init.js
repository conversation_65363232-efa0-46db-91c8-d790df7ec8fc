/**
 * Dark Mode Only - FutureWay
 * Force dark mode for all pages
 */

(function() {
    'use strict';

    // Force dark theme always
    function applyDarkTheme() {
        document.documentElement.setAttribute('data-theme', 'dark');
        document.documentElement.classList.add('dark-mode');

        // Apply to body if it exists
        if (document.body) {
            document.body.setAttribute('data-theme', 'dark');
            document.body.classList.add('dark-mode');
        }

        // Dispatch theme change event
        if (window.CustomEvent) {
            window.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme: 'dark' }
            }));
        }
    }

    // Initialize dark theme immediately (before DOM is ready)
    applyDarkTheme();

    // Setup when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Remove any toggle buttons since we're dark mode only
        const toggleButtons = document.querySelectorAll('#darkModeToggle, .dark-mode-toggle');
        toggleButtons.forEach(button => {
            if (button) {
                button.style.display = 'none';
            }
        });

        console.log('FutureWay: Dark mode only system initialized');
    });

    // Export functions for global use
    window.FutureWayTheme = {
        get: function() {
            return 'dark';
        }
    };
    
})();
