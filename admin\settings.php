<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requireAdmin();

$success = '';
$error = '';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // معالجة حفظ الإعدادات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $settings_to_save = [];
        
        // إعدادات النظام العامة
        if (isset($_POST['system_name'])) {
            $settings_to_save['system_name'] = $_POST['system_name'];
        }
        if (isset($_POST['system_description'])) {
            $settings_to_save['system_description'] = $_POST['system_description'];
        }
        if (isset($_POST['contact_email'])) {
            $settings_to_save['contact_email'] = $_POST['contact_email'];
        }
        if (isset($_POST['contact_phone'])) {
            $settings_to_save['contact_phone'] = $_POST['contact_phone'];
        }
        
        // إعدادات WAWP
        if (isset($_POST['wawp_token'])) {
            $settings_to_save['wawp_token'] = $_POST['wawp_token'];
        }
        if (isset($_POST['wawp_instance_id'])) {
            $settings_to_save['wawp_instance_id'] = $_POST['wawp_instance_id'];
        }
        
        // إعدادات التقسيط
        if (isset($_POST['default_interest_rate'])) {
            $settings_to_save['default_interest_rate'] = $_POST['default_interest_rate'];
        }
        if (isset($_POST['min_down_payment'])) {
            $settings_to_save['min_down_payment'] = $_POST['min_down_payment'];
        }
        if (isset($_POST['max_installments'])) {
            $settings_to_save['max_installments'] = $_POST['max_installments'];
        }
        
        // إعدادات الأمان
        if (isset($_POST['otp_expiry_minutes'])) {
            $settings_to_save['otp_expiry_minutes'] = $_POST['otp_expiry_minutes'];
        }
        if (isset($_POST['max_login_attempts'])) {
            $settings_to_save['max_login_attempts'] = $_POST['max_login_attempts'];
        }
        
        // إعدادات الرسائل
        if (isset($_POST['otp_message_template'])) {
            $settings_to_save['otp_message_template'] = $_POST['otp_message_template'];
        }
        if (isset($_POST['welcome_message_template'])) {
            $settings_to_save['welcome_message_template'] = $_POST['welcome_message_template'];
        }
        if (isset($_POST['installment_reminder_template'])) {
            $settings_to_save['installment_reminder_template'] = $_POST['installment_reminder_template'];
        }

        // معالجة إضافة مدير جديد
        if (isset($_POST['add_admin'])) {
            $admin_name = trim($_POST['admin_name'] ?? '');
            $admin_phone = trim($_POST['admin_phone'] ?? '');
            $admin_governorate = trim($_POST['admin_governorate'] ?? '');
            $admin_country = trim($_POST['admin_country'] ?? '');
            $admin_profession = trim($_POST['admin_profession'] ?? '');
            $admin_national_id = trim($_POST['admin_national_id'] ?? '');

            if (empty($admin_name) || empty($admin_phone) || empty($admin_governorate) || empty($admin_country) || empty($admin_profession)) {
                $error = 'جميع الحقول مطلوبة لإضافة مدير جديد';
            } else {
                // تنظيف رقم الهاتف
                require_once __DIR__ . '/../includes/functions.php';
                $clean_phone = cleanPhone($admin_phone);

                // التحقق من عدم وجود المستخدم
                $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ?");
                $stmt->execute([$clean_phone]);

                if ($stmt->rowCount() > 0) {
                    $error = 'رقم الهاتف مسجل بالفعل في النظام';
                } else {
                    // إنشاء OTP
                    $otp = generateOTP();
                    $otp_expires = date('Y-m-d H:i:s', time() + (5 * 60)); // 5 دقائق

                    // إدراج المدير الجديد
                    $stmt = $pdo->prepare("
                        INSERT INTO users (name, phone, governorate, country, profession, national_id, is_admin, is_verified, otp_code, otp_expires_at)
                        VALUES (?, ?, ?, ?, ?, ?, TRUE, FALSE, ?, ?)
                    ");
                    $stmt->execute([$admin_name, $clean_phone, $admin_governorate, $admin_country, $admin_profession, $admin_national_id, $otp, $otp_expires]);

                    // إضافة إلى جدول admin_users
                    $stmt = $pdo->prepare("INSERT INTO admin_users (phone, is_verified) VALUES (?, FALSE)");
                    $stmt->execute([$clean_phone]);

                    // إرسال OTP عبر WAWP إذا كان متاحاً
                    try {
                        require_once __DIR__ . '/../config/wawp.php';
                        $wawp = new WAWP();
                        $message = "مرحباً $admin_name، تم إنشاء حساب مدير لك في FutureWay. رمز التحقق: $otp";
                        $wawp_result = $wawp->sendMessage($clean_phone, $message);

                        if (!$wawp_result['success']) {
                            error_log("WAWP Error in admin creation: " . ($wawp_result['message'] ?? 'Unknown error'));
                        }
                    } catch (Exception $e) {
                        error_log("WAWP Exception in admin creation: " . $e->getMessage());
                    }

                    $success = "تم إنشاء حساب المدير بنجاح. تم إرسال رمز التحقق إلى $clean_phone";
                }
            }
        }

        // حفظ الإعدادات في قاعدة البيانات
        if (!empty($settings_to_save)) {
            foreach ($settings_to_save as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO system_settings (setting_key, setting_value)
                    VALUES (?, ?)
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }

            if (empty($success)) {
                $success = 'تم حفظ الإعدادات بنجاح!';
            }
        }
    }
    
    // جلب الإعدادات الحالية
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM system_settings");
    $stmt->execute();
    $current_settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    // جلب قائمة المديرين
    $stmt = $pdo->prepare("
        SELECT u.id, u.name, u.phone, u.governorate, u.country, u.profession, u.is_verified, u.created_at
        FROM users u
        WHERE u.is_admin = TRUE
        ORDER BY u.created_at DESC
    ");
    $stmt->execute();
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
    $current_settings = [];
}

$page_title = 'إعدادات النظام';
include __DIR__ . '/../includes/admin_header.php';
?>

<div class="settings-page">
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات النظام
                </h1>
                <p class="page-subtitle">إدارة وتكوين إعدادات النظام العامة</p>
            </div>
        </div>
    </div>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <form method="POST" class="settings-form">
        <!-- إعدادات النظام العامة -->
        <div class="settings-section mb-4">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-info-circle me-2"></i>
                    إعدادات النظام العامة
                </h3>
            </div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="system_name" class="form-label">اسم النظام</label>
                        <input type="text" class="form-control" id="system_name" name="system_name" 
                               value="<?= htmlspecialchars($current_settings['system_name'] ?? 'FutureWay') ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="contact_email" class="form-label">البريد الإلكتروني للتواصل</label>
                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                               value="<?= htmlspecialchars($current_settings['contact_email'] ?? '') ?>">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_phone" class="form-label">رقم الهاتف للتواصل</label>
                        <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                               value="<?= htmlspecialchars($current_settings['contact_phone'] ?? '') ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="system_description" class="form-label">وصف النظام</label>
                        <textarea class="form-control" id="system_description" name="system_description" rows="3"><?= htmlspecialchars($current_settings['system_description'] ?? 'نظام إدارة الخدمات والتقسيط') ?></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات WAWP -->
        <div class="settings-section mb-4">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fab fa-whatsapp me-2"></i>
                    إعدادات WAWP (WhatsApp API)
                </h3>
            </div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="wawp_token" class="form-label">WAWP Token</label>
                        <input type="text" class="form-control" id="wawp_token" name="wawp_token" 
                               value="<?= htmlspecialchars($current_settings['wawp_token'] ?? '') ?>"
                               placeholder="أدخل WAWP Token">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="wawp_instance_id" class="form-label">Instance ID</label>
                        <input type="text" class="form-control" id="wawp_instance_id" name="wawp_instance_id" 
                               value="<?= htmlspecialchars($current_settings['wawp_instance_id'] ?? '') ?>"
                               placeholder="أدخل Instance ID">
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    يتم استخدام WAWP لإرسال رسائل WhatsApp التلقائية مثل رموز التحقق والإشعارات.
                </div>
            </div>
        </div>

        <!-- إعدادات التقسيط -->
        <div class="settings-section mb-4">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-credit-card me-2"></i>
                    إعدادات التقسيط والدفع
                </h3>
            </div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="default_interest_rate" class="form-label">معدل الفائدة الافتراضي (%)</label>
                        <input type="number" class="form-control" id="default_interest_rate" name="default_interest_rate" 
                               value="<?= htmlspecialchars($current_settings['default_interest_rate'] ?? '5') ?>"
                               min="0" max="100" step="0.1">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="min_down_payment" class="form-label">الحد الأدنى للدفعة المقدمة (%)</label>
                        <input type="number" class="form-control" id="min_down_payment" name="min_down_payment" 
                               value="<?= htmlspecialchars($current_settings['min_down_payment'] ?? '20') ?>"
                               min="0" max="100" step="1">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="max_installments" class="form-label">الحد الأقصى لعدد الأقساط</label>
                        <input type="number" class="form-control" id="max_installments" name="max_installments" 
                               value="<?= htmlspecialchars($current_settings['max_installments'] ?? '24') ?>"
                               min="1" max="60">
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات الأمان -->
        <div class="settings-section mb-4">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-shield-alt me-2"></i>
                    إعدادات الأمان
                </h3>
            </div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="otp_expiry_minutes" class="form-label">مدة انتهاء صلاحية OTP (بالدقائق)</label>
                        <input type="number" class="form-control" id="otp_expiry_minutes" name="otp_expiry_minutes" 
                               value="<?= htmlspecialchars($current_settings['otp_expiry_minutes'] ?? '5') ?>"
                               min="1" max="60">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="max_login_attempts" class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                        <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                               value="<?= htmlspecialchars($current_settings['max_login_attempts'] ?? '5') ?>"
                               min="1" max="20">
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات الرسائل -->
        <div class="settings-section mb-4">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-envelope me-2"></i>
                    قوالب الرسائل
                </h3>
            </div>
            <div class="section-content">
                <div class="mb-3">
                    <label for="otp_message_template" class="form-label">قالب رسالة OTP</label>
                    <textarea class="form-control" id="otp_message_template" name="otp_message_template" rows="3"><?= htmlspecialchars($current_settings['otp_message_template'] ?? 'رمز التحقق الخاص بك في FutureWay هو: {code}' . "\n" . 'مرحباً بك في منصة FutureWay للخدمات المتقدمة') ?></textarea>
                    <small class="form-text text-muted">استخدم {code} لإدراج رمز التحقق</small>
                </div>
                <div class="mb-3">
                    <label for="welcome_message_template" class="form-label">قالب رسالة الترحيب</label>
                    <textarea class="form-control" id="welcome_message_template" name="welcome_message_template" rows="3"><?= htmlspecialchars($current_settings['welcome_message_template'] ?? 'مرحباً بك في FutureWay! تم إنشاء حسابك بنجاح.' . "\n" . 'أهلاً بك في منصة FutureWay للخدمات المتقدمة') ?></textarea>
                </div>
                <div class="mb-3">
                    <label for="installment_reminder_template" class="form-label">قالب تذكير الأقساط</label>
                    <textarea class="form-control" id="installment_reminder_template" name="installment_reminder_template" rows="3"><?= htmlspecialchars($current_settings['installment_reminder_template'] ?? 'تذكير: لديك قسط مستحق بقيمة {amount} في تاريخ {date}') ?></textarea>
                    <small class="form-text text-muted">استخدم {amount} للمبلغ و {date} للتاريخ</small>
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="form-actions">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save me-2"></i>
                حفظ الإعدادات
            </button>
            <button type="reset" class="btn btn-secondary btn-lg">
                <i class="fas fa-undo me-2"></i>
                إعادة تعيين
            </button>
        </div>
    </form>

    <!-- قسم إدارة المديرين -->
    <div class="settings-section mb-4">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-user-shield me-2"></i>
                إدارة المديرين
            </h3>
        </div>
        <div class="section-content">
            <!-- إضافة مدير جديد -->
            <div class="add-admin-section mb-4">
                <h5 class="mb-3">إضافة مدير جديد</h5>
                <form method="POST" class="admin-form">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="admin_name" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="admin_name" name="admin_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="admin_phone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="admin_phone" name="admin_phone"
                                   placeholder="+201012345678" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="admin_governorate" class="form-label">المحافظة</label>
                            <input type="text" class="form-control" id="admin_governorate" name="admin_governorate" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="admin_country" class="form-label">الدولة</label>
                            <input type="text" class="form-control" id="admin_country" name="admin_country"
                                   value="السعودية" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="admin_profession" class="form-label">المهنة</label>
                            <input type="text" class="form-control" id="admin_profession" name="admin_profession" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="admin_national_id" class="form-label">رقم الهوية (اختياري)</label>
                            <input type="text" class="form-control" id="admin_national_id" name="admin_national_id">
                        </div>
                    </div>
                    <button type="submit" name="add_admin" class="btn btn-success">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مدير
                    </button>
                </form>
            </div>

            <!-- قائمة المديرين الحاليين -->
            <div class="admins-list">
                <h5 class="mb-3">المديرين الحاليين</h5>
                <?php if (empty($admins)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        لا يوجد مديرين مسجلين في النظام حالياً
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>المحافظة</th>
                                    <th>المهنة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($admins as $admin): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($admin['name']) ?></td>
                                        <td><?= htmlspecialchars($admin['phone']) ?></td>
                                        <td><?= htmlspecialchars($admin['governorate']) ?></td>
                                        <td><?= htmlspecialchars($admin['profession']) ?></td>
                                        <td>
                                            <?php if ($admin['is_verified']): ?>
                                                <span class="badge bg-success">مفعل</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">في انتظار التفعيل</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= date('Y-m-d H:i', strtotime($admin['created_at'])) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

</div>

<style>
.settings-page {
    padding: 20px 0;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.settings-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 30px;
    border-bottom: 1px solid #dee2e6;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.section-content {
    padding: 30px;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-actions {
    text-align: center;
    padding: 30px 0;
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
    border-radius: 10px;
    margin: 0 10px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    border: none;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(108, 117, 125, 0.3);
}

.alert {
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

@media (max-width: 768px) {
    .page-title {
        font-size: 1.5rem;
    }

    .section-content {
        padding: 20px;
    }

    .btn-lg {
        display: block;
        width: 100%;
        margin: 10px 0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأكيد قبل إعادة التعيين
    const resetBtn = document.querySelector('button[type="reset"]');
    if (resetBtn) {
        resetBtn.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من إعادة تعيين جميع الحقول؟')) {
                e.preventDefault();
            }
        });
    }

    // تحقق من صحة البيانات
    const form = document.querySelector('.settings-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const interestRate = document.getElementById('default_interest_rate').value;
            const downPayment = document.getElementById('min_down_payment').value;

            if (interestRate < 0 || interestRate > 100) {
                alert('معدل الفائدة يجب أن يكون بين 0 و 100');
                e.preventDefault();
                return;
            }

            if (downPayment < 0 || downPayment > 100) {
                alert('نسبة الدفعة المقدمة يجب أن تكون بين 0 و 100');
                e.preventDefault();
                return;
            }
        });
    }

    // إخفاء التنبيهات تلقائياً
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('alert-success')) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            }
        });
    }, 5000);
});
</script>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>
