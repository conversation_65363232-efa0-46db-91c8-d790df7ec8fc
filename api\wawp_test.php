<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../config/wawp.php';
require_once __DIR__ . '/../includes/wawp_integration.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    $result = ['success' => false, 'message' => 'عملية غير صحيحة'];
    
    switch ($action) {
        case 'test_connection':
            $wawp = new WAWP();
            $result = $wawp->testConnection();
            break;
            
        case 'send_message':
            $phone = $_POST['phone'] ?? '';
            $message = $_POST['message'] ?? '';
            
            if (empty($phone) || empty($message)) {
                $result = ['success' => false, 'message' => 'رقم الهاتف والرسالة مطلوبان'];
                break;
            }
            
            $wawp = new WAWP();
            $result = $wawp->sendMessage($phone, $message);
            break;
            
        case 'send_otp':
            $phone = $_POST['phone'] ?? '';
            $code = $_POST['code'] ?? '';
            $type = $_POST['type'] ?? 'login';
            
            if (empty($phone) || empty($code)) {
                $result = ['success' => false, 'message' => 'رقم الهاتف ورمز التحقق مطلوبان'];
                break;
            }
            
            $wawp_integration = new WAWPIntegration();
            $result = $wawp_integration->sendOTPCode($phone, $code, $type);
            break;
            
        case 'check_instance_status':
            $wawp = new WAWP();
            $result = $wawp->checkInstanceStatus();
            break;
            
        case 'get_qr_code':
            $wawp = new WAWP();
            $result = $wawp->getQRCode();
            break;
            
        case 'create_instance':
            $wawp = new WAWP();
            $result = $wawp->createInstance();
            break;
            
        case 'reboot_instance':
            $wawp = new WAWP();
            $result = $wawp->rebootInstance();
            break;
            
        case 'send_welcome':
            $phone = $_POST['phone'] ?? '';
            $name = $_POST['name'] ?? 'المستخدم';
            
            if (empty($phone)) {
                $result = ['success' => false, 'message' => 'رقم الهاتف مطلوب'];
                break;
            }
            
            $wawp_integration = new WAWPIntegration();
            $result = $wawp_integration->sendWelcomeMessage($phone, $name);
            break;
            
        case 'send_payment_reminder':
            $phone = $_POST['phone'] ?? '';
            $amount = $_POST['amount'] ?? '100';
            $due_date = $_POST['due_date'] ?? date('Y-m-d');
            
            if (empty($phone)) {
                $result = ['success' => false, 'message' => 'رقم الهاتف مطلوب'];
                break;
            }
            
            $payment_details = [
                'amount' => $amount,
                'due_date' => $due_date,
                'service_name' => 'خدمة تجريبية'
            ];
            
            $wawp_integration = new WAWPIntegration();
            $result = $wawp_integration->sendPaymentReminder($phone, $payment_details);
            break;
            
        case 'get_settings':
            $db = new Database();
            $pdo = $db->getConnection();
            
            $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN ('wawp_token', 'wawp_instance_id')");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            $result = [
                'success' => true,
                'data' => [
                    'has_token' => !empty($settings['wawp_token']),
                    'has_instance_id' => !empty($settings['wawp_instance_id']),
                    'token_preview' => !empty($settings['wawp_token']) ? '***' . substr($settings['wawp_token'], -4) : null,
                    'instance_id' => $settings['wawp_instance_id'] ?? null
                ]
            ];
            break;
            
        default:
            $result = ['success' => false, 'message' => 'عملية غير مدعومة'];
    }
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
