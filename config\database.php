<?php
class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $conn;
    
    public function __construct($host = null, $db_name = null, $username = null, $password = null) {
        if ($host && $db_name && $username && $password !== null) {
            $this->host = $host;
            $this->db_name = $db_name;
            $this->username = $username;
            $this->password = $password;
        } else {
            // قراءة الإعدادات من ملف التكوين إذا كان موجوداً
            if (file_exists(__DIR__ . '/db_config.php')) {
                require_once __DIR__ . '/db_config.php';
                $this->host = DB_HOST;
                $this->db_name = DB_NAME;
                $this->username = DB_USERNAME;
                $this->password = DB_PASSWORD;
            }
        }
    }
    
    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO("mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                                $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // تعيين التايم زون لقاعدة البيانات
            $this->conn->exec("SET time_zone = '+02:00'");
        } catch(PDOException $exception) {
            throw new Exception("Connection error: " . $exception->getMessage());
        }
        
        return $this->conn;
    }
    
    public function testConnection() {
        try {
            $conn = new PDO("mysql:host=" . $this->host . ";charset=utf8mb4", 
                          $this->username, $this->password);
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            return true;
        } catch(PDOException $exception) {
            return false;
        }
    }
    
    public function createDatabase() {
        try {
            $conn = new PDO("mysql:host=" . $this->host . ";charset=utf8mb4", 
                          $this->username, $this->password);
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "CREATE DATABASE IF NOT EXISTS " . $this->db_name . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $conn->exec($sql);
            return true;
        } catch(PDOException $exception) {
            return false;
        }
    }
    
    public function createTables() {
        try {
            $conn = $this->getConnection();
            $tables_created = [];

            // جدول المستخدمين
            echo "إنشاء جدول المستخدمين...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                phone VARCHAR(20) UNIQUE NOT NULL,
                governorate VARCHAR(100) NOT NULL,
                country VARCHAR(100) NOT NULL,
                profession VARCHAR(100) NOT NULL,
                national_id VARCHAR(20) NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                is_verified BOOLEAN DEFAULT FALSE,
                otp_code VARCHAR(6) NULL,
                otp_expires_at TIMESTAMP NULL,
                login_attempts INT DEFAULT 0,
                last_login_attempt TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);
            $tables_created[] = 'users';
            echo "✓ تم إنشاء جدول المستخدمين<br>";

            // جدول مديري النظام
            echo "إنشاء جدول مديري النظام...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                phone VARCHAR(20) UNIQUE NOT NULL,
                is_verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);
            $tables_created[] = 'admin_users';
            echo "✓ تم إنشاء جدول مديري النظام<br>";

            // جدول الخدمات
            echo "إنشاء جدول الخدمات...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS services (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                image VARCHAR(255),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);
            $tables_created[] = 'services';
            echo "✓ تم إنشاء جدول الخدمات<br>";

            // التحقق من العمود is_active في جدول services
            echo "فحص وتحديث أعمدة جدول الخدمات...<br>";
            try {
                $stmt = $conn->prepare("SHOW COLUMNS FROM services LIKE 'is_active'");
                $stmt->execute();
                $column_exists = $stmt->fetch();

                if (!$column_exists) {
                    echo "إضافة العمود is_active إلى جدول services...<br>";
                    $conn->exec("ALTER TABLE services ADD COLUMN is_active BOOLEAN DEFAULT TRUE AFTER image");
                    echo "✓ تم إضافة العمود is_active بنجاح<br>";
                } else {
                    echo "العمود is_active موجود بالفعل في جدول services<br>";
                }
            } catch (Exception $e) {
                echo "تحذير: فشل في إضافة العمود is_active لجدول services - " . $e->getMessage() . "<br>";
            }

            // جدول خطط الأسعار
            echo "إنشاء جدول خطط الأسعار...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS pricing_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                service_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                features TEXT,
                down_payment DECIMAL(10,2) DEFAULT 0,
                interest_rate DECIMAL(5,2) DEFAULT 0,
                interest_type ENUM('fixed', 'percentage') DEFAULT 'percentage',
                interest_value DECIMAL(10,2) DEFAULT 0,
                installments_count INT DEFAULT 1,
                installment_amount DECIMAL(10,2) DEFAULT 0,
                allow_installment BOOLEAN DEFAULT TRUE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);
            $tables_created[] = 'pricing_plans';
            echo "✓ تم إنشاء جدول خطط الأسعار<br>";

            // التحقق من الأعمدة المطلوبة وإضافتها إذا لم تكن موجودة
            echo "فحص وتحديث أعمدة جدول خطط الأسعار...<br>";
            $required_columns = [
                'interest_value' => "ALTER TABLE pricing_plans ADD COLUMN interest_value DECIMAL(10,2) DEFAULT 0 AFTER interest_type",
                'is_active' => "ALTER TABLE pricing_plans ADD COLUMN is_active BOOLEAN DEFAULT TRUE AFTER allow_installment",
                'updated_at' => "ALTER TABLE pricing_plans ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at"
            ];

            foreach ($required_columns as $column => $alter_sql) {
                try {
                    // التحقق من وجود العمود
                    $stmt = $conn->prepare("SHOW COLUMNS FROM pricing_plans LIKE ?");
                    $stmt->execute([$column]);
                    $column_exists = $stmt->fetch();

                    if (!$column_exists) {
                        echo "إضافة العمود $column...<br>";
                        $conn->exec($alter_sql);
                        echo "✓ تم إضافة العمود $column بنجاح<br>";
                    } else {
                        echo "العمود $column موجود بالفعل<br>";
                    }
                } catch (Exception $e) {
                    echo "تحذير: فشل في إضافة العمود $column - " . $e->getMessage() . "<br>";
                }
            }

            // جدول خطط التقسيط
            echo "إنشاء جدول خطط التقسيط...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS installment_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                pricing_plan_id INT NOT NULL,
                total_amount DECIMAL(10,2) NOT NULL,
                down_payment DECIMAL(10,2) NOT NULL,
                remaining_amount DECIMAL(10,2) NOT NULL,
                installment_amount DECIMAL(10,2) NOT NULL,
                installments_count INT NOT NULL,
                paid_installments INT DEFAULT 0,
                purchase_date DATE NOT NULL DEFAULT (CURRENT_DATE),
                down_payment_paid BOOLEAN DEFAULT FALSE,
                status ENUM('pending', 'active', 'completed', 'cancelled', 'suspended') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (pricing_plan_id) REFERENCES pricing_plans(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);
            $tables_created[] = 'installment_plans';
            echo "✓ تم إنشاء جدول خطط التقسيط<br>";

            // التحقق من الأعمدة المطلوبة في جدول installment_plans
            echo "فحص وتحديث أعمدة جدول خطط التقسيط...<br>";
            $installment_columns = [
                'purchase_date' => "ALTER TABLE installment_plans ADD COLUMN purchase_date DATE NOT NULL DEFAULT (CURRENT_DATE) AFTER pricing_plan_id",
                'down_payment_paid' => "ALTER TABLE installment_plans ADD COLUMN down_payment_paid BOOLEAN DEFAULT FALSE AFTER paid_installments"
            ];

            foreach ($installment_columns as $column => $alter_sql) {
                try {
                    $stmt = $conn->prepare("SHOW COLUMNS FROM installment_plans LIKE ?");
                    $stmt->execute([$column]);
                    $column_exists = $stmt->fetch();

                    if (!$column_exists) {
                        echo "إضافة العمود $column إلى جدول installment_plans...<br>";
                        $conn->exec($alter_sql);
                        echo "✓ تم إضافة العمود $column بنجاح<br>";
                    } else {
                        echo "العمود $column موجود بالفعل في جدول installment_plans<br>";
                    }
                } catch (Exception $e) {
                    echo "تحذير: فشل في إضافة العمود $column لجدول installment_plans - " . $e->getMessage() . "<br>";
                }
            }

            // جدول المدفوعات
            echo "إنشاء جدول المدفوعات...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                installment_plan_id INT NOT NULL,
                installment_number INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                due_date DATE NOT NULL,
                paid_date DATE NULL,
                paid_at TIMESTAMP NULL,
                status ENUM('pending', 'paid', 'overdue') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (installment_plan_id) REFERENCES installment_plans(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);
            $tables_created[] = 'payments';
            echo "✓ تم إنشاء جدول المدفوعات<br>";

            // التحقق من الأعمدة المطلوبة في جدول payments
            echo "فحص وتحديث أعمدة جدول المدفوعات...<br>";
            $payment_columns = [
                'paid_at' => "ALTER TABLE payments ADD COLUMN paid_at TIMESTAMP NULL AFTER paid_date",
                'updated_at' => "ALTER TABLE payments ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at"
            ];

            foreach ($payment_columns as $column => $alter_sql) {
                try {
                    $stmt = $conn->prepare("SHOW COLUMNS FROM payments LIKE ?");
                    $stmt->execute([$column]);
                    $column_exists = $stmt->fetch();

                    if (!$column_exists) {
                        echo "إضافة العمود $column إلى جدول payments...<br>";
                        $conn->exec($alter_sql);
                        echo "✓ تم إضافة العمود $column بنجاح<br>";
                    } else {
                        echo "العمود $column موجود بالفعل في جدول payments<br>";
                    }
                } catch (Exception $e) {
                    echo "تحذير: فشل في إضافة العمود $column لجدول payments - " . $e->getMessage() . "<br>";
                }
            }

            // جدول رموز التحقق OTP
            echo "إنشاء جدول رموز التحقق...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS otp_codes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                phone VARCHAR(20) NOT NULL,
                code VARCHAR(6) NOT NULL,
                type ENUM('login', 'register', 'admin_setup') NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                is_used BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);
            $tables_created[] = 'otp_codes';
            echo "✓ تم إنشاء جدول رموز التحقق<br>";

            // جدول إعدادات النظام
            echo "إنشاء جدول إعدادات النظام...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS system_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);
            $tables_created[] = 'system_settings';
            echo "✓ تم إنشاء جدول إعدادات النظام<br>";

            // جدول سجل الرسائل
            echo "إنشاء جدول سجل الرسائل...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS message_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                phone VARCHAR(20) NOT NULL,
                message TEXT NOT NULL,
                status ENUM('sent', 'failed', 'pending') DEFAULT 'pending',
                response_data TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);
            $tables_created[] = 'message_logs';
            echo "✓ تم إنشاء جدول سجل الرسائل<br>";

            // جدول الخدمات المشتراة
            echo "إنشاء جدول الخدمات المشتراة...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS purchased_services (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                pricing_plan_id INT NOT NULL,
                service_id INT NOT NULL,
                total_amount DECIMAL(10,2) NOT NULL,
                payment_method ENUM('cash', 'installment') DEFAULT 'cash',
                status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
                purchase_date DATE NOT NULL,
                completion_date DATE NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (pricing_plan_id) REFERENCES pricing_plans(id) ON DELETE CASCADE,
                FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);
            $tables_created[] = 'purchased_services';
            echo "✓ تم إنشاء جدول الخدمات المشتراة<br>";

            // جدول بوابات الدفع
            echo "إنشاء جدول بوابات الدفع...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS payment_gateways (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                image VARCHAR(255) NULL,
                payment_method TEXT NOT NULL,
                description TEXT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);
            $tables_created[] = 'payment_gateways';
            echo "✓ تم إنشاء جدول بوابات الدفع<br>";

            // جدول طلبات الدفع
            echo "إنشاء جدول طلبات الدفع...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS payment_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                installment_plan_id INT NULL,
                payment_id INT NULL,
                gateway_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                payment_type ENUM('installment', 'down_payment', 'service_purchase') NOT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                payment_proofs TEXT NULL,
                admin_notes TEXT NULL,
                processed_by INT NULL,
                processed_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (installment_plan_id) REFERENCES installment_plans(id) ON DELETE CASCADE,
                FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE,
                FOREIGN KEY (gateway_id) REFERENCES payment_gateways(id) ON DELETE CASCADE,
                FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
            )";
            $conn->exec($sql);
            $tables_created[] = 'payment_requests';
            echo "✓ تم إنشاء جدول طلبات الدفع<br>";

            // إدراج بوابات دفع افتراضية
            echo "إدراج بوابات الدفع الافتراضية...<br>";
            $check_gateways = $conn->query("SELECT COUNT(*) FROM payment_gateways")->fetchColumn();

            if ($check_gateways == 0) {
                $gateways = [
                    ['فودافون كاش', 'رقم المحفظة: 01234567890', 'ادفع بسهولة عبر فودافون كاش'],
                    ['إنستاباي', 'رقم المحفظة: 01234567890', 'الدفع السريع عبر إنستاباي'],
                    ['البنك الأهلي المصري', 'رقم الحساب: 1234567890123456\nاسم صاحب الحساب: شركة المستقبل', 'تحويل بنكي للبنك الأهلي المصري'],
                    ['بنك مصر', 'رقم الحساب: 9876543210987654\nاسم صاحب الحساب: شركة المستقبل', 'تحويل بنكي لبنك مصر']
                ];

                $stmt = $conn->prepare("INSERT INTO payment_gateways (name, payment_method, description, is_active) VALUES (?, ?, ?, TRUE)");

                foreach ($gateways as $gateway) {
                    $stmt->execute($gateway);
                }

                echo "✓ تم إدراج بوابات الدفع الافتراضية<br>";
            } else {
                echo "✓ بوابات الدفع موجودة بالفعل<br>";
            }

            // إنشاء جدول purchases كبديل أو مرادف لـ purchased_services
            echo "إنشاء جدول المشتريات (purchases)...<br>";
            $sql = "CREATE TABLE IF NOT EXISTS purchases (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                pricing_plan_id INT NOT NULL,
                service_id INT NOT NULL,
                total_amount DECIMAL(10,2) NOT NULL,
                payment_method ENUM('cash', 'installment') DEFAULT 'cash',
                status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
                purchase_date DATE NOT NULL,
                completion_date DATE NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (pricing_plan_id) REFERENCES pricing_plans(id) ON DELETE CASCADE,
                FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);
            $tables_created[] = 'purchases';
            echo "✓ تم إنشاء جدول المشتريات<br>";

            // إنشاء مجلدات الرفع
            echo "<br>إنشاء مجلدات الرفع...<br>";
            $upload_dirs = [
                __DIR__ . '/../uploads/payment_gateways/',
                __DIR__ . '/../uploads/payment_proofs/',
                __DIR__ . '/../assets/uploads/'
            ];

            foreach ($upload_dirs as $dir) {
                if (!is_dir($dir)) {
                    if (mkdir($dir, 0755, true)) {
                        echo "✓ تم إنشاء مجلد: " . basename(dirname($dir)) . "/" . basename($dir) . "<br>";
                    } else {
                        echo "❌ فشل في إنشاء مجلد: " . basename(dirname($dir)) . "/" . basename($dir) . "<br>";
                    }
                } else {
                    echo "✓ مجلد موجود: " . basename(dirname($dir)) . "/" . basename($dir) . "<br>";
                }
            }

            // إضافة عمود service_id لجدول purchased_services إذا لم يكن موجوداً
            echo "<br>تحديث الجداول الموجودة...<br>";
            try {
                $conn->exec("ALTER TABLE purchased_services ADD COLUMN service_id INT NOT NULL AFTER pricing_plan_id");
                echo "✓ تم إضافة عمود service_id لجدول purchased_services<br>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    echo "✓ عمود service_id موجود بالفعل في جدول purchased_services<br>";
                } else {
                    echo "❌ خطأ في إضافة عمود service_id: " . $e->getMessage() . "<br>";
                }
            }

            // إضافة foreign key لـ service_id في purchased_services
            try {
                $conn->exec("ALTER TABLE purchased_services ADD FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE");
                echo "✓ تم إضافة foreign key لـ service_id في purchased_services<br>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') !== false || strpos($e->getMessage(), 'already exists') !== false) {
                    echo "✓ foreign key موجود بالفعل لـ service_id في purchased_services<br>";
                } else {
                    echo "❌ خطأ في إضافة foreign key: " . $e->getMessage() . "<br>";
                }
            }

            // تحديث البيانات الموجودة في purchased_services لإضافة service_id
            try {
                $stmt = $conn->query("SELECT COUNT(*) FROM purchased_services WHERE service_id IS NULL OR service_id = 0");
                $null_count = $stmt->fetchColumn();

                if ($null_count > 0) {
                    $conn->exec("
                        UPDATE purchased_services ps
                        JOIN pricing_plans pp ON ps.pricing_plan_id = pp.id
                        SET ps.service_id = pp.service_id
                        WHERE ps.service_id IS NULL OR ps.service_id = 0
                    ");
                    echo "✓ تم تحديث $null_count سجل في purchased_services بـ service_id<br>";
                } else {
                    echo "✓ جميع السجلات في purchased_services تحتوي على service_id<br>";
                }
            } catch (Exception $e) {
                echo "❌ خطأ في تحديث service_id: " . $e->getMessage() . "<br>";
            }

            // نسخ البيانات من purchased_services إلى purchases إذا كان فارغاً
            try {
                $stmt = $conn->query("SELECT COUNT(*) FROM purchases");
                $purchases_count = $stmt->fetchColumn();

                if ($purchases_count == 0) {
                    $conn->exec("
                        INSERT INTO purchases (user_id, pricing_plan_id, service_id, total_amount, payment_method, status, purchase_date, created_at, updated_at)
                        SELECT user_id, pricing_plan_id, service_id, total_amount, payment_method, status, purchase_date, created_at, updated_at
                        FROM purchased_services
                    ");

                    $stmt = $conn->query("SELECT COUNT(*) FROM purchases");
                    $copied_count = $stmt->fetchColumn();
                    echo "✓ تم نسخ $copied_count سجل من purchased_services إلى purchases<br>";
                } else {
                    echo "✓ جدول purchases يحتوي على بيانات بالفعل<br>";
                }
            } catch (Exception $e) {
                echo "❌ خطأ في نسخ البيانات: " . $e->getMessage() . "<br>";
            }

            echo "<br><strong>تم إنشاء " . count($tables_created) . " جداول بنجاح:</strong><br>";
            foreach ($tables_created as $table) {
                echo "- $table<br>";
            }

            return true;
        } catch(PDOException $exception) {
            throw new Exception("خطأ في إنشاء الجداول: " . $exception->getMessage());
        }
    }
    
    public function saveConfig() {
        $config = "<?php\nreturn [\n";
        $config .= "    'host' => '" . $this->host . "',\n";
        $config .= "    'db_name' => '" . $this->db_name . "',\n";
        $config .= "    'username' => '" . $this->username . "',\n";
        $config .= "    'password' => '" . $this->password . "'\n";
        $config .= "];";
        
        return file_put_contents(__DIR__ . '/db_config.php', $config);
    }
}
?>

