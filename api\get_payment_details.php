<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$payment_id = (int)($_GET['id'] ?? 0);

if (!$payment_id) {
    echo json_encode(['success' => false, 'message' => 'معرف المدفوع مطلوب']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // جلب بيانات المدفوع مع التفاصيل
    $stmt = $pdo->prepare("
        SELECT p.*, 
               u.name as user_name, u.phone as user_phone,
               pp.name as plan_name, pp.price as plan_price,
               s.name as service_name,
               ip.status as installment_status
        FROM payments p
        JOIN installment_plans ip ON p.installment_plan_id = ip.id
        JOIN users u ON ip.user_id = u.id
        JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
        JOIN services s ON pp.service_id = s.id
        WHERE p.id = ?
    ");
    $stmt->execute([$payment_id]);
    $payment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$payment) {
        echo json_encode(['success' => false, 'message' => 'المدفوع غير موجود']);
        exit();
    }
    
    echo json_encode([
        'success' => true,
        'payment' => $payment
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>

