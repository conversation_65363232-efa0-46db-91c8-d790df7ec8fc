<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Fixes Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>JavaScript Fixes Test</h1>
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Payment System Test</h5>
                        <button class="btn btn-primary" onclick="testPaymentSystem()">
                            Test Payment Modal
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Console Output</h5>
                        <div id="console-output" style="background: #f8f9fa; padding: 10px; height: 200px; overflow-y: auto;">
                            <!-- Console messages will appear here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Test Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a test modal to verify JavaScript fixes.</p>
                    <button class="btn btn-success" id="focusedButton">Focused Button</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');

        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.className = type === 'error' ? 'text-danger' : 'text-success';
            div.textContent = `[${type.toUpperCase()}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        function testPaymentSystem() {
            console.log('Testing payment system...');
            
            try {
                // Test modal opening with focus management
                const modal = new bootstrap.Modal(document.getElementById('testModal'));
                
                // Focus management is now handled globally
                
                modal.show();
                
                // Focus on button after modal opens
                modalElement.addEventListener('shown.bs.modal', function () {
                    document.getElementById('focusedButton').focus();
                    console.log('Button focused in modal');
                });
                
                console.log('Modal opened successfully');
                
                // Auto close after 3 seconds
                setTimeout(() => {
                    modal.hide();
                    console.log('Modal closed automatically');
                }, 3000);
                
            } catch (error) {
                console.error('Error testing payment system:', error.message);
            }
        }

        // Add global focus management for all modals (like in the actual app)
        document.addEventListener('hide.bs.modal', function(event) {
            const modal = event.target;
            const focusedElement = modal.querySelector(':focus');
            if (focusedElement) {
                focusedElement.blur();
                console.log('Global focus management: Focus removed from element');
            }
        });

        // Test for syntax errors
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, testing for syntax errors...');
            
            try {
                // Test variable declarations
                const testVar1 = 'test1';
                const testVar2 = 'test2';
                console.log('Variable declarations: OK');
                
                // Test function declarations
                function testFunction() {
                    return 'test';
                }
                console.log('Function declarations: OK');
                
                // Test object methods
                const testObj = {
                    method1: function() { return 'method1'; },
                    method2: function() { return 'method2'; }
                };
                console.log('Object methods: OK');
                
                console.log('All syntax tests passed!');
                
            } catch (error) {
                console.error('Syntax error detected:', error.message);
            }
        });
    </script>
</body>
</html>
