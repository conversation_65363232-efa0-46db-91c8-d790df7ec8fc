/* Dark Mode Styles for FutureWay */

/* Root Variables - Dark Mode Only */
:root {
    --primary-color: #6c7ce7;
    --secondary-color: #8b5fbf;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    /* Dark Mode Colors - Applied Globally */
    --bg-color: #121212;
    --bg-secondary: #1e1e1e;
    --text-color: #e0e0e0;
    --text-muted: #b0b0b0;
    --border-color: #333333;
    --card-bg: #1e1e1e;
    --navbar-bg: #1a1a1a;
    --sidebar-bg: #1a1a1a;
    --input-bg: #2a2a2a;
    --input-border: #404040;
    --table-bg: #1e1e1e;
    --table-stripe: #2a2a2a;
    --modal-bg: #1e1e1e;
    --dropdown-bg: #2a2a2a;
    --shadow: rgba(0, 0, 0, 0.5);
}

/* Apply dark theme globally */
html, body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

/* Base Styles - Dark Mode Only */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    box-shadow: 0 0.125rem 0.25rem var(--shadow);
    color: var(--text-color);
}

.card-header {
    background-color: var(--bg-secondary);
    border-bottom-color: var(--border-color);
}

.card-footer {
    background-color: var(--bg-secondary);
    border-top-color: var(--border-color);
}

/* Navigation */
.navbar {
    background-color: var(--navbar-bg) !important;
    border-bottom: 1px solid var(--border-color);
}

.navbar-brand,
.navbar-nav .nav-link {
    color: var(--text-color) !important;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
}

.sidebar .nav-link {
    color: var(--text-color);
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
}

/* Forms */
.form-control,
.form-select {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

.form-control:focus,
.form-select:focus {
    background-color: var(--input-bg);
    border-color: var(--primary-color);
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-color);
}

/* Tables */
.table {
    --bs-table-bg: var(--table-bg);
    --bs-table-striped-bg: var(--table-stripe);
    color: var(--text-color);
}

.table th {
    border-bottom-color: var(--border-color);
    color: var(--text-color);
}

.table td {
    border-top-color: var(--border-color);
}

/* Modals */
.modal-content {
    background-color: var(--modal-bg);
    border-color: var(--border-color);
}

.modal-header {
    border-bottom-color: var(--border-color);
}

.modal-footer {
    border-top-color: var(--border-color);
}

.modal-title {
    color: var(--text-color);
}

/* Dropdowns */
.dropdown-menu {
    background-color: var(--dropdown-bg);
    border-color: var(--border-color);
}

.dropdown-item {
    color: var(--text-color);
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--bg-secondary);
    color: var(--text-color);
}

/* Alerts */
.alert {
    border-color: var(--border-color);
}

/* Text Colors */
.text-muted {
    color: var(--text-muted) !important;
}

/* Borders */
.border {
    border-color: var(--border-color) !important;
}

/* Background Colors */
.bg-light {
    background-color: var(--bg-secondary) !important;
}

/* Dark Mode Toggle Button */
.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1050;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: 0 4px 12px var(--shadow);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dark-mode-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--shadow);
}

.dark-mode-toggle:active {
    transform: translateY(0);
}

/* Dark Mode Toggle in Navbar */
.navbar .dark-mode-toggle {
    position: static;
    width: 40px;
    height: 40px;
    font-size: 1rem;
    margin-left: 10px;
}

/* Statistics Cards Dark Mode */
.stat-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 10px var(--shadow);
}

.stat-number {
    color: var(--text-color);
}

.stat-label {
    color: var(--text-muted);
}

/* Page Header */
.page-header {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
}

.page-title {
    color: var(--text-color);
}

/* Breadcrumb */
.breadcrumb {
    background-color: var(--bg-secondary);
}

.breadcrumb-item a {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-muted);
}

/* List Group */
.list-group-item {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

.list-group-item:hover {
    background-color: var(--bg-secondary);
}

/* Progress Bars */
.progress {
    background-color: var(--bg-secondary);
}

/* Pagination */
.page-link {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

.page-link:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Tooltips */
.tooltip-inner {
    background-color: var(--dark-color);
    color: white;
}

/* Popovers */
.popover {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

.popover-header {
    background-color: var(--bg-secondary);
    border-bottom-color: var(--border-color);
    color: var(--text-color);
}

.popover-body {
    color: var(--text-color);
}

/* Custom Scrollbar for Dark Mode */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Animation for theme transition */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Dark mode specific adjustments for charts and graphs */
[data-theme="dark"] .chart-container {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1rem;
}

/* Dark mode for select2 if used */
[data-theme="dark"] .select2-container--default .select2-selection--single {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

[data-theme="dark"] .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: var(--text-color);
}

[data-theme="dark"] .select2-dropdown {
    background-color: var(--dropdown-bg);
    border-color: var(--border-color);
}

/* Dark mode for modern components */
[data-theme="dark"] .modern-header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
}

[data-theme="dark"] .hero-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    color: var(--text-color);
}

[data-theme="dark"] .hero-title {
    color: var(--text-color);
}

[data-theme="dark"] .hero-subtitle {
    color: var(--text-muted);
}

[data-theme="dark"] .modern-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .service-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .service-card .card-title {
    color: var(--text-color);
}

[data-theme="dark"] .service-card .card-text {
    color: var(--text-muted);
}

[data-theme="dark"] .service-card .service-icon {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

[data-theme="dark"] .stats-section {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

[data-theme="dark"] .stat-label {
    color: var(--text-muted);
}

[data-theme="dark"] .features-section {
    background-color: var(--bg-color);
}

[data-theme="dark"] .feature-title {
    color: var(--text-color);
}

[data-theme="dark"] .feature-description {
    color: var(--text-muted);
}

[data-theme="dark"] .feature-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

[data-theme="dark"] .modern-footer {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

[data-theme="dark"] .footer-divider {
    border-color: var(--border-color);
}

[data-theme="dark"] .social-link {
    background: rgba(255,255,255,0.1);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .social-link:hover {
    background: var(--primary-color);
}

[data-theme="dark"] .contact-item i {
    color: var(--primary-color);
}

[data-theme="dark"] .swiper-button-next,
[data-theme="dark"] .swiper-button-prev {
    background: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .swiper-pagination-bullet {
    background: var(--text-muted);
}

[data-theme="dark"] .swiper-pagination-bullet-active {
    background: var(--primary-color);
}

/* Additional Dark Mode Text Improvements */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
    color: var(--text-color);
}

[data-theme="dark"] p {
    color: var(--text-color);
}

[data-theme="dark"] .text-muted {
    color: var(--text-muted) !important;
}

[data-theme="dark"] .lead {
    color: var(--text-color);
}

[data-theme="dark"] .display-1,
[data-theme="dark"] .display-2,
[data-theme="dark"] .display-3,
[data-theme="dark"] .display-4,
[data-theme="dark"] .display-5,
[data-theme="dark"] .display-6 {
    color: var(--text-color);
}

/* Dark mode for buttons */
[data-theme="dark"] .btn-light {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .btn-light:hover {
    background-color: var(--bg-secondary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

[data-theme="dark"] .btn-outline-light {
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .btn-outline-light:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Dark mode for alerts */
[data-theme="dark"] .alert {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: rgba(23, 162, 184, 0.3);
    color: #7dd3fc;
}

/* Dark mode for contact section */
[data-theme="dark"] .contact-info {
    color: var(--text-muted);
}

[data-theme="dark"] .contact-item span {
    color: var(--text-color);
}

/* Dark mode for copyright and footer text */
[data-theme="dark"] .copyright {
    color: var(--text-muted);
}

[data-theme="dark"] .footer-description {
    color: var(--text-muted);
}

[data-theme="dark"] .footer-links a {
    color: var(--text-muted);
}

[data-theme="dark"] .footer-links a:hover {
    color: var(--text-color);
}

[data-theme="dark"] .footer-links-inline a {
    color: var(--text-muted);
}

[data-theme="dark"] .footer-links-inline a:hover {
    color: var(--text-color);
}

/* Dark mode for enhanced components */
[data-theme="dark"] .enhanced-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .glass-card {
    background: rgba(30, 30, 30, 0.8);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

/* Dark mode for gradient text - make it visible */
[data-theme="dark"] .text-gradient {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

[data-theme="dark"] .gradient-text-primary {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Dark mode for specific sections */
[data-theme="dark"] .bg-light {
    background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .text-dark {
    color: var(--text-color) !important;
}

[data-theme="dark"] .border-light {
    border-color: var(--border-color) !important;
}

/* Dark mode for hero buttons */
[data-theme="dark"] .hero-buttons .btn-light {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .hero-buttons .btn-light:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

[data-theme="dark"] .hero-buttons .btn-outline-light {
    border-color: rgba(255,255,255,0.3);
    color: var(--text-color);
}

[data-theme="dark"] .hero-buttons .btn-outline-light:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
    color: white;
}

/* Dark mode for stat numbers */
[data-theme="dark"] .stat-number {
    color: var(--primary-color);
}

/* Dark mode for contact cards */
[data-theme="dark"] .contact-item span {
    color: var(--text-color);
}

/* Dark mode for navbar brand */
[data-theme="dark"] .navbar-brand {
    color: white !important;
}

/* Dark mode for navigation links */
[data-theme="dark"] .navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
}

[data-theme="dark"] .navbar-nav .nav-link:hover {
    color: white !important;
}

[data-theme="dark"] .navbar-nav .nav-link.active {
    color: white !important;
}

[data-theme="dark"] .select2-container--default .select2-results__option {
    color: var(--text-color);
}

[data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary-color);
}

/* Dark mode for DataTables if used */
[data-theme="dark"] .dataTables_wrapper {
    color: var(--text-color);
}

[data-theme="dark"] .dataTables_filter input {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

[data-theme="dark"] .dataTables_length select {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dark-mode-toggle {
        bottom: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

/* Print styles - force light mode for printing */
@media print {
    * {
        background-color: white !important;
        color: black !important;
        border-color: #dee2e6 !important;
    }
}

