<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requireAdmin();

$message = '';
$message_type = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_payment_status') {
        $id = (int)($_POST['id'] ?? 0);
        $status = sanitizeInput($_POST['status'] ?? '');
        
        if ($id > 0 && in_array($status, ['pending', 'paid', 'overdue', 'cancelled'])) {
            try {
                $db = new Database();
                $pdo = $db->getConnection();
                
                $paid_at = ($status === 'paid') ? ', paid_at = NOW()' : '';
                $sql = "UPDATE payments SET status = ?, updated_at = NOW() $paid_at WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                
                if ($stmt->execute([$status, $id])) {
                    $message = 'تم تحديث حالة المدفوع بنجاح';
                    $message_type = 'success';
                    
                    // إذا تم تسجيل الدفع، تحقق من اكتمال خطة التقسيط
                    if ($status === 'paid') {
                        $stmt = $pdo->prepare("SELECT installment_plan_id FROM payments WHERE id = ?");
                        $stmt->execute([$id]);
                        $payment = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($payment) {
                            $stmt = $pdo->prepare("
                                SELECT COUNT(*) as total, 
                                       COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid
                                FROM payments 
                                WHERE installment_plan_id = ?
                            ");
                            $stmt->execute([$payment['installment_plan_id']]);
                            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
                            
                            if ($stats['total'] == $stats['paid']) {
                                $stmt = $pdo->prepare("
                                    UPDATE installment_plans 
                                    SET status = 'completed', updated_at = NOW() 
                                    WHERE id = ?
                                ");
                                $stmt->execute([$payment['installment_plan_id']]);
                            }
                        }
                    }
                } else {
                    $message = 'حدث خطأ أثناء تحديث الحالة';
                    $message_type = 'danger';
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ في النظام';
                $message_type = 'danger';
            }
        } else {
            $message = 'بيانات غير صحيحة';
            $message_type = 'danger';
        }
    } elseif ($action === 'send_reminder') {
        $id = (int)($_POST['id'] ?? 0);
        
        try {
            $db = new Database();
            $pdo = $db->getConnection();
            
            // جلب بيانات المدفوع والمستخدم
            $stmt = $pdo->prepare("
                SELECT p.*, u.name as user_name, u.phone as user_phone,
                       pp.name as plan_name, s.name as service_name
                FROM payments p
                JOIN installment_plans ip ON p.installment_plan_id = ip.id
                JOIN users u ON ip.user_id = u.id
                JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
                JOIN services s ON pp.service_id = s.id
                WHERE p.id = ?
            ");
            $stmt->execute([$id]);
            $payment = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($payment) {
                // هنا يمكن إضافة كود إرسال رسالة تذكير عبر WAWP أو SMS
                // للآن سنسجل فقط أنه تم إرسال تذكير
                
                $message = 'تم إرسال تذكير للمستخدم بنجاح';
                $message_type = 'success';
            } else {
                $message = 'المدفوع غير موجود';
                $message_type = 'danger';
            }
        } catch (Exception $e) {
            $message = 'حدث خطأ أثناء إرسال التذكير';
            $message_type = 'danger';
        }
    }
}

// تحديث المدفوعات المتأخرة
try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $stmt = $pdo->prepare("
        UPDATE payments 
        SET status = 'overdue' 
        WHERE status = 'pending' 
        AND due_date < CURDATE()
    ");
    $stmt->execute();
} catch (Exception $e) {
    // تجاهل الأخطاء في التحديث التلقائي
}

// جلب المدفوعات مع التفاصيل
try {
    $stmt = $pdo->prepare("
        SELECT p.*, 
               u.name as user_name, u.phone as user_phone,
               pp.name as plan_name, pp.price as plan_price,
               s.name as service_name,
               ip.status as installment_status,
               DATEDIFF(CURDATE(), p.due_date) as days_overdue
        FROM payments p
        JOIN installment_plans ip ON p.installment_plan_id = ip.id
        JOIN users u ON ip.user_id = u.id
        JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
        JOIN services s ON pp.service_id = s.id
        ORDER BY p.due_date DESC, p.status ASC
    ");
    $stmt->execute();
    $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات سريعة
    $total_payments = count($payments);
    $paid_payments = count(array_filter($payments, fn($p) => $p['status'] === 'paid'));
    $pending_payments = count(array_filter($payments, fn($p) => $p['status'] === 'pending'));
    $overdue_payments = count(array_filter($payments, fn($p) => $p['status'] === 'overdue'));
    $total_amount = array_sum(array_column($payments, 'amount'));
    $paid_amount = array_sum(array_column(array_filter($payments, fn($p) => $p['status'] === 'paid'), 'amount'));
    
} catch (Exception $e) {
    $payments = [];
    $total_payments = $paid_payments = $pending_payments = $overdue_payments = 0;
    $total_amount = $paid_amount = 0;
}

$page_title = 'إدارة المدفوعات';
include __DIR__ . '/../includes/admin_header.php';
?>

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="page-title">إدارة المدفوعات</h1>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="exportPayments()">
                    <i class="fas fa-download me-2"></i>
                    تصدير البيانات
                </button>
                <button type="button" class="btn btn-outline-warning" onclick="sendOverdueReminders()">
                    <i class="fas fa-bell me-2"></i>
                    إرسال تذكيرات
                </button>
            </div>
        </div>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
        <?= $message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-icon bg-primary">
                <i class="fas fa-receipt"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?= $total_payments ?></h3>
                <p class="stat-label">إجمالي المدفوعات</p>
                <small class="stat-change text-info">
                    <?= formatCurrency($total_amount) ?>
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-icon bg-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?= $paid_payments ?></h3>
                <p class="stat-label">مدفوعات مكتملة</p>
                <small class="stat-change text-success">
                    <?= formatCurrency($paid_amount) ?>
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-icon bg-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?= $pending_payments ?></h3>
                <p class="stat-label">مدفوعات معلقة</p>
                <small class="stat-change text-warning">
                    <?= formatCurrency(array_sum(array_column(array_filter($payments, fn($p) => $p['status'] === 'pending'), 'amount'))) ?>
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-icon bg-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?= $overdue_payments ?></h3>
                <p class="stat-label">مدفوعات متأخرة</p>
                <small class="stat-change text-danger">
                    <?= formatCurrency(array_sum(array_column(array_filter($payments, fn($p) => $p['status'] === 'overdue'), 'amount'))) ?>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label for="status_filter" class="form-label">تصفية حسب الحالة</label>
                <select class="form-select" id="status_filter">
                    <option value="">جميع الحالات</option>
                    <option value="pending">معلق</option>
                    <option value="paid">مدفوع</option>
                    <option value="overdue">متأخر</option>
                    <option value="cancelled">ملغي</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" class="btn btn-primary me-2" onclick="applyFilters()">تطبيق</button>
                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">مسح</button>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-money-bill-wave me-2"></i>
            قائمة المدفوعات
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped data-table" id="paymentsTable">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>المستخدم</th>
                        <th>الخدمة</th>
                        <th>خطة السعر</th>
                        <th>رقم القسط</th>
                        <th>المبلغ</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>تاريخ الدفع</th>
                        <th>الأيام المتأخرة</th>
                        <th>الحالة</th>
                        <th>حالة الخطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($payments as $payment): ?>
                        <tr data-status="<?= $payment['status'] ?>" data-due-date="<?= $payment['due_date'] ?>">
                            <td><?= $payment['id'] ?></td>
                            <td>
                                <strong><?= htmlspecialchars($payment['user_name']) ?></strong>
                                <br><small class="text-muted"><?= htmlspecialchars($payment['user_phone']) ?></small>
                            </td>
                            <td>
                                <span class="badge bg-info"><?= htmlspecialchars($payment['service_name']) ?></span>
                            </td>
                            <td>
                                <?= htmlspecialchars($payment['plan_name']) ?>
                            </td>
                            <td>
                                <span class="badge bg-secondary"><?= $payment['installment_number'] ?></span>
                            </td>
                            <td>
                                <strong class="text-primary"><?= formatCurrency($payment['amount']) ?></strong>
                            </td>
                            <td>
                                <span class="<?= strtotime($payment['due_date']) < time() && $payment['status'] !== 'paid' ? 'text-danger' : '' ?>">
                                    <?= date('Y/m/d', strtotime($payment['due_date'])) ?>
                                </span>
                            </td>
                            <td>
                                <?= $payment['paid_at'] ? date('Y/m/d', strtotime($payment['paid_at'])) : '-' ?>
                            </td>
                            <td>
                                <?php if ($payment['status'] === 'overdue'): ?>
                                    <span class="badge bg-danger"><?= $payment['days_overdue'] ?> يوم</span>
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $status_colors = [
                                    'pending' => 'warning',
                                    'paid' => 'success',
                                    'overdue' => 'danger',
                                    'cancelled' => 'secondary'
                                ];
                                $status_labels = [
                                    'pending' => 'معلق',
                                    'paid' => 'مدفوع',
                                    'overdue' => 'متأخر',
                                    'cancelled' => 'ملغي'
                                ];
                                ?>
                                <span class="badge bg-<?= $status_colors[$payment['status']] ?? 'secondary' ?>">
                                    <?= $status_labels[$payment['status']] ?? $payment['status'] ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $installment_status_colors = [
                                    'active' => 'success',
                                    'completed' => 'primary',
                                    'cancelled' => 'danger',
                                    'suspended' => 'warning'
                                ];
                                $installment_status_labels = [
                                    'active' => 'نشط',
                                    'completed' => 'مكتمل',
                                    'cancelled' => 'ملغي',
                                    'suspended' => 'معلق'
                                ];
                                ?>
                                <span class="badge bg-<?= $installment_status_colors[$payment['installment_status']] ?? 'secondary' ?>">
                                    <?= $installment_status_labels[$payment['installment_status']] ?? $payment['installment_status'] ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="viewPaymentDetails(<?= $payment['id'] ?>)" 
                                            data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    
                                    <?php if ($payment['status'] !== 'paid'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="markAsPaid(<?= $payment['id'] ?>)" 
                                                data-bs-toggle="tooltip" title="تسجيل دفع">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    <?php endif; ?>
                                    
                                    <?php if ($payment['status'] === 'overdue'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="sendReminder(<?= $payment['id'] ?>)" 
                                                data-bs-toggle="tooltip" title="إرسال تذكير">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                    <?php endif; ?>
                                    
                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                            onclick="updatePaymentStatus(<?= $payment['id'] ?>)" 
                                            data-bs-toggle="tooltip" title="تغيير الحالة">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Update Payment Status Modal -->
<div class="modal fade" id="updatePaymentStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير حالة المدفوع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_payment_status">
                    <input type="hidden" name="id" id="payment_status_id">
                    
                    <div class="mb-3">
                        <label for="payment_status_select" class="form-label">الحالة الجديدة *</label>
                        <select class="form-select" id="payment_status_select" name="status" required>
                            <option value="pending">معلق</option>
                            <option value="paid">مدفوع</option>
                            <option value="overdue">متأخر</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكد من صحة الحالة الجديدة قبل الحفظ
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغيير</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Payment Details Modal -->
<div class="modal fade" id="viewPaymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المدفوع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="paymentDetailsContent">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Send Reminder Form -->
<form method="POST" id="sendReminderForm" style="display: none;">
    <input type="hidden" name="action" value="send_reminder">
    <input type="hidden" name="id" id="reminder_payment_id">
</form>

<style>
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
}

.stat-icon i {
    font-size: 1.5rem;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

.stat-change {
    font-size: 0.8rem;
}
</style>

<script>
// تطبيق التصفية
function applyFilters() {
    const statusFilter = document.getElementById('status_filter').value;
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;
    
    const table = document.getElementById('paymentsTable');
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        let show = true;
        
        // تصفية حسب الحالة
        if (statusFilter && row.dataset.status !== statusFilter) {
            show = false;
        }
        
        // تصفية حسب التاريخ
        if (dateFrom || dateTo) {
            const dueDate = row.dataset.dueDate;
            if (dateFrom && dueDate < dateFrom) show = false;
            if (dateTo && dueDate > dateTo) show = false;
        }
        
        row.style.display = show ? '' : 'none';
    });
}

// مسح التصفية
function clearFilters() {
    document.getElementById('status_filter').value = '';
    document.getElementById('date_from').value = '';
    document.getElementById('date_to').value = '';
    
    const rows = document.querySelectorAll('#paymentsTable tbody tr');
    rows.forEach(row => {
        row.style.display = '';
    });
}

// عرض تفاصيل المدفوع
function viewPaymentDetails(paymentId) {
    fetch(`../api/get_payment_details.php?id=${paymentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPaymentDetails(data.payment);
            } else {
                showError('فشل في تحميل بيانات المدفوع');
            }
        })
        .catch(error => {
            showError('حدث خطأ في الاتصال بالخادم');
        });
}

function displayPaymentDetails(payment) {
    const content = document.getElementById('paymentDetailsContent');
    
    const statusColors = {
        'pending': 'warning',
        'paid': 'success',
        'overdue': 'danger',
        'cancelled': 'secondary'
    };
    
    const statusLabels = {
        'pending': 'معلق',
        'paid': 'مدفوع',
        'overdue': 'متأخر',
        'cancelled': 'ملغي'
    };
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات المدفوع</h6>
                <table class="table table-borderless">
                    <tr><td><strong>رقم المدفوع:</strong></td><td>${payment.id}</td></tr>
                    <tr><td><strong>رقم القسط:</strong></td><td>${payment.installment_number}</td></tr>
                    <tr><td><strong>المبلغ:</strong></td><td>${formatCurrency(payment.amount)}</td></tr>
                    <tr><td><strong>تاريخ الاستحقاق:</strong></td><td>${formatDate(payment.due_date)}</td></tr>
                    <tr><td><strong>تاريخ الدفع:</strong></td><td>${payment.paid_at ? formatDate(payment.paid_at) : 'لم يتم الدفع'}</td></tr>
                    <tr><td><strong>الحالة:</strong></td><td><span class="badge bg-${statusColors[payment.status]}">${statusLabels[payment.status]}</span></td></tr>
                    <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${formatDate(payment.created_at)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>معلومات المستخدم والخطة</h6>
                <table class="table table-borderless">
                    <tr><td><strong>المستخدم:</strong></td><td>${payment.user_name}</td></tr>
                    <tr><td><strong>رقم الهاتف:</strong></td><td>${payment.user_phone}</td></tr>
                    <tr><td><strong>الخدمة:</strong></td><td>${payment.service_name}</td></tr>
                    <tr><td><strong>خطة السعر:</strong></td><td>${payment.plan_name}</td></tr>
                    <tr><td><strong>سعر الخطة:</strong></td><td>${formatCurrency(payment.plan_price)}</td></tr>
                    <tr><td><strong>حالة خطة التقسيط:</strong></td><td><span class="badge bg-info">${payment.installment_status}</span></td></tr>
                </table>
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('viewPaymentModal'));
    modal.show();
}

// تسجيل دفع
function markAsPaid(paymentId) {
    confirmDelete('هل أنت متأكد من تسجيل هذا القسط كمدفوع؟')
        .then((result) => {
            if (result.isConfirmed) {
                fetch('../api/mark_payment_paid.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({payment_id: paymentId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess('تم تسجيل الدفع بنجاح');
                        location.reload();
                    } else {
                        showError(data.message || 'حدث خطأ أثناء تسجيل الدفع');
                    }
                })
                .catch(error => {
                    showError('حدث خطأ في الاتصال بالخادم');
                });
            }
        });
}

// تغيير حالة المدفوع
function updatePaymentStatus(paymentId) {
    document.getElementById('payment_status_id').value = paymentId;
    const modal = new bootstrap.Modal(document.getElementById('updatePaymentStatusModal'));
    modal.show();
}

// إرسال تذكير
function sendReminder(paymentId) {
    confirmDelete('هل أنت متأكد من إرسال تذكير للمستخدم؟')
        .then((result) => {
            if (result.isConfirmed) {
                document.getElementById('reminder_payment_id').value = paymentId;
                document.getElementById('sendReminderForm').submit();
            }
        });
}

// إرسال تذكيرات للمدفوعات المتأخرة
function sendOverdueReminders() {
    confirmDelete('هل أنت متأكد من إرسال تذكيرات لجميع المدفوعات المتأخرة؟')
        .then((result) => {
            if (result.isConfirmed) {
                fetch('../api/send_overdue_reminders.php', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess(`تم إرسال ${data.count} تذكير بنجاح`);
                    } else {
                        showError(data.message || 'حدث خطأ أثناء إرسال التذكيرات');
                    }
                })
                .catch(error => {
                    showError('حدث خطأ في الاتصال بالخادم');
                });
            }
        });
}

// تصدير البيانات
function exportPayments() {
    const statusFilter = document.getElementById('status_filter').value;
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;
    
    let url = '../api/export_payments.php?';
    if (statusFilter) url += `status=${statusFilter}&`;
    if (dateFrom) url += `date_from=${dateFrom}&`;
    if (dateTo) url += `date_to=${dateTo}&`;
    
    window.open(url, '_blank');
}
</script>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>

