<?php
// ملف الإعداد الأولي للنظام
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// التحقق من إكمال الإعداد مسبقاً
if (file_exists(__DIR__ . '/config/setup_complete.flag')) {
    header('Location: index.php');
    exit();
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// معالجة خطوات الإعداد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 1) {
        // خطوة إعداد قاعدة البيانات
        $host = trim($_POST['host'] ?? '');
        $db_name = trim($_POST['db_name'] ?? '');
        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($host) || empty($db_name) || empty($username)) {
            $error = 'جميع الحقول مطلوبة باستثناء كلمة المرور';
        } else {
            try {
                // اختبار الاتصال
                $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // حفظ إعدادات قاعدة البيانات
                $config_content = "<?php\n";
                $config_content .= "define('DB_HOST', '" . addslashes($host) . "');\n";
                $config_content .= "define('DB_USERNAME', '" . addslashes($username) . "');\n";
                $config_content .= "define('DB_PASSWORD', '" . addslashes($password) . "');\n";
                $config_content .= "define('DB_NAME', '" . addslashes($db_name) . "');\n";
                $config_content .= "?>";
                
                if (!is_dir(__DIR__ . '/config')) {
                    mkdir(__DIR__ . '/config', 0755, true);
                }
                
                file_put_contents(__DIR__ . '/config/db_config.php', $config_content);
                
                // الانتقال للخطوة التالية
                header('Location: setup.php?step=2');
                exit();
                
            } catch (PDOException $e) {
                $error = 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
        }
    } elseif ($step === 2) {
        // خطوة إنشاء الجداول
        try {
            require_once __DIR__ . '/config/db_config.php';
            require_once __DIR__ . '/config/database.php';
            
            $db = new Database();
            $result = $db->createTables();
            
            if ($result) {
                header('Location: setup.php?step=3');
                exit();
            } else {
                $error = 'فشل في إنشاء الجداول';
            }
        } catch (Exception $e) {
            $error = 'خطأ في إنشاء الجداول: ' . $e->getMessage();
        }
    } elseif ($step === 3) {
        // خطوة إعداد WAWP
        $wawp_token = trim($_POST['wawp_token'] ?? '');
        $wawp_instance_id = trim($_POST['wawp_instance_id'] ?? '');

        if (empty($wawp_token) || empty($wawp_instance_id)) {
            $error = 'جميع حقول WAWP مطلوبة';
        } else {
            try {
                require_once __DIR__ . '/config/db_config.php';
                require_once __DIR__ . '/config/database.php';
                require_once __DIR__ . '/config/wawp.php';

                $db = new Database();
                $pdo = $db->getConnection();

                // حفظ إعدادات WAWP
                $stmt = $pdo->prepare("
                    INSERT INTO system_settings (setting_key, setting_value)
                    VALUES ('wawp_token', ?)
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$wawp_token]);

                $stmt = $pdo->prepare("
                    INSERT INTO system_settings (setting_key, setting_value)
                    VALUES ('wawp_instance_id', ?)
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$wawp_instance_id]);

                // اختبار الاتصال مع WAWP
                $wawp = new WAWP($wawp_token, $wawp_instance_id);
                $test_result = $wawp->testConnection();

                if ($test_result['success']) {
                    $_SESSION['wawp_configured'] = true;
                    header('Location: setup.php?step=4');
                    exit();
                } else {
                    $error = 'فشل في الاتصال مع WAWP: ' . ($test_result['message'] ?? 'خطأ غير معروف');
                }

            } catch (Exception $e) {
                $error = 'خطأ في إعداد WAWP: ' . $e->getMessage();
            }
        }
    } elseif ($step === 4) {
        // خطوة إعداد المدير الأول
        $admin_name = trim($_POST['admin_name'] ?? '');
        $admin_phone = trim($_POST['admin_phone'] ?? '');
        $admin_governorate = trim($_POST['admin_governorate'] ?? '');
        $admin_country = trim($_POST['admin_country'] ?? '');
        $admin_profession = trim($_POST['admin_profession'] ?? '');
        $admin_national_id = trim($_POST['admin_national_id'] ?? '');

        if (empty($admin_name) || empty($admin_phone) || empty($admin_governorate) || empty($admin_country) || empty($admin_profession)) {
            $error = 'جميع الحقول مطلوبة باستثناء رقم الهوية';
        } else {
            try {
                require_once __DIR__ . '/config/db_config.php';
                require_once __DIR__ . '/config/database.php';
                require_once __DIR__ . '/includes/functions.php';

                $db = new Database();
                $pdo = $db->getConnection();

                // تنظيف رقم الهاتف
                $clean_phone = cleanPhone($admin_phone);

                // إنشاء OTP
                $otp = generateOTP();
                $otp_expires = date('Y-m-d H:i:s', time() + (5 * 60)); // 5 دقائق

                // إضافة المدير الأول في جدول users
                $stmt = $pdo->prepare("
                    INSERT INTO users (name, phone, governorate, country, profession, national_id, is_admin, is_verified, otp_code, otp_expires_at)
                    VALUES (?, ?, ?, ?, ?, ?, TRUE, FALSE, ?, ?)
                ");
                $stmt->execute([$admin_name, $clean_phone, $admin_governorate, $admin_country, $admin_profession, $admin_national_id, $otp, $otp_expires]);

                // إضافة إلى جدول admin_users
                $stmt = $pdo->prepare("INSERT INTO admin_users (phone, is_verified) VALUES (?, FALSE)");
                $stmt->execute([$clean_phone]);

                // إضافة إعدادات النظام الافتراضية (إذا لم تكن موجودة)
                $default_settings = [
                    'system_name' => 'FutureWay',
                    'system_description' => 'نظام إدارة الخدمات والتقسيط',
                    'default_interest_rate' => '5',
                    'min_down_payment' => '20',
                    'max_installments' => '24',
                    'otp_expiry_minutes' => '5',
                    'max_login_attempts' => '5',
                    'otp_message_template' => 'رمز التحقق الخاص بك في FutureWay هو: {code}',
                    'welcome_message_template' => 'مرحباً بك في FutureWay! تم إنشاء حسابك بنجاح.',
                    'installment_reminder_template' => 'تذكير: لديك قسط مستحق بقيمة {amount} في تاريخ {date}'
                ];

                foreach ($default_settings as $key => $value) {
                    $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)");
                    $stmt->execute([$key, $value]);
                }

                // إرسال OTP عبر WAWP
                try {
                    require_once __DIR__ . '/config/wawp.php';
                    $wawp = new WAWP();
                    $message = "مرحباً $admin_name، تم إنشاء حساب مدير لك في FutureWay. رمز التحقق: $otp";
                    $wawp_result = $wawp->sendMessage($clean_phone, $message);

                    if ($wawp_result['success']) {
                        $success = "تم إنشاء حساب المدير بنجاح. تم إرسال رمز التحقق إلى $clean_phone";
                    } else {
                        $success = "تم إنشاء حساب المدير بنجاح. رمز التحقق: $otp (فشل في الإرسال عبر واتساب)";
                        error_log("WAWP Error in setup: " . ($wawp_result['message'] ?? 'Unknown error'));
                    }
                } catch (Exception $e) {
                    $success = "تم إنشاء حساب المدير بنجاح. رمز التحقق: $otp (فشل في الإرسال عبر واتساب)";
                    error_log("WAWP Exception in setup: " . $e->getMessage());
                }

                // حفظ بيانات المدير للخطوة التالية
                $_SESSION['setup_admin_phone'] = $clean_phone;
                $_SESSION['setup_admin_name'] = $admin_name;
                $_SESSION['setup_otp'] = $otp;

                header('Location: setup.php?step=5');
                exit();

            } catch (Exception $e) {
                $error = 'خطأ في إعداد المدير: ' . $e->getMessage();
            }
        }
    } elseif ($step === 5) {
        // خطوة التحقق من OTP
        $entered_otp = trim($_POST['otp'] ?? '');

        if (empty($entered_otp)) {
            $error = 'يرجى إدخال رمز التحقق';
        } else {
            if (isset($_SESSION['setup_otp']) && $_SESSION['setup_otp'] === $entered_otp) {
                try {
                    require_once __DIR__ . '/config/db_config.php';
                    require_once __DIR__ . '/config/database.php';

                    $db = new Database();
                    $pdo = $db->getConnection();

                    // تفعيل المدير
                    $stmt = $pdo->prepare("UPDATE users SET is_verified = TRUE, otp_code = NULL, otp_expires_at = NULL WHERE phone = ?");
                    $stmt->execute([$_SESSION['setup_admin_phone']]);

                    $stmt = $pdo->prepare("UPDATE admin_users SET is_verified = TRUE WHERE phone = ?");
                    $stmt->execute([$_SESSION['setup_admin_phone']]);

                    // مسح بيانات الجلسة
                    unset($_SESSION['setup_admin_phone'], $_SESSION['setup_admin_name'], $_SESSION['setup_otp']);

                    header('Location: setup.php?step=6');
                    exit();

                } catch (Exception $e) {
                    $error = 'خطأ في تفعيل المدير: ' . $e->getMessage();
                }
            } else {
                $error = 'رمز التحقق غير صحيح';
            }
        }
    } elseif ($step === 6) {
        // إنهاء الإعداد
        try {
            // إنشاء ملف إشارة إكمال الإعداد
            file_put_contents(__DIR__ . '/config/setup_complete.flag', date('Y-m-d H:i:s'));

            header('Location: index.php');
            exit();
        } catch (Exception $e) {
            $error = 'خطأ في إنهاء الإعداد: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - FutureWay</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .setup-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
            margin: 20px;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .setup-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .setup-header p {
            opacity: 0.9;
            margin: 0;
        }
        
        .setup-content {
            padding: 40px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: 600;
            position: relative;
        }
        
        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 2px;
            background: #e9ecef;
        }
        
        .step.completed:not(:last-child)::after {
            background: #28a745;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .alert {
            border: none;
            border-radius: 10px;
        }
        
        .step-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .step-description {
            color: #6c757d;
            text-align: center;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1>
                <i class="fas fa-rocket me-2"></i>
                إعداد FutureWay
            </h1>
            <p>مرحباً بك! دعنا نقوم بإعداد النظام خطوة بخطوة</p>
        </div>
        
        <div class="setup-content">
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?= $step >= 1 ? ($step > 1 ? 'completed' : 'active') : '' ?>">1</div>
                <div class="step <?= $step >= 2 ? ($step > 2 ? 'completed' : 'active') : '' ?>">2</div>
                <div class="step <?= $step >= 3 ? ($step > 3 ? 'completed' : 'active') : '' ?>">3</div>
                <div class="step <?= $step >= 4 ? ($step > 4 ? 'completed' : 'active') : '' ?>">4</div>
                <div class="step <?= $step >= 5 ? ($step > 5 ? 'completed' : 'active') : '' ?>">5</div>
                <div class="step <?= $step >= 6 ? 'active' : '' ?>">6</div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step === 1): ?>
                <!-- خطوة 1: إعداد قاعدة البيانات -->
                <h2 class="step-title">إعداد قاعدة البيانات</h2>
                <p class="step-description">أدخل بيانات الاتصال بقاعدة البيانات</p>
                
                <form method="POST">
                    <div class="mb-3">
                        <label for="host" class="form-label">عنوان الخادم</label>
                        <input type="text" class="form-control" id="host" name="host" 
                               value="<?= htmlspecialchars($_POST['host'] ?? 'localhost') ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="db_name" name="db_name" 
                               value="<?= htmlspecialchars($_POST['db_name'] ?? '') ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" required>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password">
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            التالي
                        </button>
                    </div>
                </form>
                
            <?php elseif ($step === 2): ?>
                <!-- خطوة 2: إنشاء الجداول -->
                <h2 class="step-title">إنشاء جداول قاعدة البيانات</h2>
                <p class="step-description">سيتم إنشاء جميع الجداول المطلوبة للنظام</p>
                
                <form method="POST">
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="fas fa-database fa-4x text-primary mb-3"></i>
                            <p>اضغط على "إنشاء الجداول" لبدء إنشاء جداول قاعدة البيانات</p>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء الجداول
                        </button>
                    </div>
                </form>
                
            <?php elseif ($step === 3): ?>
                <!-- خطوة 3: إعداد WAWP -->
                <h2 class="step-title">إعداد خدمة WAWP</h2>
                <p class="step-description">أدخل بيانات الاتصال مع خدمة WAWP لإرسال الرسائل</p>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> تحتاج إلى حساب WAWP للحصول على Access Token و Instance ID.
                    <br>يمكنك الحصول عليهما من <a href="https://wawp.net" target="_blank" class="alert-link">موقع WAWP</a>
                </div>

                <form method="POST">
                    <div class="mb-3">
                        <label for="wawp_token" class="form-label">
                            <i class="fas fa-key me-2"></i>
                            WAWP Access Token
                        </label>
                        <input type="text" class="form-control" id="wawp_token" name="wawp_token"
                               value="<?= htmlspecialchars($_POST['wawp_token'] ?? '') ?>"
                               placeholder="YHxkzMgexaTFAW" required>
                        <div class="form-text">الرمز المميز للوصول إلى خدمة WAWP</div>
                    </div>

                    <div class="mb-4">
                        <label for="wawp_instance_id" class="form-label">
                            <i class="fas fa-mobile-alt me-2"></i>
                            WAWP Instance ID
                        </label>
                        <input type="text" class="form-control" id="wawp_instance_id" name="wawp_instance_id"
                               value="<?= htmlspecialchars($_POST['wawp_instance_id'] ?? '') ?>"
                               placeholder="2586759A4472" required>
                        <div class="form-text">معرف الجهاز المرتبط بحساب واتساب</div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-wifi me-2"></i>
                            اختبار الاتصال والمتابعة
                        </button>
                    </div>
                </form>

            <?php elseif ($step === 4): ?>
                <!-- خطوة 4: إعداد المدير الأول -->
                <h2 class="step-title">إعداد المدير الأول</h2>
                <p class="step-description">أدخل بيانات المدير الأول للنظام</p>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="admin_name" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="admin_name" name="admin_name"
                                   value="<?= htmlspecialchars($_POST['admin_name'] ?? '') ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="admin_phone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="admin_phone" name="admin_phone"
                                   value="<?= htmlspecialchars($_POST['admin_phone'] ?? '') ?>"
                                   placeholder="+201012345678" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="admin_governorate" class="form-label">المحافظة</label>
                            <input type="text" class="form-control" id="admin_governorate" name="admin_governorate"
                                   value="<?= htmlspecialchars($_POST['admin_governorate'] ?? '') ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="admin_country" class="form-label">الدولة</label>
                            <input type="text" class="form-control" id="admin_country" name="admin_country"
                                   value="<?= htmlspecialchars($_POST['admin_country'] ?? 'السعودية') ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="admin_profession" class="form-label">المهنة</label>
                            <input type="text" class="form-control" id="admin_profession" name="admin_profession"
                                   value="<?= htmlspecialchars($_POST['admin_profession'] ?? '') ?>" required>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="admin_national_id" class="form-label">رقم الهوية (اختياري)</label>
                        <input type="text" class="form-control" id="admin_national_id" name="admin_national_id"
                               value="<?= htmlspecialchars($_POST['admin_national_id'] ?? '') ?>">
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-shield me-2"></i>
                            إنشاء المدير
                        </button>
                    </div>
                </form>
                
            <?php elseif ($step === 5): ?>
                <!-- خطوة 5: التحقق من OTP -->
                <h2 class="step-title">التحقق من رمز التأكيد</h2>
                <p class="step-description">
                    تم إرسال رمز التحقق إلى رقم الهاتف المدخل. يرجى إدخال الرمز للمتابعة
                    <?php if (isset($_SESSION['setup_admin_phone'])): ?>
                        <br><strong><?= htmlspecialchars($_SESSION['setup_admin_phone']) ?></strong>
                    <?php endif; ?>
                </p>

                <form method="POST">
                    <div class="mb-4">
                        <label for="otp" class="form-label">رمز التحقق</label>
                        <input type="text" class="form-control text-center" id="otp" name="otp"
                               maxlength="6" style="font-size: 1.5rem; letter-spacing: 0.5rem;" required>
                        <div class="form-text">أدخل الرمز المكون من 6 أرقام</div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-check me-2"></i>
                            تحقق من الرمز
                        </button>
                    </div>
                </form>

            <?php elseif ($step === 6): ?>
                <!-- خطوة 5: إنهاء الإعداد -->
                <h2 class="step-title">إنهاء الإعداد</h2>
                <p class="step-description">تم إعداد النظام بنجاح! يمكنك الآن البدء في استخدامه</p>

                <form method="POST">
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                            <p>تم إعداد النظام بنجاح. اضغط على "إنهاء" للانتقال إلى الصفحة الرئيسية</p>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-flag-checkered me-2"></i>
                            إنهاء الإعداد
                        </button>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
