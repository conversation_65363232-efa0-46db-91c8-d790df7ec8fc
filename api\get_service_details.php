<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$service_id = (int)($_GET['id'] ?? 0);

if (!$service_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الخدمة مطلوب']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // جلب بيانات الخدمة
    $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$service_id]);
    $service = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$service) {
        echo json_encode(['success' => false, 'message' => 'الخدمة غير موجودة']);
        exit();
    }
    
    // جلب خطط الأسعار
    $stmt = $pdo->prepare("
        SELECT pp.*, COUNT(ip.id) as purchases_count
        FROM pricing_plans pp
        LEFT JOIN installment_plans ip ON pp.id = ip.pricing_plan_id
        WHERE pp.service_id = ?
        GROUP BY pp.id
        ORDER BY pp.price ASC
    ");
    $stmt->execute([$service_id]);
    $pricing_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'service' => $service,
        'pricing_plans' => $pricing_plans
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>

