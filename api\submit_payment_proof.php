<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $user = getCurrentUser();
    $user_id = $user['id'];
    
    // استلام البيانات
    $gateway_id = (int)($_POST['gateway_id'] ?? 0);
    $amount = (float)($_POST['amount'] ?? 0);
    $payment_type = $_POST['payment_type'] ?? '';
    $installment_plan_id = !empty($_POST['installment_plan_id']) ? (int)$_POST['installment_plan_id'] : null;
    $payment_id = !empty($_POST['payment_id']) ? (int)$_POST['payment_id'] : null;
    
    // التحقق من صحة البيانات
    if (!$gateway_id || !$amount || !$payment_type) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit();
    }
    
    if (!in_array($payment_type, ['installment', 'down_payment', 'service_purchase'])) {
        echo json_encode(['success' => false, 'message' => 'نوع دفع غير صحيح']);
        exit();
    }
    
    // التحقق من وجود بوابة الدفع
    $stmt = $pdo->prepare("SELECT * FROM payment_gateways WHERE id = ? AND is_active = TRUE");
    $stmt->execute([$gateway_id]);
    $gateway = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$gateway) {
        echo json_encode(['success' => false, 'message' => 'بوابة دفع غير صحيحة']);
        exit();
    }
    
    // معالجة رفع الصور
    $uploaded_files = [];
    $upload_dir = '../uploads/payment_proofs/';
    
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // التحقق من وجود ملفات مرفوعة
    if (isset($_FILES['payment_proofs']) && is_array($_FILES['payment_proofs']['name'])) {
        $file_count = count($_FILES['payment_proofs']['name']);
        
        // التحقق من عدد الملفات (حد أقصى 3)
        if ($file_count > 3) {
            echo json_encode(['success' => false, 'message' => 'يمكن رفع 3 صور كحد أقصى']);
            exit();
        }
        
        $total_size = 0;
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff'];
        $max_file_size = 10 * 1024 * 1024; // 10 MB
        $max_total_size = 30 * 1024 * 1024; // 30 MB
        
        // التحقق من أحجام الملفات أولاً
        for ($i = 0; $i < $file_count; $i++) {
            if ($_FILES['payment_proofs']['error'][$i] === UPLOAD_ERR_OK) {
                $file_size = $_FILES['payment_proofs']['size'][$i];
                $total_size += $file_size;
                
                if ($file_size > $max_file_size) {
                    echo json_encode(['success' => false, 'message' => 'حجم الصورة يجب أن يكون أقل من 10 ميجابايت']);
                    exit();
                }
            }
        }
        
        if ($total_size > $max_total_size) {
            echo json_encode(['success' => false, 'message' => 'الحجم الإجمالي للصور يجب أن يكون أقل من 30 ميجابايت']);
            exit();
        }
        
        // رفع الملفات
        for ($i = 0; $i < $file_count; $i++) {
            if ($_FILES['payment_proofs']['error'][$i] === UPLOAD_ERR_OK) {
                $file_extension = strtolower(pathinfo($_FILES['payment_proofs']['name'][$i], PATHINFO_EXTENSION));
                
                if (!in_array($file_extension, $allowed_extensions)) {
                    echo json_encode(['success' => false, 'message' => 'نوع ملف غير مدعوم. يُسمح بالصور فقط']);
                    exit();
                }
                
                $filename = uniqid() . '_' . $i . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;
                
                if (move_uploaded_file($_FILES['payment_proofs']['tmp_name'][$i], $upload_path)) {
                    $uploaded_files[] = 'uploads/payment_proofs/' . $filename;
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في رفع إحدى الصور']);
                    exit();
                }
            }
        }
    }
    
    if (empty($uploaded_files)) {
        echo json_encode(['success' => false, 'message' => 'يجب رفع إثبات دفع واحد على الأقل']);
        exit();
    }
    
    // حفظ طلب الدفع في قاعدة البيانات
    $stmt = $pdo->prepare("
        INSERT INTO payment_requests 
        (user_id, installment_plan_id, payment_id, gateway_id, amount, payment_type, payment_proofs, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
    ");
    
    $payment_proofs_json = json_encode($uploaded_files);
    
    if ($stmt->execute([$user_id, $installment_plan_id, $payment_id, $gateway_id, $amount, $payment_type, $payment_proofs_json])) {
        echo json_encode([
            'success' => true,
            'message' => 'تم إرسال طلب الدفع بنجاح! سيتم مراجعة إثبات الدفع من قبل الإدارة خلال 24 ساعة وسيتم إشعارك بالنتيجة.'
        ]);
    } else {
        // حذف الملفات المرفوعة في حالة فشل حفظ البيانات
        foreach ($uploaded_files as $file) {
            if (file_exists('../' . $file)) {
                unlink('../' . $file);
            }
        }
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حفظ طلب الدفع']);
    }
    
} catch (Exception $e) {
    error_log("Error in submit_payment_proof.php: " . $e->getMessage());
    
    // حذف الملفات المرفوعة في حالة حدوث خطأ
    if (isset($uploaded_files)) {
        foreach ($uploaded_files as $file) {
            if (file_exists('../' . $file)) {
                unlink('../' . $file);
            }
        }
    }
    
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
