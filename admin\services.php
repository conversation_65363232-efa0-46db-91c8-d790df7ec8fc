<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requireAdmin();

$message = '';
$message_type = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create') {
        $name = sanitizeInput($_POST['name'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        
        if (empty($name) || empty($description)) {
            $message = 'اسم الخدمة والوصف مطلوبان';
            $message_type = 'danger';
        } else {
            $image_filename = null;
            
            // معالجة رفع الصورة
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $upload_result = uploadFile($_FILES['image'], ['jpg', 'jpeg', 'png', 'gif']);
                if ($upload_result['success']) {
                    $image_filename = $upload_result['filename'];
                } else {
                    $message = $upload_result['message'];
                    $message_type = 'danger';
                }
            }
            
            if (empty($message)) {
                $service_id = createServiceAdmin($name, $description, $image_filename);
                if ($service_id) {
                    $message = 'تم إنشاء الخدمة بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء إنشاء الخدمة';
                    $message_type = 'danger';
                }
            }
        }
    } elseif ($action === 'update') {
        $id = (int)($_POST['id'] ?? 0);
        $name = sanitizeInput($_POST['name'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        
        if (empty($name) || empty($description)) {
            $message = 'اسم الخدمة والوصف مطلوبان';
            $message_type = 'danger';
        } else {
            $image_filename = null;
            
            // معالجة رفع الصورة الجديدة
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $upload_result = uploadFile($_FILES['image'], ['jpg', 'jpeg', 'png', 'gif']);
                if ($upload_result['success']) {
                    $image_filename = $upload_result['filename'];
                } else {
                    $message = $upload_result['message'];
                    $message_type = 'danger';
                }
            }
            
            if (empty($message)) {
                if (updateServiceAdmin($id, $name, $description, $image_filename)) {
                    $message = 'تم تحديث الخدمة بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تحديث الخدمة';
                    $message_type = 'danger';
                }
            }
        }
    } elseif ($action === 'delete') {
        $id = (int)($_POST['id'] ?? 0);
        $result = deleteServiceAdmin($id);
        $message = $result['message'];
        $message_type = $result['success'] ? 'success' : 'danger';
    }
}

// جلب جميع الخدمات
try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $stmt = $pdo->prepare("
        SELECT s.*,
               COUNT(DISTINCT pp.id) as pricing_plans_count,
               COUNT(DISTINCT ip.id) as installment_plans_count,
               COALESCE(SUM(ip.total_amount), 0) as total_revenue
        FROM services s
        LEFT JOIN pricing_plans pp ON s.id = pp.service_id
        LEFT JOIN installment_plans ip ON pp.id = ip.pricing_plan_id AND ip.status = 'completed'
        GROUP BY s.id
        ORDER BY s.created_at DESC
    ");
    $stmt->execute();
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $services = [];
}

$page_title = 'إدارة الخدمات';
include __DIR__ . '/../includes/admin_header.php';
?>

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="page-title">إدارة الخدمات</h1>
        </div>
        <div class="col-auto">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addServiceModal">
                <i class="fas fa-plus me-2"></i>
                إضافة خدمة جديدة
            </button>
        </div>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
        <?= $message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-cogs me-2"></i>
            قائمة الخدمات
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped data-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>الصورة</th>
                        <th>اسم الخدمة</th>
                        <th>الوصف</th>
                        <th>خطط الأسعار</th>
                        <th>مرات الشراء</th>
                        <th>إجمالي الإيرادات</th>
                        <th>تاريخ الإضافة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($services as $service): ?>
                        <tr>
                            <td><?= $service['id'] ?></td>
                            <td>
                                <?php if ($service['image']): ?>
                                    <img src="../assets/uploads/<?= htmlspecialchars($service['image']) ?>" 
                                         alt="<?= htmlspecialchars($service['name']) ?>" 
                                         class="service-thumbnail">
                                <?php else: ?>
                                    <div class="service-placeholder-small">
                                        <i class="fas fa-image"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?= htmlspecialchars($service['name']) ?></strong>
                            </td>
                            <td>
                                <span class="text-truncate" style="max-width: 200px; display: inline-block;" 
                                      data-bs-toggle="tooltip" title="<?= htmlspecialchars($service['description']) ?>">
                                    <?= htmlspecialchars(substr($service['description'], 0, 50)) ?>...
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info"><?= $service['pricing_plans_count'] ?> خطة</span>
                            </td>
                            <td>
                                <span class="badge bg-success"><?= $service['installment_plans_count'] ?> مرة</span>
                            </td>
                            <td>
                                <strong class="text-success"><?= formatCurrency($service['total_revenue']) ?></strong>
                            </td>
                            <td>
                                <small><?= date('Y/m/d', strtotime($service['created_at'])) ?></small>
                            </td>
                            <td>
                                <span class="badge bg-success">نشط</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="viewService(<?= $service['id'] ?>)" 
                                            data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                            onclick="editService(<?= $service['id'] ?>)" 
                                            data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <a href="pricing_plans.php?service_id=<?= $service['id'] ?>" 
                                       class="btn btn-sm btn-outline-info" 
                                       data-bs-toggle="tooltip" title="إدارة خطط الأسعار">
                                        <i class="fas fa-tags"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteServiceConfirm(<?= $service['id'] ?>)" 
                                            data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Service Modal -->
<div class="modal fade" id="addServiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة خدمة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الخدمة *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الخدمة *</label>
                        <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">صورة الخدمة (اختياري)</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">الحد الأقصى لحجم الملف: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="image-preview" id="imagePreview" style="display: none;">
                            <img id="previewImg" src="" alt="معاينة الصورة" style="max-width: 200px; max-height: 200px;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الخدمة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Service Modal -->
<div class="modal fade" id="editServiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الخدمة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="editServiceForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_service_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">اسم الخدمة *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">وصف الخدمة *</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_image" class="form-label">صورة الخدمة (اختياري)</label>
                        <input type="file" class="form-control" id="edit_image" name="image" accept="image/*">
                        <div class="form-text">اترك فارغاً للاحتفاظ بالصورة الحالية</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="current-image" id="currentImage">
                            <!-- سيتم عرض الصورة الحالية هنا -->
                        </div>
                        <div class="image-preview" id="editImagePreview" style="display: none;">
                            <img id="editPreviewImg" src="" alt="معاينة الصورة" style="max-width: 200px; max-height: 200px;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Service Modal -->
<div class="modal fade" id="viewServiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الخدمة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="serviceDetailsContent">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Delete Service Form -->
<form method="POST" id="deleteServiceForm" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="id" id="delete_service_id">
</form>

<style>
.service-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
}

.service-placeholder-small {
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.image-preview {
    margin-top: 10px;
    text-align: center;
}

.current-image img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
}
</style>

<script>
// معاينة الصورة عند الرفع
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImg').src = e.target.result;
            document.getElementById('imagePreview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        document.getElementById('imagePreview').style.display = 'none';
    }
});

document.getElementById('edit_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('editPreviewImg').src = e.target.result;
            document.getElementById('editImagePreview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        document.getElementById('editImagePreview').style.display = 'none';
    }
});

// عرض تفاصيل الخدمة
function viewService(serviceId) {
    fetch(`../api/get_service_details.php?id=${serviceId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayServiceDetails(data.service, data.pricing_plans);
            } else {
                showError('فشل في تحميل بيانات الخدمة');
            }
        })
        .catch(error => {
            showError('حدث خطأ في الاتصال بالخادم');
        });
}

function displayServiceDetails(service, pricingPlans) {
    const content = document.getElementById('serviceDetailsContent');
    
    let pricingPlansHtml = '';
    if (pricingPlans.length > 0) {
        pricingPlans.forEach(plan => {
            pricingPlansHtml += `
                <div class="card mb-2">
                    <div class="card-body">
                        <h6>${plan.name}</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small>السعر: ${formatCurrency(plan.price)}</small>
                            </div>
                            <div class="col-md-6">
                                <small>التقسيط: ${plan.installment_enabled ? 'متاح' : 'غير متاح'}</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        pricingPlansHtml = '<p class="text-muted">لا توجد خطط أسعار</p>';
    }
    
    const imageHtml = service.image ? 
        `<img src="../assets/uploads/${service.image}" alt="${service.name}" class="img-fluid rounded mb-3" style="max-height: 300px;">` : 
        '<div class="alert alert-info">لا توجد صورة للخدمة</div>';
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات الخدمة</h6>
                ${imageHtml}
                <table class="table table-borderless">
                    <tr><td><strong>اسم الخدمة:</strong></td><td>${service.name}</td></tr>
                    <tr><td><strong>الوصف:</strong></td><td>${service.description}</td></tr>
                    <tr><td><strong>تاريخ الإضافة:</strong></td><td>${formatDate(service.created_at)}</td></tr>
                    <tr><td><strong>الحالة:</strong></td><td><span class="badge bg-${service.is_active ? 'success' : 'secondary'}">${service.is_active ? 'نشط' : 'غير نشط'}</span></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>خطط الأسعار</h6>
                ${pricingPlansHtml}
                <a href="pricing_plans.php?service_id=${service.id}" class="btn btn-primary btn-sm">إدارة خطط الأسعار</a>
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('viewServiceModal'));
    modal.show();
}

// تعديل الخدمة
function editService(serviceId) {
    fetch(`../api/get_service_details.php?id=${serviceId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const service = data.service;
                document.getElementById('edit_service_id').value = service.id;
                document.getElementById('edit_name').value = service.name;
                document.getElementById('edit_description').value = service.description;
                
                // عرض الصورة الحالية
                const currentImageDiv = document.getElementById('currentImage');
                if (service.image) {
                    currentImageDiv.innerHTML = `
                        <label class="form-label">الصورة الحالية:</label><br>
                        <img src="../assets/uploads/${service.image}" alt="${service.name}" style="max-width: 200px; max-height: 200px; border-radius: 8px;">
                    `;
                } else {
                    currentImageDiv.innerHTML = '<p class="text-muted">لا توجد صورة حالية</p>';
                }
                
                // إخفاء معاينة الصورة الجديدة
                document.getElementById('editImagePreview').style.display = 'none';
                
                const modal = new bootstrap.Modal(document.getElementById('editServiceModal'));
                modal.show();
            } else {
                showError('فشل في تحميل بيانات الخدمة');
            }
        })
        .catch(error => {
            showError('حدث خطأ في الاتصال بالخادم');
        });
}

// حذف الخدمة
function deleteServiceConfirm(serviceId) {
    confirmDelete('هل أنت متأكد من حذف هذه الخدمة؟ سيتم حذف جميع خطط الأسعار المرتبطة بها.')
        .then((result) => {
            if (result.isConfirmed) {
                document.getElementById('delete_service_id').value = serviceId;
                document.getElementById('deleteServiceForm').submit();
            }
        });
}
</script>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>

