<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/admin_auth.php';

$page_title = 'إدارة الأقساط';
$message = '';
$message_type = '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_installment') {
        $user_id = (int)($_POST['user_id'] ?? 0);
        $pricing_plan_id = (int)($_POST['pricing_plan_id'] ?? 0);
        $custom_down_payment = (float)($_POST['custom_down_payment'] ?? 0);
        $purchase_date = $_POST['purchase_date'] ?? date('Y-m-d');

        if ($user_id > 0 && $pricing_plan_id > 0) {
            try {
                $db = new Database();
                $pdo = $db->getConnection();

                // التحقق من صحة تاريخ الشراء
                $purchase_datetime = DateTime::createFromFormat('Y-m-d', $purchase_date);
                if (!$purchase_datetime) {
                    $message = 'تاريخ الشراء غير صحيح';
                    $message_type = 'danger';
                } else {
                    // جلب بيانات خطة السعر
                    $stmt = $pdo->prepare("
                        SELECT pp.*, s.name as service_name
                        FROM pricing_plans pp
                        JOIN services s ON pp.service_id = s.id
                        WHERE pp.id = ?
                    ");
                    $stmt->execute([$pricing_plan_id]);
                    $plan = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($plan) {
                        // حساب قيم التقسيط مع الفائدة
                        $base_price = $plan['price'];

                        // حساب الفائدة
                        $total_interest = 0;
                        if ($plan['interest_type'] === 'fixed') {
                            $total_interest = $plan['interest_value'];
                        } else {
                            $total_interest = ($base_price * $plan['interest_value']) / 100;
                        }

                        // إجمالي المبلغ مع الفائدة
                        $total_amount = $base_price + $total_interest;

                        $down_payment = $custom_down_payment > 0 ? $custom_down_payment : $plan['down_payment'];
                        $remaining_amount = $total_amount - $down_payment;
                        $installment_amount = $plan['installment_amount'];
                        $installments_count = $plan['installments_count'];

                        // إنشاء خطة التقسيط مع تاريخ الشراء
                        $stmt = $pdo->prepare("
                            INSERT INTO installment_plans (
                                user_id, pricing_plan_id, total_amount, down_payment,
                                remaining_amount, installment_amount, installments_count,
                                purchase_date, status, created_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?)
                        ");

                        if ($stmt->execute([
                            $user_id, $pricing_plan_id, $total_amount, $down_payment,
                            $remaining_amount, $installment_amount, $installments_count,
                            $purchase_date, $purchase_date . ' 00:00:00'
                        ])) {
                            $installment_plan_id = $pdo->lastInsertId();

                            // إنشاء جدولة الأقساط بناءً على تاريخ الشراء
                            // أول قسط في نفس اليوم من الشهر التالي
                            $first_installment_date = clone $purchase_datetime;
                            $first_installment_date->add(new DateInterval('P1M')); // إضافة شهر واحد

                            for ($i = 1; $i <= $installments_count; $i++) {
                                // حساب تاريخ استحقاق كل قسط (كل شهر في نفس اليوم)
                                $due_date = clone $purchase_datetime;
                                $due_date->add(new DateInterval('P' . $i . 'M')); // إضافة عدد الأشهر

                                $stmt = $pdo->prepare("
                                    INSERT INTO payments (
                                        installment_plan_id, installment_number, amount, due_date, status
                                    ) VALUES (?, ?, ?, ?, 'pending')
                                ");
                                $stmt->execute([
                                    $installment_plan_id, $i, $installment_amount, $due_date->format('Y-m-d')
                                ]);
                            }

                            $message = 'تم إنشاء خطة التقسيط بنجاح مع تاريخ الشراء: ' . $purchase_date;
                            $message_type = 'success';
                        } else {
                            $message = 'فشل في إنشاء خطة التقسيط';
                            $message_type = 'danger';
                        }
                    } else {
                        $message = 'خطة السعر غير موجودة';
                        $message_type = 'danger';
                    }
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ: ' . $e->getMessage();
                $message_type = 'danger';
            }
        } else {
            $message = 'يرجى اختيار المستخدم وخطة السعر';
            $message_type = 'danger';
        }
    }
    
    elseif ($action === 'update_payment_status') {
        $payment_id = (int)($_POST['payment_id'] ?? 0);
        $status = $_POST['status'] ?? '';
        
        if ($payment_id > 0 && in_array($status, ['pending', 'paid', 'overdue'])) {
            try {
                $db = new Database();
                $pdo = $db->getConnection();
                
                $paid_date = null;
                $paid_at = null;
                
                if ($status === 'paid') {
                    $paid_date = date('Y-m-d');
                    $paid_at = date('Y-m-d H:i:s');
                }
                
                $stmt = $pdo->prepare("
                    UPDATE payments 
                    SET status = ?, paid_date = ?, paid_at = ? 
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$status, $paid_date, $paid_at, $payment_id])) {
                    // تحديث عدد الأقساط المدفوعة في خطة التقسيط
                    if ($status === 'paid') {
                        $stmt = $pdo->prepare("
                            UPDATE installment_plans 
                            SET paid_installments = (
                                SELECT COUNT(*) FROM payments 
                                WHERE installment_plan_id = installment_plans.id AND status = 'paid'
                            )
                            WHERE id = (
                                SELECT installment_plan_id FROM payments WHERE id = ?
                            )
                        ");
                        $stmt->execute([$payment_id]);
                    }
                    
                    $message = 'تم تحديث حالة الدفعة بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'فشل في تحديث حالة الدفعة';
                    $message_type = 'danger';
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ: ' . $e->getMessage();
                $message_type = 'danger';
            }
        }
    }
    
    elseif ($action === 'send_reminder') {
        $payment_id = (int)($_POST['payment_id'] ?? 0);
        
        if ($payment_id > 0) {
            try {
                $db = new Database();
                $pdo = $db->getConnection();
                
                // جلب بيانات الدفعة والمستخدم
                $stmt = $pdo->prepare("
                    SELECT p.*, ip.*, u.name as user_name, u.phone, pp.name as plan_name, s.name as service_name
                    FROM payments p
                    JOIN installment_plans ip ON p.installment_plan_id = ip.id
                    JOIN users u ON ip.user_id = u.id
                    JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
                    JOIN services s ON pp.service_id = s.id
                    WHERE p.id = ?
                ");
                $stmt->execute([$payment_id]);
                $payment_data = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($payment_data) {
                    require_once __DIR__ . '/../config/wawp.php';
                    $wawp = new WAWP();
                    
                    $message_text = "تذكير دفع - FutureWay\n\n";
                    $message_text .= "عزيزي/عزيزتي {$payment_data['user_name']}\n\n";
                    $message_text .= "لديك قسط مستحق للدفع:\n";
                    $message_text .= "الخدمة: {$payment_data['service_name']}\n";
                    $message_text .= "الخطة: {$payment_data['plan_name']}\n";
                    $message_text .= "رقم القسط: {$payment_data['installment_number']}\n";
                    $message_text .= "المبلغ: {$payment_data['amount']} ريال\n";
                    $message_text .= "تاريخ الاستحقاق: {$payment_data['due_date']}\n\n";
                    $message_text .= "يرجى سداد المبلغ في أقرب وقت ممكن.\n";
                    $message_text .= "شكراً لك - فريق FutureWay";
                    
                    $result = $wawp->sendMessage($payment_data['phone'], $message_text);
                    
                    if ($result['success']) {
                        $message = 'تم إرسال تذكير الدفع بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'فشل في إرسال تذكير الدفع: ' . $result['message'];
                        $message_type = 'warning';
                    }
                } else {
                    $message = 'بيانات الدفعة غير موجودة';
                    $message_type = 'danger';
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ في إرسال التذكير: ' . $e->getMessage();
                $message_type = 'danger';
            }
        }
    }
}

// جلب البيانات للعرض
try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // جلب جميع خطط التقسيط
    $stmt = $pdo->prepare("
        SELECT ip.*, u.name as user_name, u.phone, pp.name as plan_name, s.name as service_name,
               (SELECT COUNT(*) FROM payments WHERE installment_plan_id = ip.id AND status = 'paid') as paid_count,
               (SELECT COUNT(*) FROM payments WHERE installment_plan_id = ip.id) as total_count,
               (SELECT MIN(due_date) FROM payments WHERE installment_plan_id = ip.id AND status = 'pending') as next_payment_date
        FROM installment_plans ip
        JOIN users u ON ip.user_id = u.id
        JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
        JOIN services s ON pp.service_id = s.id
        ORDER BY ip.created_at DESC
    ");
    $stmt->execute();
    $installment_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب المستخدمين للقائمة المنسدلة
    $stmt = $pdo->prepare("SELECT id, name, phone FROM users WHERE is_verified = TRUE ORDER BY name");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب الخدمات
    $stmt = $pdo->prepare("SELECT id, name FROM services WHERE is_active = TRUE ORDER BY name");
    $stmt->execute();
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $installment_plans = [];
    $users = [];
    $services = [];
    error_log("خطأ في جلب بيانات الأقساط: " . $e->getMessage());
}

include __DIR__ . '/../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">إدارة الأقساط</h1>
                <div>
                    <a href="../test_pricing_plans_api.php" class="btn btn-outline-info me-2" target="_blank">
                        <i class="fas fa-bug me-2"></i>
                        اختبار API
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء قسط جديد
                    </button>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
                    <i class="fas fa-<?= $message_type === 'success' ? 'check-circle' : ($message_type === 'danger' ? 'exclamation-circle' : 'info-circle') ?> me-2"></i>
                    <?= htmlspecialchars($message) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- جدول خطط التقسيط -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        خطط التقسيط الحالية
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($installment_plans)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد خطط تقسيط</h5>
                            <p class="text-muted">ابدأ بإنشاء خطة تقسيط جديدة</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>الخدمة</th>
                                        <th>الخطة</th>
                                        <th>تاريخ الشراء</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>المقدم</th>
                                        <th>التقدم</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($installment_plans as $plan): ?>
                                        <?php
                                        // حساب المبلغ المدفوع مع المقدم
                                        $paid_amount = $plan['down_payment'] + ($plan['paid_count'] * $plan['installment_amount']);
                                        $remaining_amount = $plan['total_amount'] - $paid_amount;
                                        $progress_percentage = $plan['total_amount'] > 0 ? ($paid_amount / $plan['total_amount']) * 100 : 0;
                                        ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($plan['user_name']) ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?= htmlspecialchars($plan['phone']) ?></small>
                                                </div>
                                            </td>
                                            <td><?= htmlspecialchars($plan['service_name']) ?></td>
                                            <td><?= htmlspecialchars($plan['plan_name']) ?></td>
                                            <td>
                                                <i class="fas fa-calendar me-1"></i>
                                                <?= date('d/m/Y', strtotime($plan['purchase_date'] ?? $plan['created_at'])) ?>
                                                <?php if (!empty($plan['next_payment_date'])): ?>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        القسط التالي: <?= date('d/m/Y', strtotime($plan['next_payment_date'])) ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?= number_format($plan['total_amount'], 2) ?> جنيه</strong>
                                            </td>
                                            <td><?= number_format($plan['down_payment'], 2) ?> جنيه</td>
                                            <td>
                                                <div class="progress mb-1" style="height: 8px;">
                                                    <div class="progress-bar bg-success" style="width: <?= $progress_percentage ?>%"></div>
                                                </div>
                                                <small class="text-muted">
                                                    مدفوع: <?= number_format($paid_amount, 2) ?>/<?= number_format($plan['total_amount'], 2) ?> جنيه
                                                    (<?= number_format($progress_percentage, 1) ?>%)
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($plan['status'] === 'active'): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php elseif ($plan['status'] === 'completed'): ?>
                                                    <span class="badge bg-primary">مكتمل</span>
                                                <?php elseif ($plan['status'] === 'cancelled'): ?>
                                                    <span class="badge bg-danger">ملغي</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">معلق</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="viewInstallmentDetails(<?= $plan['id'] ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" 
                                                            onclick="sendInstallmentReminder(<?= $plan['id'] ?>)">
                                                        <i class="fab fa-whatsapp"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج إنشاء قسط جديد -->
<div class="modal fade" id="createInstallmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء قسط جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_installment">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="user_id" class="form-label">المستخدم</label>
                            <select class="form-select" id="user_id" name="user_id" required>
                                <option value="">اختر المستخدم</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?= $user['id'] ?>">
                                        <?= htmlspecialchars($user['name']) ?> - <?= htmlspecialchars($user['phone']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="service_id" class="form-label">الخدمة</label>
                            <select class="form-select" id="service_id" name="service_id" onchange="loadPricingPlans()" required>
                                <option value="">اختر الخدمة</option>
                                <?php foreach ($services as $service): ?>
                                    <option value="<?= $service['id'] ?>"><?= htmlspecialchars($service['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="pricing_plan_id" class="form-label">خطة السعر</label>
                        <select class="form-select" id="pricing_plan_id" name="pricing_plan_id" onchange="loadPlanDetails()" required>
                            <option value="">اختر خطة السعر</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="purchase_date" class="form-label">تاريخ الشراء</label>
                        <input type="date" class="form-control" id="purchase_date" name="purchase_date"
                               value="<?= date('Y-m-d') ?>" max="<?= date('Y-m-d') ?>" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            سيتم احتساب أول قسط في نفس اليوم من الشهر التالي
                            <br>
                            <small class="text-muted">مثال: إذا كان تاريخ الشراء 5/1/2024، فأول قسط سيكون 5/2/2024</small>
                        </div>
                    </div>

                    <!-- تفاصيل الخطة -->
                    <div id="plan_details" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">تفاصيل خطة السعر</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>السعر الإجمالي:</strong>
                                        <div id="total_price" class="text-primary fs-5"></div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>المقدم:</strong>
                                        <div id="down_payment" class="text-success fs-5"></div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>عدد الأقساط:</strong>
                                        <div id="installments_count" class="text-info fs-5"></div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>قيمة القسط:</strong>
                                        <div id="installment_amount" class="text-warning fs-5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <label for="custom_down_payment" class="form-label">مقدم مخصص (اختياري)</label>
                            <input type="number" class="form-control" id="custom_down_payment" name="custom_down_payment"
                                   step="0.01" min="0" placeholder="اتركه فارغاً لاستخدام المقدم الافتراضي">
                            <div class="form-text">يمكنك تخصيص قيمة المقدم حسب الحاجة</div>
                        </div>

                        <!-- عرض جدولة الأقساط المتوقعة -->
                        <div id="installment_schedule" style="display: none;" class="mt-3">
                            <h6 class="text-primary">
                                <i class="fas fa-calendar-alt me-2"></i>
                                جدولة الأقساط المتوقعة
                            </h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم القسط</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>المبلغ</th>
                                        </tr>
                                    </thead>
                                    <tbody id="schedule_body">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء القسط</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تفاصيل القسط -->
<div class="modal fade" id="installmentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل خطة التقسيط</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="installment_details_content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل خطط الأسعار عند اختيار الخدمة
function loadPricingPlans() {
    const serviceId = document.getElementById('service_id').value;
    const pricingPlanSelect = document.getElementById('pricing_plan_id');
    const planDetails = document.getElementById('plan_details');

    // إعادة تعيين القائمة
    pricingPlanSelect.innerHTML = '<option value="">جاري التحميل...</option>';
    planDetails.style.display = 'none';

    if (serviceId) {
        console.log('تحميل خطط الأسعار للخدمة:', serviceId);

        fetch(`../api/get_pricing_plans_by_service.php?service_id=${serviceId}`)
            .then(response => {
                console.log('استجابة الخادم:', response);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('البيانات المستلمة:', data);

                pricingPlanSelect.innerHTML = '<option value="">اختر خطة السعر</option>';

                if (data.success && data.plans && data.plans.length > 0) {
                    data.plans.forEach(plan => {
                        const option = document.createElement('option');
                        option.value = plan.id;
                        option.textContent = `${plan.name} - ${parseFloat(plan.price).toFixed(2)} جنيه`;
                        option.dataset.planData = JSON.stringify(plan);
                        pricingPlanSelect.appendChild(option);
                    });
                    console.log(`تم تحميل ${data.plans.length} خطة سعر`);
                } else {
                    pricingPlanSelect.innerHTML = '<option value="">لا توجد خطط أسعار متاحة</option>';
                    console.warn('لا توجد خطط أسعار للخدمة المختارة');
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل خطط الأسعار:', error);
                pricingPlanSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
                alert('حدث خطأ في تحميل خطط الأسعار: ' + error.message);
            });
    } else {
        pricingPlanSelect.innerHTML = '<option value="">اختر خطة السعر</option>';
    }
}

// تحميل تفاصيل الخطة عند الاختيار
function loadPlanDetails() {
    const pricingPlanSelect = document.getElementById('pricing_plan_id');
    const selectedOption = pricingPlanSelect.options[pricingPlanSelect.selectedIndex];
    const planDetails = document.getElementById('plan_details');
    const scheduleDiv = document.getElementById('installment_schedule');

    if (selectedOption.value && selectedOption.dataset.planData) {
        const plan = JSON.parse(selectedOption.dataset.planData);

        document.getElementById('total_price').textContent = `${parseFloat(plan.price).toFixed(2)} جنيه`;
        document.getElementById('down_payment').textContent = `${parseFloat(plan.down_payment).toFixed(2)} جنيه`;
        document.getElementById('installments_count').textContent = `${plan.installments_count} قسط`;
        document.getElementById('installment_amount').textContent = `${parseFloat(plan.installment_amount).toFixed(2)} جنيه`;

        planDetails.style.display = 'block';

        // حساب وعرض جدولة الأقساط
        calculateInstallmentSchedule(plan);
    } else {
        planDetails.style.display = 'none';
        scheduleDiv.style.display = 'none';
    }
}

// حساب جدولة الأقساط بناءً على تاريخ الشراء
function calculateInstallmentSchedule(plan) {
    const purchaseDate = document.getElementById('purchase_date').value;
    const scheduleDiv = document.getElementById('installment_schedule');
    const scheduleBody = document.getElementById('schedule_body');

    if (!purchaseDate || !plan.installments_count) {
        scheduleDiv.style.display = 'none';
        return;
    }

    // حساب تاريخ أول قسط (في نفس اليوم من الشهر التالي)
    const purchaseDateObj = new Date(purchaseDate);

    // مسح الجدول السابق
    scheduleBody.innerHTML = '';

    // إنشاء صفوف الجدول
    for (let i = 1; i <= plan.installments_count; i++) {
        const installmentDate = new Date(purchaseDateObj);
        installmentDate.setMonth(installmentDate.getMonth() + i); // إضافة عدد الأشهر

        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="badge bg-primary">${i}</span></td>
            <td>
                <i class="fas fa-calendar me-1"></i>
                ${installmentDate.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                })}
            </td>
            <td>
                <strong>${parseFloat(plan.installment_amount).toFixed(2)} جنيه</strong>
            </td>
        `;
        scheduleBody.appendChild(row);
    }

    scheduleDiv.style.display = 'block';
}

// تحديث الجدولة عند تغيير تاريخ الشراء
document.addEventListener('DOMContentLoaded', function() {
    const purchaseDateInput = document.getElementById('purchase_date');
    if (purchaseDateInput) {
        purchaseDateInput.addEventListener('change', function() {
            const pricingPlanSelect = document.getElementById('pricing_plan_id');
            const selectedOption = pricingPlanSelect.options[pricingPlanSelect.selectedIndex];

            if (selectedOption.value && selectedOption.dataset.planData) {
                const plan = JSON.parse(selectedOption.dataset.planData);
                calculateInstallmentSchedule(plan);
            }
        });
    }
});

// عرض تفاصيل خطة التقسيط
function viewInstallmentDetails(installmentId) {
    const modal = new bootstrap.Modal(document.getElementById('installmentDetailsModal'));
    const content = document.getElementById('installment_details_content');

    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    modal.show();

    fetch(`../api/admin_get_installment_details.php?id=${installmentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = generateInstallmentDetailsHTML(data.plan, data.payments);
            } else {
                content.innerHTML = '<div class="alert alert-danger">فشل في تحميل التفاصيل</div>';
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل التفاصيل:', error);
            content.innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل التفاصيل</div>';
        });
}

// إنشاء HTML لتفاصيل القسط
function generateInstallmentDetailsHTML(plan, payments) {
    // حساب المبلغ المدفوع مع المقدم
    const paidInstallments = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + parseFloat(p.amount), 0);
    const downPayment = parseFloat(plan.down_payment) || 0;
    const paidAmount = downPayment + paidInstallments;
    const totalAmount = parseFloat(plan.total_amount);
    const remainingAmount = totalAmount - paidAmount;
    const progressPercentage = (paidAmount / totalAmount) * 100;

    let html = `
        <div class="row mb-4">
            <div class="col-md-6">
                <h6>معلومات العميل</h6>
                <p><strong>الاسم:</strong> ${plan.user_name}</p>
                <p><strong>الهاتف:</strong> ${plan.phone}</p>
                <p><strong>الخدمة:</strong> ${plan.service_name}</p>
                <p><strong>الخطة:</strong> ${plan.plan_name}</p>
            </div>
            <div class="col-md-6">
                <h6>ملخص مالي</h6>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center p-3 bg-primary text-white rounded">
                            <h5>${totalAmount.toFixed(2)} جنيه</h5>
                            <small>المبلغ الإجمالي</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-success text-white rounded">
                            <h5>${paidAmount.toFixed(2)} جنيه</h5>
                            <small>المدفوع</small>
                        </div>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" style="width: ${progressPercentage}%">
                            ${progressPercentage.toFixed(1)}%
                        </div>
                    </div>
                    <div class="text-center mt-1">
                        <small>المتبقي: ${remainingAmount.toFixed(2)} جنيه</small>
                    </div>
                </div>
            </div>
        </div>

        <h6>جدول الأقساط</h6>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم القسط</th>
                        <th>المبلغ</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>تاريخ الدفع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    payments.forEach(payment => {
        const statusBadge = getStatusBadge(payment.status);
        const actionButtons = generatePaymentActionButtons(payment);

        html += `
            <tr>
                <td>${payment.installment_number}</td>
                <td>${parseFloat(payment.amount).toFixed(2)} جنيه</td>
                <td>${payment.due_date}</td>
                <td>${payment.paid_date || '-'}</td>
                <td>${statusBadge}</td>
                <td>${actionButtons}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    return html;
}

// إنشاء شارة الحالة
function getStatusBadge(status) {
    switch (status) {
        case 'paid':
            return '<span class="badge bg-success">مدفوع</span>';
        case 'overdue':
            return '<span class="badge bg-danger">متأخر</span>';
        case 'pending':
        default:
            return '<span class="badge bg-warning">معلق</span>';
    }
}

// إنشاء أزرار الإجراءات للدفعة
function generatePaymentActionButtons(payment) {
    let buttons = '<div class="btn-group" role="group">';

    if (payment.status !== 'paid') {
        buttons += `
            <button class="btn btn-sm btn-success" onclick="updatePaymentStatus(${payment.id}, 'paid')">
                <i class="fas fa-check"></i> مدفوع
            </button>
        `;
    }

    if (payment.status !== 'overdue') {
        buttons += `
            <button class="btn btn-sm btn-danger" onclick="updatePaymentStatus(${payment.id}, 'overdue')">
                <i class="fas fa-exclamation-triangle"></i> متأخر
            </button>
        `;
    }

    if (payment.status !== 'pending') {
        buttons += `
            <button class="btn btn-sm btn-warning" onclick="updatePaymentStatus(${payment.id}, 'pending')">
                <i class="fas fa-clock"></i> معلق
            </button>
        `;
    }

    buttons += `
        <button class="btn btn-sm btn-info" onclick="sendPaymentReminder(${payment.id})">
            <i class="fab fa-whatsapp"></i>
        </button>
    `;

    buttons += '</div>';
    return buttons;
}

// تحديث حالة الدفعة
function updatePaymentStatus(paymentId, status) {
    if (confirm('هل أنت متأكد من تحديث حالة هذه الدفعة؟')) {
        const formData = new FormData();
        formData.append('action', 'update_payment_status');
        formData.append('payment_id', paymentId);
        formData.append('status', status);

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            console.error('خطأ في تحديث الحالة:', error);
            alert('حدث خطأ في تحديث الحالة');
        });
    }
}

// إرسال تذكير دفعة
function sendPaymentReminder(paymentId) {
    if (confirm('هل تريد إرسال تذكير دفع عبر واتساب؟')) {
        const formData = new FormData();
        formData.append('action', 'send_reminder');
        formData.append('payment_id', paymentId);

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            alert('تم إرسال التذكير بنجاح');
        })
        .catch(error => {
            console.error('خطأ في إرسال التذكير:', error);
            alert('حدث خطأ في إرسال التذكير');
        });
    }
}

// إرسال تذكير لخطة التقسيط
function sendInstallmentReminder(installmentId) {
    if (confirm('هل تريد إرسال تذكير عام لهذه الخطة؟')) {
        // يمكن تطوير هذه الوظيفة لاحقاً
        alert('سيتم تطوير هذه الميزة قريباً');
    }
}
</script>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>
