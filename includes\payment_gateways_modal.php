<?php
// مكون بوابات الدفع - يتم استدعاؤه في الصفحات التي تحتاج دفع

// جلب بوابات الدفع النشطة
try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $stmt = $pdo->prepare("SELECT * FROM payment_gateways WHERE is_active = TRUE ORDER BY name");
    $stmt->execute();
    $payment_gateways = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $payment_gateways = [];
}
?>

<!-- Modal بوابات الدفع -->
<div class="modal fade" id="paymentGatewaysModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اختر طريقة الدفع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="paymentInfo" class="alert alert-info mb-4" style="display: none;">
                    <!-- سيتم ملء معلومات الدفع هنا -->
                </div>

                <!-- تعليمات للمستخدم -->
                <div class="alert alert-warning mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>خطوات الدفع:</strong>
                    <ol class="mb-0 mt-2">
                        <li>اختر بوابة الدفع المناسبة أدناه</li>
                        <li>قم بإجراء الدفع باستخدام التفاصيل المعروضة</li>
                        <li>ارفع إثبات الدفع (صورة الإيصال أو لقطة الشاشة)</li>
                        <li>اضغط "إرسال طلب الدفع" للمراجعة</li>
                    </ol>
                </div>

                <h6 class="mb-3">اختر بوابة الدفع المناسبة:</h6>
                <div class="row">
                    <?php foreach ($payment_gateways as $gateway): ?>
                    <div class="col-md-6 mb-3">
                        <div class="payment-gateway-card" data-gateway-id="<?= $gateway['id'] ?>" 
                             data-gateway-name="<?= htmlspecialchars($gateway['name']) ?>"
                             data-payment-method="<?= htmlspecialchars($gateway['payment_method']) ?>"
                             data-description="<?= htmlspecialchars($gateway['description']) ?>">
                            <div class="card h-100 border-2" style="cursor: pointer; transition: all 0.3s ease;">
                                <div class="card-body text-center">
                                    <?php if ($gateway['image']): ?>
                                        <img src="<?= htmlspecialchars($gateway['image']) ?>" 
                                             alt="<?= htmlspecialchars($gateway['name']) ?>"
                                             style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px; margin-bottom: 15px;">
                                    <?php else: ?>
                                        <div style="width: 80px; height: 80px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                            <i class="fas fa-credit-card fa-2x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <h6 class="card-title"><?= htmlspecialchars($gateway['name']) ?></h6>
                                    
                                    <?php if ($gateway['description']): ?>
                                        <p class="card-text text-muted small">
                                            <?= htmlspecialchars($gateway['description']) ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- تفاصيل الدفع المختارة -->
                <div id="selectedGatewayDetails" style="display: none;">
                    <hr>
                    <div class="alert alert-primary">
                        <h6 id="selectedGatewayName"></h6>
                        <div id="selectedPaymentMethod" style="white-space: pre-line;"></div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                قم بإجراء الدفع أولاً ثم ارفع إثبات الدفع أدناه
                            </small>
                        </div>
                    </div>

                    <!-- نموذج رفع إثبات الدفع -->
                    <form id="paymentProofForm" enctype="multipart/form-data">
                        <input type="hidden" id="selectedGatewayId" name="gateway_id">
                        <input type="hidden" id="paymentAmount" name="amount">
                        <input type="hidden" id="paymentType" name="payment_type">
                        <input type="hidden" id="installmentPlanId" name="installment_plan_id">
                        <input type="hidden" id="paymentId" name="payment_id">

                        <div class="mb-3">
                            <label for="paymentProofs" class="form-label">
                                <i class="fas fa-upload"></i> رفع إثبات الدفع <span class="text-danger">*</span>
                            </label>
                            <input type="file" class="form-control" id="paymentProofs" name="payment_proofs[]"
                                   multiple accept="image/*" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                يمكن رفع حتى 3 صور (حد أقصى 10 ميجابايت للصورة الواحدة، 30 ميجابايت إجمالي)
                                <br>
                                <i class="fas fa-camera"></i>
                                ارفع صورة واضحة لإيصال الدفع أو لقطة شاشة من التحويل
                            </div>
                        </div>

                        <!-- معاينة الصور -->
                        <div id="imagePreview" class="row mb-3" style="display: none;"></div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>ملاحظة مهمة:</strong> تأكد من إجراء الدفع أولاً ثم رفع إثبات الدفع.
                            سيتم مراجعة طلبك خلال 24 ساعة وإشعارك بالنتيجة.
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-paper-plane"></i> إرسال طلب الدفع
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.payment-gateway-card:hover .card {
    border-color: #6c7ce7 !important;
    box-shadow: 0 4px 12px rgba(108, 124, 231, 0.15) !important;
    transform: translateY(-2px);
}

.payment-gateway-card.selected .card {
    border-color: #6c7ce7 !important;
    background-color: rgba(108, 124, 231, 0.05) !important;
    position: relative;
}

.payment-gateway-card.selected .card::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    background: #6c7ce7;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.image-preview {
    position: relative;
    display: inline-block;
    margin: 5px;
}

.image-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #ddd;
}

.image-preview .remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<script>
// التأكد من تحميل DOM قبل تعريف المتغيرات والدوال
document.addEventListener('DOMContentLoaded', function() {
    console.log('Payment gateways modal script loaded');

    // Add global focus management for all modals
    document.addEventListener('hide.bs.modal', function(event) {
        const modal = event.target;
        const focusedElement = modal.querySelector(':focus');
        if (focusedElement) {
            focusedElement.blur();
        }
    });

// متغيرات global
window.selectedGateway = null;
window.selectedFiles = [];

// فتح modal الدفع (fallback function)
if (!window.openPaymentModal) {
    window.openPaymentModal = function(amount, paymentType, installmentPlanId = null, paymentId = null, planId = null) {
    document.getElementById('paymentAmount').value = amount;
    document.getElementById('paymentType').value = paymentType;
    document.getElementById('installmentPlanId').value = installmentPlanId || '';
    document.getElementById('paymentId').value = paymentId || '';

    // حفظ planId للشراء الجديد
    if (planId) {
        document.getElementById('paymentProofForm').setAttribute('data-plan-id', planId);
    } else {
        document.getElementById('paymentProofForm').removeAttribute('data-plan-id');
    }

    // عرض معلومات الدفع
    const paymentInfo = document.getElementById('paymentInfo');
    const typeLabels = {
        'installment': 'دفع قسط',
        'down_payment': 'دفع مقدم',
        'service_purchase': 'شراء خدمة'
    };

    paymentInfo.innerHTML = `
        <strong>نوع الدفع:</strong> ${typeLabels[paymentType] || paymentType}<br>
        <strong>المبلغ:</strong> ${parseFloat(amount).toLocaleString()} جنيه
    `;
    paymentInfo.style.display = 'block';

    // إعادة تعيين النموذج
    resetPaymentForm();

    const modal = new bootstrap.Modal(document.getElementById('paymentGatewaysModal'));
    modal.show();
    };
}

// اختيار بوابة دفع - يتم التعامل معه في payment-system.js
    
    // معالجة رفع الصور
    document.getElementById('paymentProofs').addEventListener('change', function(e) {
        window.handleFileSelection(Array.from(e.target.files));
    });
    
    // معالجة إرسال النموذج
    document.getElementById('paymentProofForm').addEventListener('submit', function(e) {
        e.preventDefault();
        window.submitPaymentProof();
    });

window.handleFileSelection = function(files) {
    const maxFiles = 3;
    const maxFileSize = 10 * 1024 * 1024; // 10 MB
    const maxTotalSize = 30 * 1024 * 1024; // 30 MB
    
    if (files.length > maxFiles) {
        alert(`يمكن رفع ${maxFiles} صور كحد أقصى`);
        return;
    }
    
    let totalSize = 0;
    const validFiles = [];
    
    for (let file of files) {
        if (!file.type.startsWith('image/')) {
            alert('يُسمح بالصور فقط');
            return;
        }
        
        if (file.size > maxFileSize) {
            alert('حجم الصورة يجب أن يكون أقل من 10 ميجابايت');
            return;
        }
        
        totalSize += file.size;
        validFiles.push(file);
    }
    
    if (totalSize > maxTotalSize) {
        alert('الحجم الإجمالي للصور يجب أن يكون أقل من 30 ميجابايت');
        return;
    }
    
    window.selectedFiles = validFiles;
    window.displayImagePreviews();
}

window.displayImagePreviews = function() {
    const previewContainer = document.getElementById('imagePreview');
    previewContainer.innerHTML = '';
    
    if (window.selectedFiles.length === 0) {
        previewContainer.style.display = 'none';
        return;
    }

    previewContainer.style.display = 'block';

    window.selectedFiles.forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const col = document.createElement('div');
            col.className = 'col-auto';
            col.innerHTML = `
                <div class="image-preview">
                    <img src="${e.target.result}" alt="معاينة ${index + 1}">
                    <button type="button" class="remove-image" onclick="removeImage(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            previewContainer.appendChild(col);
        };
        reader.readAsDataURL(file);
    });
}

window.removeImage = function(index) {
    window.selectedFiles.splice(index, 1);
    
    // تحديث input file
    const fileInput = document.getElementById('paymentProofs');
    const dt = new DataTransfer();
    selectedFiles.forEach(file => dt.items.add(file));
    fileInput.files = dt.files;
    
    window.displayImagePreviews();
}

window.submitPaymentProof = function() {
    // الحصول على بوابة الدفع المختارة
    const selectedGatewayElement = document.querySelector('.payment-gateway-card.selected');
    const gatewayIdInput = document.getElementById('selectedGatewayId');

    if (!selectedGatewayElement || !gatewayIdInput.value) {
        alert('يرجى اختيار بوابة دفع');
        return;
    }

    if (window.selectedFiles.length === 0) {
        alert('يرجى رفع إثبات دفع واحد على الأقل');
        return;
    }

    const formData = new FormData();
    formData.append('gateway_id', gatewayIdInput.value);
    formData.append('amount', document.getElementById('paymentAmount').value);
    formData.append('payment_type', document.getElementById('paymentType').value);
    formData.append('installment_plan_id', document.getElementById('installmentPlanId').value);
    formData.append('payment_id', document.getElementById('paymentId').value);
    
    window.selectedFiles.forEach(file => {
        formData.append('payment_proofs[]', file);
    });
    
    // عرض مؤشر التحميل
    const submitBtn = document.querySelector('#paymentProofForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
    submitBtn.disabled = true;
    
    // التحقق من وجود planId للشراء الجديد
    const planId = document.getElementById('paymentProofForm').getAttribute('data-plan-id');

    if (planId && window.pendingPurchase) {
        // إضافة بيانات الشراء للطلب
        formData.append('plan_id', planId);
        formData.append('purchase_payment_type', window.pendingPurchase.payment_type);

        fetch('api/submit_purchase_payment.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                bootstrap.Modal.getInstance(document.getElementById('paymentGatewaysModal')).hide();
                // إعادة توجيه للوحة التحكم
                window.location.href = 'dashboard.php';
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إرسال الطلب');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    } else {
        // الطلب العادي للأقساط
        fetch('api/submit_payment_proof.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                bootstrap.Modal.getInstance(document.getElementById('paymentGatewaysModal')).hide();
                location.reload(); // إعادة تحميل الصفحة لتحديث البيانات
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إرسال الطلب');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }
}

window.resetPaymentForm = function() {
    window.selectedGateway = null;
    window.selectedFiles = [];
    
    document.querySelectorAll('.payment-gateway-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    document.getElementById('selectedGatewayDetails').style.display = 'none';
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('paymentProofForm').reset();
}

}); // إغلاق DOMContentLoaded
</script>
