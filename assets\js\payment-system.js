/**
 * Payment System JavaScript
 * Handles payment gateway modal and related functionality
 */

// Global variables for payment system
window.PaymentSystem = {
    selectedGateway: null,
    selectedFiles: [],
    
    // Initialize payment system
    init: function() {
        console.log('Payment System: Initializing...');
        this.bindEvents();
    },
    
    // Bind event listeners
    bindEvents: function() {
        // Payment gateway selection
        document.addEventListener('click', function(e) {
            if (e.target.closest('.payment-gateway-card')) {
                PaymentSystem.selectGateway(e.target.closest('.payment-gateway-card'));
            }
        });
        
        // File input change
        const fileInput = document.getElementById('paymentProofs');
        if (fileInput) {
            fileInput.addEventListener('change', function(e) {
                PaymentSystem.handleFileSelection(Array.from(e.target.files));
            });
        }
        
        // Form submission
        const form = document.getElementById('paymentProofForm');
        if (form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                PaymentSystem.submitPaymentProof();
            });
        }
    },
    
    // Open payment modal
    openModal: function(amount, paymentType, installmentPlanId = null, paymentId = null, planId = null) {
        console.log('Opening payment modal:', { amount, paymentType, installmentPlanId, paymentId, planId });
        
        const amountInput = document.getElementById('paymentAmount');
        const typeInput = document.getElementById('paymentType');
        const installmentInput = document.getElementById('installmentPlanId');
        const paymentIdInput = document.getElementById('paymentId');
        const form = document.getElementById('paymentProofForm');
        
        if (amountInput) amountInput.value = amount;
        if (typeInput) typeInput.value = paymentType;
        if (installmentInput) installmentInput.value = installmentPlanId || '';
        if (paymentIdInput) paymentIdInput.value = paymentId || '';
        
        // Save planId for new purchases
        if (form) {
            if (planId) {
                form.setAttribute('data-plan-id', planId);
            } else {
                form.removeAttribute('data-plan-id');
            }
        }
        
        // Show modal
        const modal = document.getElementById('paymentGatewaysModal');
        if (modal && typeof bootstrap !== 'undefined') {
            const bsModal = new bootstrap.Modal(modal);

            // Add event listeners for proper focus management
            modal.addEventListener('hide.bs.modal', function () {
                // Remove focus from any focused elements inside the modal BEFORE it's hidden
                const focusedElement = modal.querySelector(':focus');
                if (focusedElement) {
                    focusedElement.blur();
                }
            });

            bsModal.show();
        }
        
        // Reset form
        this.resetForm();
    },
    
    // Select payment gateway
    selectGateway: function(card) {
        console.log('PaymentSystem.selectGateway called with:', card);

        // Remove previous selection
        document.querySelectorAll('.payment-gateway-card').forEach(c => {
            c.classList.remove('selected');
        });

        // Add selection to clicked card
        card.classList.add('selected');

        // Store selected gateway
        this.selectedGateway = {
            id: card.dataset.gatewayId,
            name: card.dataset.gatewayName,
            method: card.dataset.paymentMethod
        };

        // تحديث المتغير العام أيضاً
        window.selectedGateway = this.selectedGateway;

        console.log('Selected gateway data:', this.selectedGateway);

        // Show gateway details - update existing elements instead of replacing content
        const gatewayNameElement = document.getElementById('selectedGatewayName');
        const paymentMethodElement = document.getElementById('selectedPaymentMethod');
        const gatewayIdInput = document.getElementById('selectedGatewayId');
        const detailsDiv = document.getElementById('selectedGatewayDetails');

        console.log('Elements found:', {
            gatewayNameElement: !!gatewayNameElement,
            paymentMethodElement: !!paymentMethodElement,
            gatewayIdInput: !!gatewayIdInput,
            detailsDiv: !!detailsDiv
        });

        if (gatewayNameElement) gatewayNameElement.textContent = this.selectedGateway.name;
        if (paymentMethodElement) paymentMethodElement.textContent = this.selectedGateway.method;
        if (gatewayIdInput) gatewayIdInput.value = this.selectedGateway.id;
        if (detailsDiv) {
            detailsDiv.style.display = 'block';
            console.log('Gateway details div shown');
        } else {
            console.error('selectedGatewayDetails element not found!');
        }
    },
    
    // Handle file selection
    handleFileSelection: function(files) {
        const maxFiles = 3;
        const maxFileSize = 10 * 1024 * 1024; // 10 MB
        const maxTotalSize = 30 * 1024 * 1024; // 30 MB
        
        if (files.length > maxFiles) {
            alert(`يمكن رفع ${maxFiles} صور كحد أقصى`);
            return;
        }
        
        let totalSize = 0;
        const validFiles = [];
        
        for (let file of files) {
            if (!file.type.startsWith('image/')) {
                alert('يجب أن تكون الملفات من نوع صورة');
                return;
            }
            
            if (file.size > maxFileSize) {
                alert(`حجم الملف ${file.name} كبير جداً. الحد الأقصى 10 ميجابايت`);
                return;
            }
            
            totalSize += file.size;
            validFiles.push(file);
        }
        
        if (totalSize > maxTotalSize) {
            alert('الحجم الإجمالي للملفات كبير جداً. الحد الأقصى 30 ميجابايت');
            return;
        }
        
        this.selectedFiles = validFiles;
        this.displayImagePreviews();
    },
    
    // Display image previews
    displayImagePreviews: function() {
        const previewContainer = document.getElementById('imagePreview');
        if (!previewContainer) return;
        
        previewContainer.innerHTML = '';
        
        if (this.selectedFiles.length === 0) {
            previewContainer.style.display = 'none';
            return;
        }
        
        previewContainer.style.display = 'block';
        
        this.selectedFiles.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'image-preview-item';
                imageDiv.innerHTML = `
                    <img src="${e.target.result}" alt="Preview ${index + 1}">
                    <button type="button" class="btn btn-sm btn-danger remove-image" onclick="PaymentSystem.removeImage(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                previewContainer.appendChild(imageDiv);
            };
            reader.readAsDataURL(file);
        });
    },
    
    // Remove image
    removeImage: function(index) {
        this.selectedFiles.splice(index, 1);
        
        // Update file input
        const fileInput = document.getElementById('paymentProofs');
        if (fileInput) {
            const dt = new DataTransfer();
            this.selectedFiles.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;
        }
        
        this.displayImagePreviews();
    },
    
    // Submit payment proof
    submitPaymentProof: function() {
        if (!this.selectedGateway) {
            alert('يرجى اختيار بوابة دفع');
            return;
        }
        
        if (this.selectedFiles.length === 0) {
            alert('يرجى رفع إثبات الدفع');
            return;
        }
        
        const formData = new FormData();
        const form = document.getElementById('paymentProofForm');
        
        // Add form fields
        formData.append('gateway_id', this.selectedGateway.id);
        formData.append('amount', document.getElementById('paymentAmount').value);
        formData.append('payment_type', document.getElementById('paymentType').value);
        formData.append('installment_plan_id', document.getElementById('installmentPlanId').value);
        formData.append('payment_id', document.getElementById('paymentId').value);
        
        // Add plan ID for new purchases
        const planId = form ? form.getAttribute('data-plan-id') : null;
        if (planId) {
            formData.append('plan_id', planId);
        }
        
        // Add files
        this.selectedFiles.forEach((file) => {
            formData.append(`payment_proofs[]`, file);
        });
        
        // Show loading
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
        submitBtn.disabled = true;
        
        // Submit
        fetch('api/submit_purchase_payment.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('paymentGatewaysModal'));
                if (modal) modal.hide();
                
                // Show success message
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: 'تم الإرسال بنجاح',
                        text: 'تم إرسال طلب الدفع بنجاح. سيتم مراجعته من قبل الإدارة.',
                        icon: 'success'
                    }).then(() => {
                        // Redirect or refresh
                        if (planId) {
                            window.location.href = 'dashboard.php';
                        } else {
                            window.location.reload();
                        }
                    });
                }
            } else {
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الإرسال');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    },
    
    // Reset form
    resetForm: function() {
        this.selectedGateway = null;
        this.selectedFiles = [];
        
        document.querySelectorAll('.payment-gateway-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        const detailsDiv = document.getElementById('selectedGatewayDetails');
        if (detailsDiv) detailsDiv.style.display = 'none';
        
        const previewDiv = document.getElementById('imagePreview');
        if (previewDiv) previewDiv.style.display = 'none';
        
        const form = document.getElementById('paymentProofForm');
        if (form) form.reset();
    }
};

// Make openPaymentModal globally available
window.openPaymentModal = function(amount, paymentType, installmentPlanId, paymentId, planId) {
    return PaymentSystem.openModal(amount, paymentType, installmentPlanId, paymentId, planId);
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    PaymentSystem.init();
});
