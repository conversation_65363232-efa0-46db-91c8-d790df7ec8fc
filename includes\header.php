<?php
// التحقق من تسجيل الدخول
$is_logged_in = isLoggedIn();
$current_user = null;
if ($is_logged_in) {
    $current_user = getCurrentUser();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($page_title) ? $page_title . ' - ' : '' ?><?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- intl-tel-input -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/css/intlTelInput.css">
    
    <!-- Swiper CSS for Carousel -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Dark Mode CSS -->
    <link href="assets/css/dark-mode.css" rel="stylesheet">

    <!-- Dark Only CSS (Force dark theme) -->
    <link href="assets/css/dark-only.css" rel="stylesheet">

    <!-- Theme Initialization (must be loaded early) -->
    <script src="assets/js/theme-init.js"></script>

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/enhancements.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --text-color: #2c3e50;
            --text-muted: #6c757d;
            --border-radius: 15px;
            --box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
        }
        
        /* Modern Header Styles */
        .modern-header {
            background: var(--primary-gradient);
            box-shadow: var(--box-shadow);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: white !important;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .navbar-brand:hover {
            transform: scale(1.05);
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            padding: 0.75rem 1rem !important;
            border-radius: var(--border-radius);
            margin: 0 0.25rem;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        
        .navbar-nav .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            transition: var(--transition);
        }
        
        .navbar-nav .nav-link:hover::before {
            left: 0;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
            transform: translateY(-2px);
        }
        
        .navbar-nav .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white !important;
        }
        

        
        /* User Menu */
        .user-menu .dropdown-toggle {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.2);
            color: white !important;
            border-radius: 25px;
            padding: 0.5rem 1rem;
            transition: var(--transition);
        }
        
        .user-menu .dropdown-toggle:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        .user-menu .dropdown-menu {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-top: 0.5rem;
        }
        
        /* Mobile Menu */
        .navbar-toggler {
            border: none;
            padding: 0.5rem;
            border-radius: var(--border-radius);
            background: rgba(255,255,255,0.1);
        }
        
        .navbar-toggler:focus {
            box-shadow: none;
        }
        
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        
        /* Auth Buttons */
        .auth-buttons .btn {
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            margin: 0 0.25rem;
            transition: var(--transition);
        }
        
        .btn-outline-light {
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
        }
        
        .btn-outline-light:hover {
            background: white;
            color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .btn-light {
            background: white;
            color: var(--primary-color);
            border: 2px solid white;
        }

        .btn-light:hover {
            background: rgba(255,255,255,0.9);
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        /* Responsive Design */
        @media (max-width: 991.98px) {
            .navbar-nav {
                background: rgba(255,255,255,0.1);
                border-radius: var(--border-radius);
                padding: 1rem;
                margin-top: 1rem;
            }
            
            .auth-buttons {
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid rgba(255,255,255,0.2);
            }
            

        }
        
        /* Modern Footer Styles */
        .modern-footer {
            background: var(--dark-gradient);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 5rem;
        }
        
        .footer-brand h5 {
            color: white;
            font-weight: 700;
        }
        
        .footer-description {
            color: rgba(255,255,255,0.7);
            margin-bottom: 1.5rem;
        }
        
        .social-links {
            display: flex;
            gap: 1rem;
        }
        
        .social-link {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.1);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .social-link:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
        }
        
        .footer-title {
            color: white;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .footer-links {
            list-style: none;
            padding: 0;
        }
        
        .footer-links li {
            margin-bottom: 0.5rem;
        }
        
        .footer-links a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .footer-links a:hover {
            color: white;
            padding-right: 0.5rem;
        }
        
        .contact-info {
            color: rgba(255,255,255,0.7);
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        
        .contact-item i {
            width: 20px;
            margin-left: 0.75rem;
            color: var(--primary-color);
        }
        
        .footer-divider {
            border-color: rgba(255,255,255,0.2);
            margin: 2rem 0 1rem;
        }
        
        .copyright {
            color: rgba(255,255,255,0.7);
            margin: 0;
        }
        
        .footer-links-inline {
            display: flex;
            gap: 2rem;
        }
        
        .footer-links-inline a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .footer-links-inline a:hover {
            color: white;
        }
    </style>
</head>
<body>
    <!-- Scroll Progress Indicator -->
    <div class="scroll-indicator">
        <div class="scroll-progress" id="scrollProgress"></div>
    </div>

    <!-- Modern Header -->
    <header class="modern-header">
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="index.php">
                    <i class="fas fa-rocket me-2"></i>
                    <?php echo SITE_NAME; ?>
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : '' ?>" href="index.php">
                                <i class="fas fa-home me-1"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'services.php' ? 'active' : '' ?>" href="services.php">
                                <i class="fas fa-cogs me-1"></i>
                                الخدمات
                            </a>
                        </li>
                        <?php if ($is_logged_in): ?>
                        <li class="nav-item">
                            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : '' ?>" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-1"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'my_installments.php' ? 'active' : '' ?>" href="my_installments.php">
                                <i class="fas fa-credit-card me-1"></i>
                                أقساطي
                            </a>
                        </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="#about">
                                <i class="fas fa-info-circle me-1"></i>
                                من نحن
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#contact">
                                <i class="fas fa-envelope me-1"></i>
                                اتصل بنا
                            </a>
                        </li>
                    </ul>
                    
                    <div class="d-flex align-items-center">
                        <?php if ($is_logged_in): ?>
                            <!-- User Menu -->
                            <div class="user-menu dropdown">
                                <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle me-2"></i>
                                    <?= htmlspecialchars($current_user['name'] ?? 'المستخدم') ?>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>
                                        لوحة التحكم
                                    </a></li>
                                    <li><a class="dropdown-item" href="my_installments.php">
                                        <i class="fas fa-credit-card me-2"></i>
                                        أقساطي
                                    </a></li>
                                    <li><a class="dropdown-item" href="my_payment_requests.php">
                                        <i class="fas fa-receipt me-2"></i>
                                        طلبات الدفع
                                    </a></li>
                                    <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="admin/index.php">
                                        <i class="fas fa-cog me-2"></i>
                                        لوحة الإدارة
                                    </a></li>
                                    <?php endif; ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="logout.php">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        تسجيل الخروج
                                    </a></li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <!-- Auth Buttons -->
                            <div class="auth-buttons">
                                <a href="login.php" class="btn btn-outline-light">
                                    <i class="fas fa-sign-in-alt me-1"></i>
                                    تسجيل الدخول
                                </a>
                                <a href="register.php" class="btn btn-light">
                                    <i class="fas fa-user-plus me-1"></i>
                                    إنشاء حساب
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <main>
