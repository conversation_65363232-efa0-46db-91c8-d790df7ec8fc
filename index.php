<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/db_config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/functions.php';

$page_title = 'الرئيسية';

// جلب الخدمات المتاحة
try {
    $db = new Database();
    $pdo = $db->getConnection();

    // التحقق من وجود حقل is_active في جدول services
    $stmt = $pdo->prepare("SHOW COLUMNS FROM services LIKE 'is_active'");
    $stmt->execute();
    $is_active_exists = $stmt->fetch();

    // جلب الخدمات (مع أو بدون شرط is_active حسب وجود الحقل)
    if ($is_active_exists) {
        $stmt = $pdo->prepare("SELECT * FROM services WHERE is_active = TRUE ORDER BY created_at DESC LIMIT 6");
    } else {
        $stmt = $pdo->prepare("SELECT * FROM services ORDER BY created_at DESC LIMIT 6");
    }
    $stmt->execute();
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات الموقع
    $stats = [
        'users' => 0,
        'services' => 0,
        'installments' => 0,
        'completed' => 0
    ];

    // عدد المستخدمين
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE is_admin = FALSE");
    $stmt->execute();
    $stats['users'] = $stmt->fetchColumn();

    // عدد الخدمات
    if ($is_active_exists) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM services WHERE is_active = TRUE");
    } else {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM services");
    }
    $stmt->execute();
    $stats['services'] = $stmt->fetchColumn();

    // عدد خطط التقسيط
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM installment_plans");
    $stmt->execute();
    $stats['installments'] = $stmt->fetchColumn();

    // عدد الخطط المكتملة
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM installment_plans WHERE status = 'completed'");
    $stmt->execute();
    $stats['completed'] = $stmt->fetchColumn();

} catch (Exception $e) {
    error_log("Error loading homepage data: " . $e->getMessage());
    $services = [];
    $stats = ['users' => 0, 'services' => 0, 'installments' => 0, 'completed' => 0];
}

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero-section" data-aos="fade-in">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <div class="hero-content">
                    <h1 class="hero-title">مرحباً بك في <span class="text-gradient"><?php echo SITE_NAME; ?></span></h1>
                    <p class="hero-subtitle">
                        نحن نقدم أفضل خدمات التقسيط والمتابعة المالية لمساعدتك في تحقيق أهدافك المالية بسهولة وأمان
                    </p>
                    <div class="hero-buttons">
                        <?php if (isLoggedIn()): ?>
                            <a href="services.php" class="btn btn-light btn-lg">
                                <i class="fas fa-cogs me-2"></i>
                                تصفح الخدمات
                            </a>
                            <a href="dashboard.php" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        <?php else: ?>
                            <a href="register.php" class="btn btn-light btn-lg">
                                <i class="fas fa-user-plus me-2"></i>
                                ابدأ الآن
                            </a>
                            <a href="services.php" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-eye me-2"></i>
                                تصفح الخدمات
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="hero-image text-center">
                    <i class="fas fa-rocket" style="font-size: 15rem; color: rgba(255,255,255,0.2);"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="stats-section" data-aos="fade-up">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item" data-aos="zoom-in" data-aos-delay="100">
                    <span class="stat-number" data-count="<?= $stats['users'] ?>">0</span>
                    <div class="stat-label">عميل راضٍ</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item" data-aos="zoom-in" data-aos-delay="200">
                    <span class="stat-number" data-count="<?= $stats['services'] ?>">0</span>
                    <div class="stat-label">خدمة متاحة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item" data-aos="zoom-in" data-aos-delay="300">
                    <span class="stat-number" data-count="<?= $stats['installments'] ?>">0</span>
                    <div class="stat-label">خطة تقسيط</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item" data-aos="zoom-in" data-aos-delay="400">
                    <span class="stat-number" data-count="<?= $stats['completed'] ?>">0</span>
                    <div class="stat-label">خطة مكتملة</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-5" style="background-color: #1e1e1e;" id="services">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center" data-aos="fade-up">
                <h2 class="display-4 fw-bold text-gradient mb-3" style="color: #e0e0e0;">خدماتنا المميزة</h2>
                <p class="lead" style="color: #b0b0b0;">نقدم مجموعة شاملة من الخدمات المالية والاستشارية</p>
            </div>
        </div>

        <?php if (!empty($services)): ?>
        <!-- Services Carousel -->
        <div class="services-carousel" data-aos="fade-up" data-aos-delay="200">
            <div class="swiper servicesSwiper">
                <div class="swiper-wrapper">
                    <?php foreach ($services as $service): ?>
                    <div class="swiper-slide">
                        <div class="service-card enhanced-card h-100">
                            <div class="card-body text-center">
                                <div class="service-icon float-animation">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <h5 class="card-title"><?= htmlspecialchars($service['name']) ?></h5>
                                <p class="card-text"><?= htmlspecialchars(substr($service['description'], 0, 100)) ?>...</p>
                                <div class="mt-auto">
                                    <a href="services.php?service=<?= $service['id'] ?>" class="btn btn-primary-modern btn-enhanced">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        تفاصيل أكثر
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
        <?php else: ?>
        <div class="text-center" data-aos="fade-up">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد خدمات متاحة حالياً
            </div>
        </div>
        <?php endif; ?>

        <div class="text-center mt-4" data-aos="fade-up" data-aos-delay="300">
            <a href="services.php" class="btn btn-secondary-modern btn-lg">
                <i class="fas fa-eye me-2"></i>
                عرض جميع الخدمات
            </a>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section" id="features">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center" data-aos="fade-up">
                <h2 class="display-4 fw-bold text-gradient mb-3" style="color: #e0e0e0;">لماذا تختارنا؟</h2>
                <p class="lead" style="color: #b0b0b0;">نحن نقدم أفضل الحلول المالية مع ضمان الجودة والأمان</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-item" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="feature-title">أمان وموثوقية</h4>
                    <p class="feature-description">
                        نضمن أعلى مستويات الأمان في جميع معاملاتك المالية مع حماية كاملة لبياناتك الشخصية
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-item" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4 class="feature-title">خدمة سريعة</h4>
                    <p class="feature-description">
                        نقدم خدمات سريعة ومرنة تناسب احتياجاتك مع إمكانية المتابعة على مدار الساعة
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-item" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h4 class="feature-title">دعم فني متميز</h4>
                    <p class="feature-description">
                        فريق دعم فني محترف متاح لمساعدتك في أي وقت لضمان أفضل تجربة استخدام
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-item" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 class="feature-title">تقارير مفصلة</h4>
                    <p class="feature-description">
                        احصل على تقارير مفصلة ودقيقة عن جميع معاملاتك وخطط التقسيط الخاصة بك
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-item" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4 class="feature-title">متوافق مع الجوال</h4>
                    <p class="feature-description">
                        منصة متجاوبة تعمل بكفاءة على جميع الأجهزة الذكية والحاسوب اللوحي
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-item" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <h4 class="feature-title">أسعار تنافسية</h4>
                    <p class="feature-description">
                        نقدم أفضل الأسعار في السوق مع خطط تقسيط مرنة تناسب جميع الميزانيات
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5" style="background: var(--gradient-secondary);" id="cta">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center text-white" data-aos="zoom-in">
                <h2 class="display-5 fw-bold mb-3">هل أنت مستعد للبدء؟</h2>
                <p class="lead mb-4">انضم إلى آلاف العملاء الراضين واستمتع بأفضل خدمات التقسيط</p>
                <?php if (!isLoggedIn()): ?>
                <a href="register.php" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-user-plus me-2"></i>
                    إنشاء حساب جديد
                </a>
                <a href="login.php" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </a>
                <?php else: ?>
                <a href="services.php" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-cogs me-2"></i>
                    تصفح الخدمات
                </a>
                <a href="dashboard.php" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="py-5" style="background-color: #1e1e1e;" id="about">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <h2 class="display-5 fw-bold text-gradient mb-4" style="color: #e0e0e0;">من نحن</h2>
                <p class="lead mb-4" style="color: #e0e0e0;">
                    نحن فريق من الخبراء المتخصصين في تقديم الحلول المالية المبتكرة والخدمات الاستشارية
                    التي تساعد عملاءنا على تحقيق أهدافهم المالية بكفاءة وأمان.
                </p>
                <p class="mb-4" style="color: #b0b0b0;">
                    مع سنوات من الخبرة في مجال الخدمات المالية، نفخر بتقديم حلول مخصصة تناسب احتياجات
                    كل عميل، مع ضمان أعلى مستويات الجودة والشفافية في جميع معاملاتنا.
                </p>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-gradient fw-bold" style="color: #6c7ce7;">+5</h3>
                            <p style="color: #b0b0b0;">سنوات خبرة</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-gradient fw-bold" style="color: #6c7ce7;">24/7</h3>
                            <p style="color: #b0b0b0;">دعم فني</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="text-center">
                    <i class="fas fa-users" style="font-size: 12rem; color: var(--primary-color); opacity: 0.1;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-5" style="background-color: #121212;" id="contact">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center" data-aos="fade-up">
                <h2 class="display-4 fw-bold text-gradient mb-3" style="color: #e0e0e0;">تواصل معنا</h2>
                <p class="lead" style="color: #b0b0b0;">نحن هنا لمساعدتك في أي وقت</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="modern-card text-center h-100" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-body">
                        <div class="service-icon mb-3">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h5 class="card-title">اتصل بنا</h5>
                        <p class="card-text">+20 ************</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="modern-card text-center h-100" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-body">
                        <div class="service-icon mb-3">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h5 class="card-title">راسلنا</h5>
                        <p class="card-text"><EMAIL></p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="modern-card text-center h-100" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-body">
                        <div class="service-icon mb-3">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h5 class="card-title">زورنا</h5>
                        <p class="card-text">القاهرة، مصر</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
$additional_scripts = '
<!-- Swiper JS -->
<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>

<!-- AOS Animation -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<script>
// Initialize AOS
AOS.init({
    duration: 800,
    easing: "ease-in-out",
    once: true,
    mirror: false
});

// Initialize Swiper
const swiper = new Swiper(".servicesSwiper", {
    slidesPerView: 1,
    spaceBetween: 30,
    loop: true,
    autoplay: {
        delay: 3000,
        disableOnInteraction: false,
    },
    pagination: {
        el: ".swiper-pagination",
        clickable: true,
    },
    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
    },
    breakpoints: {
        640: {
            slidesPerView: 2,
        },
        768: {
            slidesPerView: 2,
        },
        1024: {
            slidesPerView: 3,
        },
    },
});

// Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll("[data-count]");
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute("data-count"));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 16);
    });
}

// Trigger counter animation when stats section is visible
const statsSection = document.querySelector(".stats-section");
const statsObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateCounters();
            statsObserver.unobserve(entry.target);
        }
    });
});

if (statsSection) {
    statsObserver.observe(statsSection);
}
</script>
';

include 'includes/footer.php';
?>
