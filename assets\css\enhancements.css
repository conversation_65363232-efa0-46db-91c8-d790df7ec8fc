/* FutureWay - Enhanced Styles */

/* Advanced Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes slideInFromLeft {
    0% { transform: translateX(-100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes fadeInScale {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

/* Floating Elements */
.float-animation {
    animation: float 3s ease-in-out infinite;
}

.pulse-animation {
    animation: pulse 2s ease-in-out infinite;
}

/* Enhanced Cards */
.enhanced-card {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

.enhanced-card:hover::before {
    left: 100%;
}

.enhanced-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* Glass Morphism Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .glass-card {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Text Effects */
.gradient-text-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gradient-text-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gradient-text-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Buttons */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.btn-enhanced:active {
    transform: translateY(0);
}

/* Neon Glow Effect */
.neon-glow {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
    animation: neonGlow 2s ease-in-out infinite alternate;
}

@keyframes neonGlow {
    from { text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor; }
    to { text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor; }
}

/* Parallax Sections */
.parallax-section {
    position: relative;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

/* Loading Spinner */
.modern-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Bars */
.progress-modern {
    height: 8px;
    border-radius: 10px;
    background: rgba(102, 126, 234, 0.1);
    overflow: hidden;
}

.progress-modern .progress-bar {
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Tooltip Enhancements */
.tooltip-modern {
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    backdrop-filter: blur(10px);
}

/* Form Enhancements */
.form-floating-modern .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.form-floating-modern .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

.form-floating-modern label {
    color: #6c757d;
    transition: all 0.3s ease;
}

.form-floating-modern .form-control:focus ~ label,
.form-floating-modern .form-control:not(:placeholder-shown) ~ label {
    color: #667eea;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Scroll Indicators */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(102, 126, 234, 0.1);
    z-index: 9999;
}

.scroll-progress {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.1s ease;
}

/* Image Hover Effects */
.image-hover-zoom {
    overflow: hidden;
    border-radius: 15px;
}

.image-hover-zoom img {
    transition: transform 0.5s ease;
}

.image-hover-zoom:hover img {
    transform: scale(1.1);
}

/* Badge Enhancements */
.badge-modern {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.875rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

/* Table Enhancements */
.table-modern {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.table-modern thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.table-modern tbody tr {
    transition: all 0.3s ease;
}

.table-modern tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* Accessibility Enhancements */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    z-index: 10000;
    padding: 8px 16px;
    background: #000;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Focus Indicators */
*:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

button:focus,
.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.5);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .modern-card,
    .service-card {
        border: 2px solid currentColor;
    }
    
    .btn-modern {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .float-animation,
    .pulse-animation,
    .neon-glow {
        animation: none;
    }
}

/* Print Optimizations */
@media print {
    .enhanced-card,
    .modern-card,
    .service-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid;
    }
    
    .btn-enhanced,
    .btn-modern {
        background: #fff !important;
        color: #000 !important;
        border: 1px solid #000 !important;
    }
    
    .gradient-text-primary,
    .gradient-text-secondary,
    .gradient-text-success {
        -webkit-text-fill-color: initial;
        color: #000 !important;
    }
}

/* Enhanced Components - Dark Mode Only */
.enhanced-card::before {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent);
}

.glass-card {
    background: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-enhanced {
    background: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-enhanced:hover {
    background: var(--primary-color);
    color: white;
}

.modern-spinner {
    border: 4px solid rgba(108, 124, 231, 0.1);
    border-left: 4px solid #6c7ce7;
}

.progress-modern {
    background: rgba(108, 124, 231, 0.1);
}

.tooltip-modern {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid var(--border-color);
}

.form-floating-modern .form-control {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

.form-floating-modern .form-control:focus {
    border-color: var(--primary-color);
    background-color: var(--input-bg);
}

.form-floating-modern label {
    color: var(--text-muted);
}

.badge-modern {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%);
}

.table-modern thead th {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%);
}

.table-modern tbody tr:hover {
    background: rgba(108, 124, 231, 0.1);
}

/* Scroll Progress */
.scroll-indicator {
    background: rgba(108, 124, 231, 0.1);
}

.scroll-progress {
    background: linear-gradient(90deg, #6c7ce7, #8b5fbf);
}
