<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'يجب تسجيل الدخول أولاً'
    ]);
    exit();
}

try {
    if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
        throw new Exception('معرف خطة التقسيط مطلوب');
    }
    
    $plan_id = (int)$_GET['id'];
    $user_id = $_SESSION['user_id'];
    
    $db = new Database();
    $pdo = $db->getConnection();
    
    // جلب تفاصيل خطة التقسيط
    $stmt = $pdo->prepare("
        SELECT ip.*, pp.name as plan_name, s.name as service_name
        FROM installment_plans ip
        JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
        JOIN services s ON pp.service_id = s.id
        WHERE ip.id = ? AND ip.user_id = ?
    ");
    $stmt->execute([$plan_id, $user_id]);
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$plan) {
        throw new Exception('خطة التقسيط غير موجودة');
    }
    
    // جلب دفعات الأقساط
    $stmt = $pdo->prepare("
        SELECT * FROM payments
        WHERE installment_plan_id = ?
        ORDER BY installment_number ASC
    ");
    $stmt->execute([$plan_id]);
    $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'plan' => $plan,
        'payments' => $payments
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

