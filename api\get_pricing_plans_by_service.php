<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

$service_id = (int)($_GET['service_id'] ?? 0);

if ($service_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف الخدمة مطلوب']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $stmt = $pdo->prepare("
        SELECT id, name, price, features, down_payment, interest_rate, interest_type, 
               interest_value, installments_count, installment_amount, allow_installment
        FROM pricing_plans 
        WHERE service_id = ? AND allow_installment = TRUE AND is_active = TRUE
        ORDER BY price ASC
    ");
    $stmt->execute([$service_id]);
    $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'plans' => $plans,
        'count' => count($plans)
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_pricing_plans_by_service.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام: ' . $e->getMessage()]);
}
?>
