<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'طلبات الدفع';

try {
    $db = new Database();
    $pdo = $db->getConnection();

    // معالجة الإجراءات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        $request_id = (int)($_POST['request_id'] ?? 0);
        $admin_notes = sanitizeInput($_POST['admin_notes'] ?? '');
        $current_user_id = getCurrentUser()['id'];

        if ($action === 'approve') {
            $pdo->beginTransaction();
            try {
                // تحديث حالة طلب الدفع
                $stmt = $pdo->prepare("
                    UPDATE payment_requests 
                    SET status = 'approved', admin_notes = ?, processed_by = ?, processed_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$admin_notes, $current_user_id, $request_id]);

                // جلب تفاصيل طلب الدفع
                $stmt = $pdo->prepare("
                    SELECT pr.*, ip.id as plan_id, p.id as payment_id, ip.pricing_plan_id
                    FROM payment_requests pr
                    LEFT JOIN installment_plans ip ON pr.installment_plan_id = ip.id
                    LEFT JOIN payments p ON pr.payment_id = p.id
                    WHERE pr.id = ?
                ");
                $stmt->execute([$request_id]);
                $request = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($request) {
                    if ($request['payment_type'] === 'installment' && $request['payment_id']) {
                        // تحديث حالة القسط إلى مدفوع
                        $stmt = $pdo->prepare("UPDATE payments SET status = 'paid', paid_at = NOW() WHERE id = ?");
                        $stmt->execute([$request['payment_id']]);

                        // تحديث عدد الأقساط المدفوعة في خطة التقسيط
                        $stmt = $pdo->prepare("
                            UPDATE installment_plans
                            SET paid_installments = paid_installments + 1
                            WHERE id = ?
                        ");
                        $stmt->execute([$request['installment_plan_id']]);
                    }
                    elseif ($request['payment_type'] === 'down_payment') {
                        // تحديث حالة خطة التقسيط لتفعيلها
                        $stmt = $pdo->prepare("
                            UPDATE installment_plans
                            SET status = 'active'
                            WHERE id = ?
                        ");
                        $stmt->execute([$request['installment_plan_id']]);
                    }
                    elseif ($request['payment_type'] === 'service_purchase') {
                        // تحديث حالة الشراء إلى مكتمل
                        $stmt = $pdo->prepare("
                            UPDATE purchases
                            SET status = 'completed', completion_date = CURDATE()
                            WHERE user_id = ? AND pricing_plan_id = ? AND status = 'active'
                            ORDER BY created_at DESC LIMIT 1
                        ");
                        $stmt->execute([$request['user_id'], $request['pricing_plan_id'] ?? 0]);
                    }
                }

                $pdo->commit();
                $success_message = 'تم الموافقة على طلب الدفع بنجاح';
            } catch (Exception $e) {
                $pdo->rollBack();
                throw $e;
            }
        }
        elseif ($action === 'reject') {
            $stmt = $pdo->prepare("
                UPDATE payment_requests 
                SET status = 'rejected', admin_notes = ?, processed_by = ?, processed_at = NOW()
                WHERE id = ?
            ");
            
            if ($stmt->execute([$admin_notes, $current_user_id, $request_id])) {
                $success_message = 'تم رفض طلب الدفع';
            } else {
                $error_message = 'حدث خطأ أثناء رفض طلب الدفع';
            }
        }
    }

    // فلترة النتائج
    $status_filter = $_GET['status'] ?? '';
    $where_clause = '';
    $params = [];

    if ($status_filter && in_array($status_filter, ['pending', 'approved', 'rejected'])) {
        $where_clause = 'WHERE pr.status = ?';
        $params[] = $status_filter;
    }

    // جلب طلبات الدفع
    $stmt = $pdo->prepare("
        SELECT pr.*, u.name as user_name, u.phone as user_phone,
               pg.name as gateway_name, pg.image as gateway_image,
               ip.id as plan_id, pp.name as plan_name, s.name as service_name,
               admin.name as processed_by_name
        FROM payment_requests pr
        JOIN users u ON pr.user_id = u.id
        JOIN payment_gateways pg ON pr.gateway_id = pg.id
        LEFT JOIN installment_plans ip ON pr.installment_plan_id = ip.id
        LEFT JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
        LEFT JOIN services s ON pp.service_id = s.id
        LEFT JOIN users admin ON pr.processed_by = admin.id
        $where_clause
        ORDER BY pr.created_at DESC
    ");
    $stmt->execute($params);
    $payment_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات سريعة
    $stats = [];
    $stmt = $pdo->prepare("SELECT status, COUNT(*) as count FROM payment_requests GROUP BY status");
    $stmt->execute();
    $status_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($status_counts as $stat) {
        $stats[$stat['status']] = $stat['count'];
    }

} catch (Exception $e) {
    error_log("Error in payment_requests.php: " . $e->getMessage());
    $error_message = 'حدث خطأ في النظام';
    $payment_requests = []; // تهيئة المتغير في حالة الخطأ
    $stats = ['pending' => 0, 'approved' => 0, 'rejected' => 0]; // تهيئة الإحصائيات
    $status_filter = $_GET['status'] ?? ''; // تهيئة متغير الفلترة
}

include __DIR__ . '/../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="admin-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>طلبات الدفع</h2>
                </div>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?= $success_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?= $error_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?= $stats['pending'] ?? 0 ?></h5>
                                <p class="card-text">في الانتظار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?= $stats['approved'] ?? 0 ?></h5>
                                <p class="card-text">موافق عليها</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger"><?= $stats['rejected'] ?? 0 ?></h5>
                                <p class="card-text">مرفوضة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info"><?= array_sum($stats) ?></h5>
                                <p class="card-text">إجمالي الطلبات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="status" class="form-label">حالة الطلب</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>في الانتظار</option>
                                    <option value="approved" <?= $status_filter === 'approved' ? 'selected' : '' ?>>موافق عليها</option>
                                    <option value="rejected" <?= $status_filter === 'rejected' ? 'selected' : '' ?>>مرفوضة</option>
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">فلترة</button>
                                <a href="payment_requests.php" class="btn btn-outline-secondary">إعادة تعيين</a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول طلبات الدفع -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>نوع الدفع</th>
                                        <th>المبلغ</th>
                                        <th>بوابة الدفع</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($payment_requests as $request): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?= htmlspecialchars($request['user_name']) ?></strong><br>
                                                <small class="text-muted"><?= htmlspecialchars($request['user_phone']) ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $type_labels = [
                                                'installment' => 'قسط',
                                                'down_payment' => 'مقدم',
                                                'service_purchase' => 'شراء خدمة'
                                            ];
                                            echo $type_labels[$request['payment_type']] ?? $request['payment_type'];
                                            ?>
                                            <?php if ($request['service_name']): ?>
                                                <br><small class="text-muted"><?= htmlspecialchars($request['service_name']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?= number_format($request['amount'], 2) ?> جنيه</strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if ($request['gateway_image']): ?>
                                                    <img src="../<?= htmlspecialchars($request['gateway_image']) ?>" 
                                                         alt="<?= htmlspecialchars($request['gateway_name']) ?>"
                                                         style="width: 30px; height: 30px; object-fit: cover; border-radius: 4px; margin-left: 8px;">
                                                <?php endif; ?>
                                                <span><?= htmlspecialchars($request['gateway_name']) ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $status_classes = [
                                                'pending' => 'bg-warning',
                                                'approved' => 'bg-success',
                                                'rejected' => 'bg-danger'
                                            ];
                                            $status_labels = [
                                                'pending' => 'في الانتظار',
                                                'approved' => 'موافق عليه',
                                                'rejected' => 'مرفوض'
                                            ];
                                            ?>
                                            <span class="badge <?= $status_classes[$request['status']] ?>">
                                                <?= $status_labels[$request['status']] ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?= date('Y-m-d H:i', strtotime($request['created_at'])) ?>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    onclick="viewRequest(<?= htmlspecialchars(json_encode($request)) ?>)">
                                                <i class="fas fa-eye"></i> عرض
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal عرض تفاصيل الطلب -->
<div class="modal fade" id="viewRequestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل طلب الدفع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="requestDetails">
                <!-- سيتم ملء المحتوى بواسطة JavaScript -->
            </div>
            <div class="modal-footer" id="requestActions">
                <!-- سيتم ملء الإجراءات بواسطة JavaScript -->
            </div>
        </div>
    </div>
</div>

<script>
function viewRequest(request) {
    const modal = new bootstrap.Modal(document.getElementById('viewRequestModal'));
    
    // تحضير أثباتات الدفع
    let proofImages = '';
    if (request.payment_proofs) {
        try {
            const proofs = JSON.parse(request.payment_proofs);
            proofImages = proofs.map(proof => 
                `<div class="col-md-4 mb-3">
                    <img src="../${proof}" class="img-fluid rounded" style="max-height: 200px; cursor: pointer;" 
                         onclick="window.open('../${proof}', '_blank')">
                </div>`
            ).join('');
        } catch (e) {
            proofImages = '<p class="text-muted">لا توجد أثباتات دفع</p>';
        }
    } else {
        proofImages = '<p class="text-muted">لا توجد أثباتات دفع</p>';
    }

    // ملء تفاصيل الطلب
    document.getElementById('requestDetails').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات العميل</h6>
                <p><strong>الاسم:</strong> ${request.user_name}</p>
                <p><strong>رقم الهاتف:</strong> ${request.user_phone}</p>
            </div>
            <div class="col-md-6">
                <h6>معلومات الدفع</h6>
                <p><strong>المبلغ:</strong> ${parseFloat(request.amount).toLocaleString()} جنيه</p>
                <p><strong>نوع الدفع:</strong> ${getPaymentTypeLabel(request.payment_type)}</p>
                <p><strong>بوابة الدفع:</strong> ${request.gateway_name}</p>
                <p><strong>تاريخ الطلب:</strong> ${new Date(request.created_at).toLocaleString('ar-EG')}</p>
            </div>
        </div>
        
        ${request.service_name ? `<div class="row mt-3"><div class="col-12"><h6>الخدمة</h6><p>${request.service_name}</p></div></div>` : ''}
        
        <div class="row mt-3">
            <div class="col-12">
                <h6>أثباتات الدفع</h6>
                <div class="row">
                    ${proofImages}
                </div>
            </div>
        </div>
        
        ${request.admin_notes ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>ملاحظات الإدارة</h6>
                <p class="alert alert-info">${request.admin_notes}</p>
                <small class="text-muted">تم المعالجة بواسطة: ${request.processed_by_name || 'غير محدد'}</small>
            </div>
        </div>
        ` : ''}
    `;

    // ملء الإجراءات
    let actions = '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>';
    
    if (request.status === 'pending') {
        actions = `
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            <button type="button" class="btn btn-danger" onclick="processRequest(${request.id}, 'reject')">رفض</button>
            <button type="button" class="btn btn-success" onclick="processRequest(${request.id}, 'approve')">موافقة</button>
        `;
    }
    
    document.getElementById('requestActions').innerHTML = actions;
    
    modal.show();
}

function getPaymentTypeLabel(type) {
    const labels = {
        'installment': 'قسط',
        'down_payment': 'مقدم',
        'service_purchase': 'شراء خدمة'
    };
    return labels[type] || type;
}

function processRequest(requestId, action) {
    const actionText = action === 'approve' ? 'الموافقة على' : 'رفض';
    const notes = prompt(`ملاحظات ${actionText} الطلب (اختياري):`);
    
    if (notes !== null) { // لم يتم إلغاء الحوار
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="${action}">
            <input type="hidden" name="request_id" value="${requestId}">
            <input type="hidden" name="admin_notes" value="${notes || ''}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>
