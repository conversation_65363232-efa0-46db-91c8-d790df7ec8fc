            </div>
        </div>
    </div>

    <!-- Scrip<PERSON> -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/intlTelInput.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.all.min.js"></script>
    
    <!-- Dark Mode JS -->
    <script src="../assets/js/dark-mode.js"></script>

    <script>
        // تهيئة DataTables
        $(document).ready(function() {
            $('.data-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']]
            });
            
            // تهيئة Select2
            $('.select2').each(function() {
                if (!$(this).hasClass('select2-hidden-accessible')) {
                    $(this).select2({
                        theme: 'bootstrap-5',
                        width: '100%'
                    });
                }
            });
            
            // تهيئة tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
        
        // دالة لتأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return Swal.fire({
                title: 'تأكيد الحذف',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء'
            });
        }
        
        // دالة لعرض رسائل النجاح
        function showSuccess(message) {
            Swal.fire({
                title: 'تم بنجاح!',
                text: message,
                icon: 'success',
                confirmButtonText: 'موافق'
            });
        }
        
        // دالة لعرض رسائل الخطأ
        function showError(message) {
            Swal.fire({
                title: 'خطأ!',
                text: message,
                icon: 'error',
                confirmButtonText: 'موافق'
            });
        }
        
        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-EG', {
                style: 'currency',
                currency: 'EGP'
            }).format(amount);
        }
        
        // تنسيق التاريخ
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('ar-EG');
        }
        
        // دالة لحساب الوقت المنقضي
        function timeAgo(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) {
                return 'منذ لحظات';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `منذ ${minutes} دقيقة`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `منذ ${hours} ساعة`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `منذ ${days} يوم`;
            }
        }
        
        // تبديل الشريط الجانبي في الهواتف المحمولة
        function toggleSidebar() {
            const sidebar = document.querySelector('.admin-sidebar');
            sidebar.classList.toggle('show');
        }
        
        // إغلاق الشريط الجانبي عند النقر خارجه
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.admin-sidebar');
            const toggleButton = document.querySelector('.navbar-toggler');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !toggleButton.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });
    </script>
</body>
</html>

