<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/db_config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$user_id = getCurrentUserId();
$page_title = 'طلبات الدفع';

try {
    $db = new Database();
    $pdo = $db->getConnection();

    // جلب جميع طلبات الدفع للمستخدم - استعلام مبسط أولاً
    $stmt = $pdo->prepare("
        SELECT pr.*, pg.name as gateway_name, pg.image as gateway_image,
               CASE
                   WHEN pr.payment_type = 'installment' THEN 'دفع قسط'
                   WHEN pr.payment_type = 'down_payment' THEN 'دفع مقدم'
                   WHEN pr.payment_type = 'service_purchase' THEN 'شراء خدمة'
                   ELSE pr.payment_type
               END as payment_type_label
        FROM payment_requests pr
        LEFT JOIN payment_gateways pg ON pr.gateway_id = pg.id
        WHERE pr.user_id = ?
        ORDER BY pr.created_at DESC
    ");
    $stmt->execute([$user_id]);
    $payment_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إضافة معلومات إضافية لكل طلب
    foreach ($payment_requests as &$request) {
        $request['plan_name'] = '';
        $request['service_name'] = '';

        // محاولة جلب معلومات الخطة إذا كان هناك installment_plan_id
        if (!empty($request['installment_plan_id'])) {
            try {
                $plan_stmt = $pdo->prepare("
                    SELECT pp.name as plan_name, s.name as service_name
                    FROM installment_plans ip
                    LEFT JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
                    LEFT JOIN services s ON pp.service_id = s.id
                    WHERE ip.id = ?
                ");
                $plan_stmt->execute([$request['installment_plan_id']]);
                $plan_info = $plan_stmt->fetch(PDO::FETCH_ASSOC);

                if ($plan_info) {
                    $request['plan_name'] = $plan_info['plan_name'] ?? '';
                    $request['service_name'] = $plan_info['service_name'] ?? '';
                }
            } catch (Exception $e) {
                // تجاهل الخطأ واستمر
                error_log("Error fetching plan info: " . $e->getMessage());
            }
        }
    }
    
    // إحصائيات سريعة
    $stats = [];
    $stmt = $pdo->prepare("
        SELECT status, COUNT(*) as count 
        FROM payment_requests 
        WHERE user_id = ? 
        GROUP BY status
    ");
    $stmt->execute([$user_id]);
    $status_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($status_counts as $stat) {
        $stats[$stat['status']] = $stat['count'];
    }
    
} catch (Exception $e) {
    error_log("Error in my_payment_requests.php: " . $e->getMessage());
    $payment_requests = [];
    $stats = ['pending' => 0, 'approved' => 0, 'rejected' => 0];
}

include __DIR__ . '/includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="page-header mb-4">
                <h1 class="page-title">
                    <i class="fas fa-receipt me-3"></i>
                    طلبات الدفع
                </h1>
                <p class="page-subtitle">تتبع حالة جميع طلبات الدفع التي قمت بإرسالها</p>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon">
                            <i class="fas fa-clock text-warning"></i>
                        </div>
                        <div class="stat-number"><?= $stats['pending'] ?? 0 ?></div>
                        <div class="stat-label">في الانتظار</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                        <div class="stat-number"><?= $stats['approved'] ?? 0 ?></div>
                        <div class="stat-label">موافق عليها</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon">
                            <i class="fas fa-times-circle text-danger"></i>
                        </div>
                        <div class="stat-number"><?= $stats['rejected'] ?? 0 ?></div>
                        <div class="stat-label">مرفوضة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon">
                            <i class="fas fa-list text-info"></i>
                        </div>
                        <div class="stat-number"><?= array_sum($stats) ?></div>
                        <div class="stat-label">إجمالي الطلبات</div>
                    </div>
                </div>
            </div>
            
            <!-- قائمة طلبات الدفع -->
            <?php if (empty($payment_requests)): ?>
                <div class="empty-state text-center py-5">
                    <div class="empty-icon">
                        <i class="fas fa-file-invoice fa-4x text-muted"></i>
                    </div>
                    <h3 class="empty-title">لا توجد طلبات دفع</h3>
                    <p class="empty-description">لم تقم بإرسال أي طلبات دفع بعد. ابدأ بشراء خدمة أو دفع قسط.</p>
                    <div class="empty-actions">
                        <a href="services.php" class="btn btn-primary me-2">
                            <i class="fas fa-shopping-cart me-2"></i>تصفح الخدمات
                        </a>
                        <a href="my_installments.php" class="btn btn-outline-primary">
                            <i class="fas fa-credit-card me-2"></i>أقساطي
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="payment-requests-table">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>نوع الدفع</th>
                                            <th>المبلغ</th>
                                            <th>بوابة الدفع</th>
                                            <th>الخدمة</th>
                                            <th>تاريخ الطلب</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payment_requests as $request): ?>
                                        <tr>
                                            <td>
                                                <div class="payment-type">
                                                    <i class="fas fa-credit-card me-2"></i>
                                                    <?= htmlspecialchars($request['payment_type_label']) ?>
                                                </div>
                                            </td>
                                            <td>
                                                <strong class="amount"><?= number_format($request['amount'], 2) ?> جنيه</strong>
                                            </td>
                                            <td>
                                                <div class="gateway-info">
                                                    <?php if ($request['gateway_image']): ?>
                                                        <img src="<?= htmlspecialchars($request['gateway_image']) ?>" 
                                                             alt="<?= htmlspecialchars($request['gateway_name']) ?>"
                                                             class="gateway-icon me-2">
                                                    <?php endif; ?>
                                                    <?= htmlspecialchars($request['gateway_name']) ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($request['service_name']): ?>
                                                    <div class="service-info">
                                                        <strong><?= htmlspecialchars($request['service_name']) ?></strong>
                                                        <?php if ($request['plan_name']): ?>
                                                            <br><small class="text-muted"><?= htmlspecialchars($request['plan_name']) ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="request-date">
                                                    <?= date('Y/m/d', strtotime($request['created_at'])) ?>
                                                    <br><small class="text-muted"><?= date('H:i', strtotime($request['created_at'])) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'pending' => 'bg-warning',
                                                    'approved' => 'bg-success',
                                                    'rejected' => 'bg-danger'
                                                ];
                                                $status_labels = [
                                                    'pending' => 'في الانتظار',
                                                    'approved' => 'موافق عليه',
                                                    'rejected' => 'مرفوض'
                                                ];
                                                ?>
                                                <span class="badge <?= $status_classes[$request['status']] ?>">
                                                    <?= $status_labels[$request['status']] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-info" 
                                                        onclick="viewRequestDetails(<?= htmlspecialchars(json_encode($request)) ?>)">
                                                    <i class="fas fa-eye"></i> عرض
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal عرض تفاصيل الطلب -->
<div class="modal fade" id="requestDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل طلب الدفع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="requestDetailsContent">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
    margin-bottom: 1rem;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
}

.payment-type {
    font-weight: 600;
    color: #2c3e50;
}

.amount {
    color: #667eea;
    font-size: 1.1rem;
}

.gateway-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.service-info strong {
    color: #2c3e50;
}

.request-date {
    font-size: 0.9rem;
}

.empty-state {
    background: white;
    border-radius: 15px;
    padding: 3rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.empty-icon {
    margin-bottom: 2rem;
}

.empty-title {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.empty-description {
    color: #6c757d;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}
</style>

<script>
function viewRequestDetails(request) {
    const modal = new bootstrap.Modal(document.getElementById('requestDetailsModal'));
    
    // تحضير أثباتات الدفع
    let proofImages = '';
    if (request.payment_proofs) {
        try {
            const proofs = JSON.parse(request.payment_proofs);
            proofImages = proofs.map(proof => 
                `<div class="col-md-4 mb-3">
                    <img src="${proof}" class="img-fluid rounded" style="max-height: 200px; cursor: pointer;" 
                         onclick="window.open('${proof}', '_blank')" alt="إثبات الدفع">
                </div>`
            ).join('');
        } catch (e) {
            proofImages = '<p class="text-muted">لا توجد أثباتات دفع</p>';
        }
    } else {
        proofImages = '<p class="text-muted">لا توجد أثباتات دفع</p>';
    }

    // ملء تفاصيل الطلب
    document.getElementById('requestDetailsContent').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات الدفع</h6>
                <p><strong>نوع الدفع:</strong> ${request.payment_type_label}</p>
                <p><strong>المبلغ:</strong> ${parseFloat(request.amount).toLocaleString()} جنيه</p>
                <p><strong>بوابة الدفع:</strong> ${request.gateway_name}</p>
                <p><strong>تاريخ الطلب:</strong> ${new Date(request.created_at).toLocaleString('ar-EG')}</p>
            </div>
            <div class="col-md-6">
                <h6>الحالة</h6>
                <p><span class="badge bg-${getStatusClass(request.status)}">${getStatusLabel(request.status)}</span></p>
                ${request.admin_notes ? `<p><strong>ملاحظات الإدارة:</strong><br>${request.admin_notes}</p>` : ''}
                ${request.processed_at ? `<p><strong>تاريخ المعالجة:</strong> ${new Date(request.processed_at).toLocaleString('ar-EG')}</p>` : ''}
            </div>
        </div>
        
        ${request.service_name ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>الخدمة</h6>
                    <p><strong>${request.service_name}</strong></p>
                    ${request.plan_name ? `<p class="text-muted">${request.plan_name}</p>` : ''}
                </div>
            </div>
        ` : ''}
        
        <div class="row mt-3">
            <div class="col-12">
                <h6>أثباتات الدفع</h6>
                <div class="row">
                    ${proofImages}
                </div>
            </div>
        </div>
    `;
    
    modal.show();
}

function getStatusClass(status) {
    const classes = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger'
    };
    return classes[status] || 'secondary';
}

function getStatusLabel(status) {
    const labels = {
        'pending': 'في الانتظار',
        'approved': 'موافق عليه',
        'rejected': 'مرفوض'
    };
    return labels[status] || status;
}
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
