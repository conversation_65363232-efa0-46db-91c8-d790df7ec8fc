<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

try {
    if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
        throw new Exception('معرف الخطة مطلوب');
    }
    
    $plan_id = (int)$_GET['id'];
    
    $db = new Database();
    $pdo = $db->getConnection();
    
    // التحقق من وجود حقل is_active في الجداول
    $stmt = $pdo->prepare("SHOW COLUMNS FROM pricing_plans LIKE 'is_active'");
    $stmt->execute();
    $pp_is_active_exists = $stmt->fetch();

    $stmt = $pdo->prepare("SHOW COLUMNS FROM services LIKE 'is_active'");
    $stmt->execute();
    $s_is_active_exists = $stmt->fetch();

    // جلب تفاصيل الخطة مع اسم الخدمة
    $where_conditions = ["pp.id = ?"];
    if ($pp_is_active_exists) {
        $where_conditions[] = "pp.is_active = TRUE";
    }
    if ($s_is_active_exists) {
        $where_conditions[] = "s.is_active = TRUE";
    }

    $sql = "
        SELECT pp.*, s.name as service_name
        FROM pricing_plans pp
        JOIN services s ON pp.service_id = s.id
        WHERE " . implode(" AND ", $where_conditions) . "
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$plan_id]);
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$plan) {
        throw new Exception('الخطة غير موجودة');
    }
    
    echo json_encode([
        'success' => true,
        'plan' => $plan
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

