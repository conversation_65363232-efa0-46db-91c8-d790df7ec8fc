<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$plan_id = (int)($_GET['id'] ?? 0);

if (!$plan_id) {
    echo json_encode(['success' => false, 'message' => 'معرف خطة السعر مطلوب']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // جلب بيانات خطة السعر
    $stmt = $pdo->prepare("
        SELECT pp.*, s.name as service_name
        FROM pricing_plans pp
        JOIN services s ON pp.service_id = s.id
        WHERE pp.id = ?
    ");
    $stmt->execute([$plan_id]);
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$plan) {
        echo json_encode(['success' => false, 'message' => 'خطة السعر غير موجودة']);
        exit();
    }
    
    // جلب خطط التقسيط المرتبطة
    $stmt = $pdo->prepare("
        SELECT ip.*, u.name as user_name
        FROM installment_plans ip
        JOIN users u ON ip.user_id = u.id
        WHERE ip.pricing_plan_id = ?
        ORDER BY ip.created_at DESC
    ");
    $stmt->execute([$plan_id]);
    $installment_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'plan' => $plan,
        'installment_plans' => $installment_plans
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>

