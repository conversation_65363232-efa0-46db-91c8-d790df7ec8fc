<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/db_config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/functions.php';

requireLogin();

try {
    $db = new Database();
    $pdo = $db->getConnection();
    $user_id = $_SESSION['user_id'];
    
    // جلب معلومات المستخدم
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // جلب جميع خطط التقسيط للمستخدم
    $stmt = $pdo->prepare("
        SELECT ip.*, pp.name as plan_name, s.name as service_name,
               (SELECT COUNT(*) FROM payments WHERE installment_plan_id = ip.id AND status = 'paid') as paid_count,
               (SELECT COUNT(*) FROM payments WHERE installment_plan_id = ip.id) as total_count,
               ((ip.down_payment + (ip.paid_installments * ip.installment_amount)) / ip.total_amount * 100) as progress_percentage
        FROM installment_plans ip
        JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
        JOIN services s ON pp.service_id = s.id
        WHERE ip.user_id = ?
        ORDER BY ip.created_at DESC
    ");
    $stmt->execute([$user_id]);
    $installment_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب طلبات الدفع للمستخدم
    $stmt = $pdo->prepare("
        SELECT pr.*, pg.name as gateway_name,
               CASE
                   WHEN pr.payment_type = 'installment' THEN 'دفع قسط'
                   WHEN pr.payment_type = 'down_payment' THEN 'دفع مقدم'
                   WHEN pr.payment_type = 'service_purchase' THEN 'شراء خدمة'
                   ELSE pr.payment_type
               END as payment_type_label
        FROM payment_requests pr
        JOIN payment_gateways pg ON pr.gateway_id = pg.id
        WHERE pr.user_id = ?
        ORDER BY pr.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$user_id]);
    $payment_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب الخدمات المشتراة (من جدول purchases أو purchased_services)
    $purchased_services = [];
    $purchases = [];
    try {
        // محاولة جلب من جدول purchases أولاً
        $stmt = $pdo->prepare("
            SELECT p.*, pp.name as plan_name, s.name as service_name, s.image as service_image
            FROM purchases p
            JOIN pricing_plans pp ON p.pricing_plan_id = pp.id
            JOIN services s ON pp.service_id = s.id
            WHERE p.user_id = ?
            ORDER BY p.created_at DESC
        ");
        $stmt->execute([$user_id]);
        $purchases = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $purchased_services = $purchases; // نسخ للتوافق
    } catch (Exception $e) {
        try {
            // محاولة جلب من جدول purchased_services كبديل
            $stmt = $pdo->prepare("
                SELECT ps.*, pp.name as plan_name, s.name as service_name, s.image as service_image
                FROM purchased_services ps
                JOIN pricing_plans pp ON ps.pricing_plan_id = pp.id
                JOIN services s ON ps.service_id = s.id
                WHERE ps.user_id = ?
                ORDER BY ps.created_at DESC
            ");
            $stmt->execute([$user_id]);
            $purchased_services = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $purchases = $purchased_services; // نسخ للتوافق
        } catch (Exception $e2) {
            // كلا الجدولين غير موجود، استخدم خطط التقسيط كبديل
            $purchased_services = $installment_plans;
            $purchases = $installment_plans;
        }
    }

    // حساب الإحصائيات
    $total_amount = 0;
    $paid_amount = 0;
    $active_plans = 0;
    $completed_plans = 0;
    $overdue_payments = 0;

    foreach ($installment_plans as $plan) {
        $total_amount += $plan['total_amount'];
        // إضافة المقدم إلى المبلغ المدفوع + الأقساط المدفوعة
        $paid_amount += $plan['down_payment'] + ($plan['paid_count'] * $plan['installment_amount']);

        if ($plan['status'] === 'active') {
            $active_plans++;
        } elseif ($plan['status'] === 'completed') {
            $completed_plans++;
        }

        // حساب الأقساط المتأخرة
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as overdue_count
            FROM payments
            WHERE installment_plan_id = ? AND status = 'overdue'
        ");
        $stmt->execute([$plan['id']]);
        $overdue_result = $stmt->fetch(PDO::FETCH_ASSOC);
        $overdue_payments += $overdue_result['overdue_count'];
    }

    // إحصائيات سريعة
    $stats = [
        'total_plans' => count($installment_plans),
        'active_plans' => $active_plans,
        'completed_plans' => $completed_plans,
        'total_amount' => $total_amount,
        'paid_amount' => $paid_amount,
        'remaining_amount' => $total_amount - $paid_amount,
        'overdue_payments' => $overdue_payments,
        'pending_amount' => 0  // تعريف المتغير أولاً
    ];

    // حساب المبلغ المتبقي من الأقساط
    foreach ($installment_plans as $plan) {
        $remaining_installments = $plan['installments_count'] - $plan['paid_installments'];
        $stats['pending_amount'] += $remaining_installments * $plan['installment_amount'];
    }
    
} catch (Exception $e) {
    $user = null;
    $installment_plans = [];
    $purchases = [];
    $stats = [
        'total_plans' => 0,
        'active_plans' => 0,
        'completed_plans' => 0,
        'total_amount' => 0,
        'paid_amount' => 0,
        'remaining_amount' => 0,
        'overdue_payments' => 0,
        'pending_amount' => 0
    ];
}

$page_title = 'لوحة التحكم';
include __DIR__ . '/includes/header.php';
?>

<div class="dashboard-page">
    <!-- Welcome Section -->
    <div class="welcome-section mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-title">مرحباً، <?= htmlspecialchars($user['name'] ?? 'المستخدم') ?></h1>
                <p class="welcome-subtitle">إليك ملخص حسابك وخطط التقسيط الخاصة بك</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="user-avatar">
                    <i class="fas fa-user-circle fa-4x text-primary"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-section mb-4">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= $stats['total_plans'] ?></h3>
                        <p class="stat-label">إجمالي خطط التقسيط</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= $stats['active_plans'] ?></h3>
                        <p class="stat-label">خطط نشطة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['paid_amount'], 2) ?></h3>
                        <p class="stat-label">المبلغ المدفوع (جنيه)</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?= number_format($stats['remaining_amount'], 2) ?></h3>
                        <p class="stat-label">المبلغ المتبقي (جنيه)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Installment Plans Section -->
    <?php if (!empty($installment_plans)): ?>
    <div class="installment-section mb-4">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-credit-card me-2"></i>
                خطط التقسيط النشطة
            </h2>
        </div>
        
        <div class="row">
            <?php foreach ($installment_plans as $plan): ?>
            <div class="col-lg-6 mb-3">
                <div class="installment-card">
                    <div class="installment-header">
                        <h5 class="installment-title"><?= htmlspecialchars($plan['service_name']) ?></h5>
                        <span class="installment-plan"><?= htmlspecialchars($plan['plan_name']) ?></span>
                    </div>
                    
                    <div class="installment-progress">
                        <div class="progress-info">
                            <?php
                            $paid_total = $plan['down_payment'] + ($plan['paid_installments'] * $plan['installment_amount']);
                            ?>
                            <span>المدفوع: <?= number_format($paid_total, 2) ?> من <?= number_format($plan['total_amount'], 2) ?> جنيه</span>
                            <span><?= number_format($plan['progress_percentage'], 1) ?>%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: <?= $plan['progress_percentage'] ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="installment-details">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">القسط الشهري</small>
                                <div class="detail-value"><?= number_format($plan['installment_amount'], 2) ?> جنيه</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">المبلغ المتبقي</small>
                                <div class="detail-value">
                                    <?php
                                    // المبلغ المتبقي = إجمالي المبلغ - (المقدم + الأقساط المدفوعة)
                                    $remaining = $plan['total_amount'] - ($plan['down_payment'] + ($plan['paid_installments'] * $plan['installment_amount']));
                                    echo number_format($remaining, 2);
                                    ?> جنيه
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="installment-actions">
                        <button class="btn btn-primary btn-sm" onclick="viewInstallmentDetails(<?= $plan['id'] ?>)">
                            <i class="fas fa-eye me-1"></i>
                            عرض التفاصيل
                        </button>
                        <?php if ($plan['status'] === 'active' && ($plan['installments_count'] - $plan['paid_installments']) > 0): ?>
                        <button class="btn btn-success btn-sm" onclick="openPaymentModal(<?= $plan['installment_amount'] ?>, 'installment', <?= $plan['id'] ?>)">
                            <i class="fas fa-credit-card me-1"></i>
                            دفع قسط
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Purchases Section -->
    <div class="purchases-section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-shopping-bag me-2"></i>
                مشترياتي
            </h2>
        </div>
        
        <?php if (empty($purchases)): ?>
            <div class="empty-state text-center py-5">
                <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مشتريات بعد</h4>
                <p class="text-muted">ابدأ بتصفح خدماتنا واختر ما يناسبك</p>
                <a href="services.php" class="btn btn-primary">تصفح الخدمات</a>
            </div>
        <?php else: ?>
            <div class="purchases-grid">
                <?php foreach ($purchases as $purchase): ?>
                <div class="purchase-card">
                    <div class="purchase-image">
                        <?php if ($purchase['service_image']): ?>
                            <img src="assets/uploads/<?= htmlspecialchars($purchase['service_image']) ?>" 
                                 alt="<?= htmlspecialchars($purchase['service_name']) ?>">
                        <?php else: ?>
                            <div class="purchase-placeholder">
                                <i class="fas fa-cogs fa-2x"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="purchase-content">
                        <h6 class="purchase-title"><?= htmlspecialchars($purchase['service_name']) ?></h6>
                        <p class="purchase-plan"><?= htmlspecialchars($purchase['plan_name']) ?></p>
                        
                        <div class="purchase-details">
                            <div class="detail-row">
                                <span class="detail-label">المبلغ:</span>
                                <span class="detail-value"><?= number_format($purchase['total_amount'], 2) ?> جنيه</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">نوع الدفع:</span>
                                <span class="detail-value">
                                    <?= $purchase['payment_type'] === 'cash' ? 'نقداً' : 'تقسيط' ?>
                                </span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">تاريخ الشراء:</span>
                                <span class="detail-value">
                                    <?= date('Y/m/d', strtotime($purchase['created_at'])) ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="purchase-status">
                            <span class="badge bg-<?= $purchase['status'] === 'completed' ? 'success' : 'warning' ?>">
                                <?= $purchase['status'] === 'completed' ? 'مكتمل' : 'معلق' ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Payment Requests Section -->
<div class="payment-requests-section">
    <div class="section-header">
        <h2 class="section-title">
            <i class="fas fa-receipt me-2"></i>
            طلبات الدفع الأخيرة
        </h2>
        <a href="my_payment_requests.php" class="btn btn-outline-primary">عرض الكل</a>
    </div>

    <?php if (empty($payment_requests)): ?>
        <div class="empty-state text-center py-4">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد طلبات دفع</h5>
            <p class="text-muted">ستظهر هنا طلبات الدفع التي قمت بإرسالها</p>
        </div>
    <?php else: ?>
        <div class="payment-requests-list">
            <?php foreach ($payment_requests as $request): ?>
            <div class="payment-request-card">
                <div class="request-info">
                    <div class="request-type">
                        <i class="fas fa-credit-card me-2"></i>
                        <?= htmlspecialchars($request['payment_type_label']) ?>
                    </div>
                    <div class="request-amount">
                        <?= number_format($request['amount'], 2) ?> جنيه
                    </div>
                </div>

                <div class="request-details">
                    <div class="request-gateway">
                        <small class="text-muted">بوابة الدفع:</small>
                        <?= htmlspecialchars($request['gateway_name']) ?>
                    </div>
                    <div class="request-date">
                        <small class="text-muted">تاريخ الطلب:</small>
                        <?= date('Y/m/d H:i', strtotime($request['created_at'])) ?>
                    </div>
                </div>

                <div class="request-status">
                    <?php
                    $status_classes = [
                        'pending' => 'bg-warning',
                        'approved' => 'bg-success',
                        'rejected' => 'bg-danger'
                    ];
                    $status_labels = [
                        'pending' => 'في الانتظار',
                        'approved' => 'موافق عليه',
                        'rejected' => 'مرفوض'
                    ];
                    ?>
                    <span class="badge <?= $status_classes[$request['status']] ?>">
                        <?= $status_labels[$request['status']] ?>
                    </span>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Installment Details Modal -->
<div class="modal fade" id="installmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل خطة التقسيط</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="installmentModalBody">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<style>
.dashboard-page {
    padding: 20px 0;
    background-color: #121212;
    color: #e0e0e0;
    min-height: 100vh;
}

/* Force dark theme for all dashboard elements */
.dashboard-page * {
    color: #e0e0e0;
}

.dashboard-page h1, .dashboard-page h2, .dashboard-page h3,
.dashboard-page h4, .dashboard-page h5, .dashboard-page h6 {
    color: #e0e0e0 !important;
}

.dashboard-page p, .dashboard-page span, .dashboard-page div {
    color: #e0e0e0 !important;
}

.dashboard-page .text-muted {
    color: #b0b0b0 !important;
}

.welcome-section {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.user-avatar {
    text-align: center;
}

.stat-card {
    background: #1e1e1e;
    border: 1px solid #333333;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
    color: #e0e0e0;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.5);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
}

.stat-icon i {
    font-size: 1.5rem;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #6c7ce7;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #b0b0b0;
    margin: 0;
    font-size: 0.9rem;
}

.section-header {
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #e0e0e0;
    margin: 0;
}

.installment-card {
    background: #1e1e1e;
    border: 1px solid #333333;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    height: 100%;
    color: #e0e0e0;
}

.installment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.5);
}

.installment-header {
    margin-bottom: 1rem;
}

.installment-title {
    font-weight: 600;
    color: #e0e0e0;
    margin-bottom: 0.25rem;
}

.installment-plan {
    color: #b0b0b0;
    font-size: 0.9rem;
}

.installment-progress {
    margin-bottom: 1rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #b0b0b0;
}

.progress {
    height: 8px;
    border-radius: 10px;
    background: #333333;
}

.progress-bar {
    background: linear-gradient(135deg, #6c7ce7 0%, #8b5fbf 100%);
    border-radius: 10px;
}

.installment-details {
    margin-bottom: 1rem;
}

.detail-value {
    font-weight: 600;
    color: #e0e0e0;
    font-size: 1.1rem;
}

.installment-actions {
    display: flex;
    gap: 0.5rem;
}

.installment-actions .btn {
    flex: 1;
    border-radius: 8px;
    font-size: 0.9rem;
}

.purchases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.purchase-card {
    background: #1e1e1e;
    border: 1px solid #333333;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    color: #e0e0e0;
}

.purchase-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.5);
}

.purchase-image {
    height: 150px;
    overflow: hidden;
    position: relative;
}

.purchase-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.purchase-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
}

.purchase-content {
    padding: 1.5rem;
}

.purchase-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.purchase-plan {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.purchase-details {
    margin-bottom: 1rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.detail-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.detail-row .detail-value {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
}

.purchase-status {
    text-align: center;
}

.empty-state {
    background: white;
    border-radius: 15px;
    padding: 3rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

/* Payment Requests Section */
.payment-requests-section {
    margin-bottom: 2rem;
}

.payment-requests-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.payment-request-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.payment-request-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.12);
}

.request-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.request-type {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
}

.request-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: #667eea;
}

.request-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
}

.request-gateway,
.request-date {
    font-size: 0.9rem;
    color: #6c757d;
}

.request-status .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

@media (max-width: 768px) {
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .installment-actions {
        flex-direction: column;
    }
    
    .purchases-grid {
        grid-template-columns: 1fr;
    }

    .payment-request-card {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .request-details {
        align-self: stretch;
        text-align: left;
    }

    .request-status {
        align-self: stretch;
        text-align: center;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.all.min.js"></script>
<script>
function viewInstallmentDetails(planId) {
    const modal = new bootstrap.Modal(document.getElementById('installmentModal'));
    const modalBody = document.getElementById('installmentModalBody');
    
    // إظهار loader
    modalBody.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // جلب تفاصيل خطة التقسيط
    fetch(`api/get_installment_details.php?id=${planId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayInstallmentDetails(data.plan, data.payments);
            } else {
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        حدث خطأ في تحميل التفاصيل
                    </div>
                `;
            }
        })
        .catch(error => {
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ في الاتصال بالخادم
                </div>
            `;
        });
}

function displayInstallmentDetails(plan, payments) {
    const modalBody = document.getElementById('installmentModalBody');
    
    let paymentsHtml = '';
    payments.forEach(payment => {
        const statusClass = payment.status === 'paid' ? 'success' : 
                           payment.status === 'overdue' ? 'danger' : 'warning';
        const statusText = payment.status === 'paid' ? 'مدفوع' : 
                          payment.status === 'overdue' ? 'متأخر' : 'معلق';
        
        paymentsHtml += `
            <tr>
                <td>القسط ${payment.installment_number}</td>
                <td>${parseFloat(payment.amount).toFixed(2)} جنيه</td>
                <td>${formatDate(payment.due_date)}</td>
                <td>${payment.paid_date ? formatDate(payment.paid_date) : '-'}</td>
                <td>
                    <span class="badge bg-${statusClass}">${statusText}</span>
                </td>
                <td>
                    ${payment.status === 'pending' ? `
                        <button class="btn btn-success btn-sm" onclick="payInstallmentPayment(${payment.id})">
                            <i class="fas fa-credit-card me-1"></i>دفع
                        </button>
                    ` : '-'}
                </td>
            </tr>
        `;
    });
    
    modalBody.innerHTML = `
        <div class="installment-details-content">
            <div class="plan-summary mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الخطة</h6>
                        <p><strong>الخدمة:</strong> ${plan.service_name}</p>
                        <p><strong>الخطة:</strong> ${plan.plan_name}</p>
                        <p><strong>المبلغ الإجمالي:</strong> ${parseFloat(plan.total_amount).toFixed(2)} جنيه</p>
                    </div>
                    <div class="col-md-6">
                        <h6>تفاصيل التقسيط</h6>
                        <p><strong>المقدم:</strong> ${parseFloat(plan.down_payment).toFixed(2)} جنيه</p>
                        <p><strong>عدد الأقساط:</strong> ${plan.installments_count}</p>
                        <p><strong>القسط الشهري:</strong> ${parseFloat(plan.installment_amount).toFixed(2)} جنيه</p>
                    </div>
                </div>
            </div>
            
            <div class="payments-table">
                <h6>جدول الأقساط</h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>القسط</th>
                                <th>المبلغ</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>تاريخ الدفع</th>
                                <th>الحالة</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${paymentsHtml}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

// تم نقل دالة الدفع إلى نظام بوابات الدفع الجديد
function payInstallment(planId) {
    // هذه الدالة محفوظة للتوافق مع الإصدارات القديمة
    // يُفضل استخدام openPaymentModal بدلاً منها
    console.log('استخدم openPaymentModal بدلاً من payInstallment');
        confirmButtonText: 'نعم، ادفع',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // هنا يمكن إضافة منطق الدفع
            Swal.fire({
                title: 'قريباً',
                text: 'سيتم إضافة نظام الدفع الإلكتروني قريباً',
                icon: 'info'
            });
        }
    });
}

function payInstallmentPayment(paymentId) {
    Swal.fire({
        title: 'دفع القسط',
        text: 'هل تريد دفع هذا القسط؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، ادفع',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // هنا يمكن إضافة منطق الدفع
            Swal.fire({
                title: 'قريباً',
                text: 'سيتم إضافة نظام الدفع الإلكتروني قريباً',
                icon: 'info'
            });
        }
    });
}

function formatCurrency(amount) {
    return parseFloat(amount).toFixed(2) + ' جنيه';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-EG');
}
</script>

<?php
// إضافة مكون بوابات الدفع
include __DIR__ . '/includes/payment_gateways_modal.php';
include __DIR__ . '/includes/footer.php';
?>

