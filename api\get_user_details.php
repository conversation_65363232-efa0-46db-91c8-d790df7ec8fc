<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$user_id = (int)($_GET['id'] ?? 0);

if (!$user_id) {
    echo json_encode(['success' => false, 'message' => 'معرف المستخدم مطلوب']);
    exit();
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // جلب بيانات المستخدم
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND is_admin = FALSE");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'المستخدم غير موجود']);
        exit();
    }
    
    // جلب خطط التقسيط
    $stmt = $pdo->prepare("
        SELECT ip.*, pp.name as plan_name, s.name as service_name
        FROM installment_plans ip
        JOIN pricing_plans pp ON ip.pricing_plan_id = pp.id
        JOIN services s ON pp.service_id = s.id
        WHERE ip.user_id = ?
        ORDER BY ip.created_at DESC
    ");
    $stmt->execute([$user_id]);
    $installment_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'user' => $user,
        'installment_plans' => $installment_plans
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>

